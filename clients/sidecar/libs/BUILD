load("@npm//:defs.bzl", "npm_link_all_packages")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@bazel_skylib//rules:build_test.bzl", "build_test")
load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_jest//jest:defs.bzl", "jest_test")

npm_link_all_packages()

SRC_FILES = glob(
    [
        "src/**/*.ts",
        "protos/**/*.ts",
        "protos/**/*.d.ts",
        "protos/**/*.js",
    ],
    exclude = [
        "src/**/__tests__/**",
        "src/**/__mocks__/**",
    ],
)

TEST_FILES = glob([
    "src/**/__tests__/**/*.ts",
    "src/**/__tests__/__fixtures__/*.json",
    "src/**/__tests__/**/__snapshots__/*.snap",
    "src/**/__mocks__/**/*.ts",
])

TEST_DATA = glob([
    "src/**/__tests__/**/test_data/**",
    "src/**/__tests__/**/match_test_data/**",
])

LIB_DEPS = [
    ":node_modules/turndown",
    ":node_modules/zod",
    ":node_modules/winston",
    ":node_modules/@types/node",
    ":node_modules/diff",
    ":node_modules/@types/diff",
    ":node_modules/lru-cache",
]

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = [
        "//clients:__subpackages__",
    ],
    deps = [
        "//clients:tsconfig",
    ],
)

ts_project(
    name = "ts",
    srcs = SRC_FILES + LIB_DEPS,
    # The declaration is needed so type information is accessible by
    # other ts_projects.
    declaration = True,
    out_dir = "out",
    source_map = True,
    tsconfig = ":tsconfig",
    visibility = [
        "//clients:__subpackages__",
    ],
    deps = [
        ":node_modules",
        "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",
    ],
)

# Build test ensures that CI/CD runs the :ts build that checks typing
build_test(
    name = "build_test",
    targets = [":ts"],
)

# This should not be used unless absolutely necessary.
js_library(
    name = "src",
    srcs = SRC_FILES + LIB_DEPS,
    visibility = [
        # Svelte check doesn't seem to use the .d.ts definitions from the :ts
        # target, so it needs to use the source files directly.
        "//clients/common/webviews:__pkg__",

        # Jest & ESlint doesn't seem to use the .d.ts definitions from the :ts
        # target, so it needs to use the source files directly.
        "//clients/vscode:__pkg__",
        "//clients/beachhead:__pkg__",
    ],
    deps = [
        "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",
    ],
)

# Mocks for common interfaces
js_library(
    name = "mocks",
    srcs = glob(["src/__tests__/mocks/**/*.ts"]),
    visibility = [
        "//clients/vscode:__pkg__",
    ],
    deps = [],
)

jest_test(
    name = "test",
    timeout = "moderate",
    config = "jest.config.js",
    data = SRC_FILES + TEST_FILES + TEST_DATA + LIB_DEPS + [
        "babel.config.js",
        ":node_modules",
        ":tsconfig",
        "//base/blob_names/test_data:blob-name-test-data-js",
        "//clients/sidecar/libs/protos:sidecar_libs_ts_protos",  # Need this in the build tree to resolve proto/grpc
    ],
    include_transitive_types = True,  # Needed for type checking
    node_modules = ":node_modules",
)
