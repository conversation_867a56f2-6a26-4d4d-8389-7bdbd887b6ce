import { Exchange } from "../../chat/chat-types";
import { getAPIClient } from "../../client-interfaces/api-client";
import { truncate } from "../../utils/strings";
import { ToolBase, ToolSafety, ToolUseResponse } from "../tool-types";
import { SidecarToolType } from "./sidecar-tool-types";
import { errorToolResponse, successToolResponse } from "./tool-use-response";
import { createRequestId } from "../../utils/request-id";

/**
 * A tool that retrieves information from the codebase.
 */
export class CodebaseRetrievalTool extends ToolBase<SidecarToolType> {
  private readonly _maxRetrievalSize: number = 20000;

  constructor() {
    super(SidecarToolType.codebaseRetrieval, ToolSafety.Safe);
  }

  public readonly description: string = `\
This tool is Augment's context engine, the world's best codebase context engine. It:
1. Takes in a natural language description of the code you are looking for;
2. Uses a proprietary retrieval/embedding model suite that produces the highest-quality recall of relevant code snippets from across the codebase;
3. Maintains a real-time index of the codebase, so the results are always up-to-date and reflects the current state of the codebase;
4. Can retrieve across different programming languages;
5. Only reflects the current state of the codebase on the disk, and has no information on version control or code history.`;

  public readonly inputSchemaJson: string = JSON.stringify({
    /* eslint-disable @typescript-eslint/naming-convention */
    type: "object",
    properties: {
      information_request: {
        type: "string",
        description: "A description of the information you need.",
      },
    },
    required: ["information_request"],
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  public readonly version: number = 2;

  public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
    return true;
  }

  public async call(
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    const toolRequestId = createRequestId();
    try {
      const informationRequest = toolInput.information_request as string;
      const result = await getAPIClient().agentCodebaseRetrieval(
        toolRequestId,
        informationRequest,
        chatHistory,
        this._maxRetrievalSize,
        abortSignal,
      );
      const truncatedResult = truncate(
        result.formattedRetrieval,
        this._maxRetrievalSize,
      );
      return successToolResponse(truncatedResult, toolRequestId);
    } catch (e: unknown) {
      return errorToolResponse(
        `Failed to retrieve codebase information: ${e instanceof Error ? e.message : String(e)}`,
        toolRequestId,
      );
    }
  }
}
