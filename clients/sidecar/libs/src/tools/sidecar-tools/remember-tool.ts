import {
  ToolBase,
  ToolSafety,
  ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { RingBuffer } from "@augment-internal/sidecar-libs/src/ring-buffer";
import {
  ChatMode,
  ChatResultNodeType,
  Exchange,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  AgentSessionEventName,
  RememberToolCallData,
  RememberToolCallDebugFlag,
  RememberToolCaller,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import {
  errorToolResponse,
  successToolResponse,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import { getAgentSessionEventReporter } from "@augment-internal/sidecar-libs/src/metrics/agent-session-event-reporter";
import { llmCallStream } from "@augment-internal/sidecar-libs/src/utils/tools-utils";
import { AgentEditDiffViewDocument } from "@augment-internal/sidecar-libs/src/diff-view/document-agent";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import { getClientFeatureFlags } from "../../client-interfaces/feature-flags";
import { createRequestId } from "@augment-internal/sidecar-libs/src/utils/request-id";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import { MemoryUpdateManager } from "../../agent/memory/memory-update-manager";

/**
 * Tool to modify memories.
 */
export class RememberTool extends ToolBase<SidecarToolType> {
  private memoryRingBuffer: RingBuffer<string>;
  private readonly maxMemoryBufferSize = 1000;

  constructor(
    private readonly _getAgentMemories: () => Promise<string | undefined>,
    private readonly _getAgentMemoriesAbsPath: () => string | undefined,
    private readonly _memoryUpdateManager?: MemoryUpdateManager,
  ) {
    super(SidecarToolType.remember, ToolSafety.Safe);
    this.memoryRingBuffer = new RingBuffer<string>(this.maxMemoryBufferSize);
  }

  public readonly description: string = `\
Call this tool when user asks you:
- to remember something
- to create memory/memories

Use this tool only with information that can be useful in the long-term.
Do not use this tool for temporary information.
`;

  public readonly inputSchemaJson: string = JSON.stringify({
    /* eslint-disable @typescript-eslint/naming-convention */
    type: "object",
    properties: {
      memory: {
        type: "string",
        description: "The concise (1 sentence) memory to remember.",
      },
    },
    required: ["memory"],
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
    return true;
  }

  public async call(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    toolInput: Record<string, any>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    // Main inputs (visible to the model)
    const newMemory = toolInput.memory as string;

    this.memoryRingBuffer.addItem(newMemory);

    // Additional inputs (not visible to the model)
    const isComplexNewMemory =
      (toolInput.isComplexNewMemory as boolean) ?? false;
    const caller =
      (toolInput.caller as RememberToolCaller) ??
      RememberToolCaller.unspecified;
    const memoriesRequestId = toolInput.memoriesRequestId as string | undefined;

    // We use this object to trace the execution of this tool
    const trace = RememberToolCallData.create(caller, isComplexNewMemory);

    if (memoriesRequestId) {
      trace.setRequestId(
        RememberToolCallDebugFlag.memoriesRequestId,
        memoriesRequestId,
      );
    }

    let response: ToolUseResponse | undefined;
    try {
      response = await this.injectMemories(
        newMemory,
        isComplexNewMemory,
        trace,
      );
    } catch (e) {
      trace.setFlag(RememberToolCallDebugFlag.exceptionThrown);
    } finally {
      if (response === undefined) {
        response = errorToolResponse("Failed to save memory.");
      }
      trace.setFlag(
        RememberToolCallDebugFlag.toolOutputIsError,
        response.isError,
      );

      getAgentSessionEventReporter().reportEvent({
        eventName: AgentSessionEventName.rememberToolCall,
        // To fix it we need to pipe the conversation ID from the webview.
        conversationId: "",
        eventData: {
          rememberToolCallData: trace,
        },
      });
    }

    return response;
  }

  public async injectMemories(
    newMemory: string,
    isComplexNewMemory: boolean,
    trace: RememberToolCallData,
  ): Promise<ToolUseResponse> {
    trace.setFlag(RememberToolCallDebugFlag.injectionStarted);

    const currentMemories = await this._getAgentMemories();
    trace.setStringStats(
      RememberToolCallDebugFlag.injectionCurrentMemoriesStats,
      currentMemories,
    );
    if (!currentMemories) {
      if (newMemory.trim().split("\n").length === 1) {
        newMemory = `- ${newMemory}`;
      }
      return this._setMemories(newMemory, "", trace);
    }

    let injectionPrompt;
    if (isComplexNewMemory) {
      injectionPrompt = getClientFeatureFlags().flags.memoriesParams
        .complex_injection_prompt as string;
    } else {
      injectionPrompt = getClientFeatureFlags().flags.memoriesParams
        .injection_prompt as string;
    }
    if (!injectionPrompt) {
      trace.setFlag(RememberToolCallDebugFlag.injectionPromptMissing);
      return errorToolResponse("Failed to save memory.");
    }

    injectionPrompt = injectionPrompt
      .replace("{currentMemories}", currentMemories)
      .replace("{newMemory}", newMemory);
    trace.setStringStats(
      RememberToolCallDebugFlag.injectionPromptStats,
      injectionPrompt,
    );

    try {
      const requestId = createRequestId();
      trace.setRequestId(
        RememberToolCallDebugFlag.injectionRequestId,
        requestId,
      );
      const updatedMemories = await this._callModel(
        injectionPrompt,
        requestId,
        trace,
        ChatMode.memories,
      );

      trace.setStringStats(
        RememberToolCallDebugFlag.injectionUpdatedMemoriesStats,
        updatedMemories,
      );
      const linesDiff = Math.abs(
        updatedMemories.split("\n").length - currentMemories.split("\n").length,
      );
      const charsDiff = Math.abs(
        updatedMemories.length - currentMemories.length,
      );
      // It might happen if model goes off rails
      if (linesDiff > 10 || charsDiff > 1000) {
        throw new Error("Injection failed");
      }
      return this._setMemories(updatedMemories, currentMemories, trace);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (e: any) {
      trace.setFlag(RememberToolCallDebugFlag.injectionFailed);
      return errorToolResponse("Failed to save memory.");
    }
  }

  /**
   * Makes the model call and returns RAW_RESPONSE as a result
   *
   * Fails with exception if RAW_RESPONSE is not present.
   */
  private async _callModel(
    prompt: string,
    requestId: string,
    trace: RememberToolCallData,
    mode: ChatMode,
  ): Promise<string> {
    const chatResultIter = await llmCallStream(
      prompt,
      requestId,
      [],
      [],
      [],
      mode,
      true,
    );
    for await (const { nodes = [] } of chatResultIter) {
      const rawResponse = nodes.find(
        (node) => node.type === ChatResultNodeType.RAW_RESPONSE,
      )?.content;
      if (rawResponse) {
        const startIndex = rawResponse.indexOf("```");
        const endIndex = rawResponse.lastIndexOf("```");
        if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex + 3) {
          return rawResponse.substring(startIndex + 3, endIndex).trim();
        } else {
          trace.setFlag(RememberToolCallDebugFlag.injectionNoCodeWrapper);
          return rawResponse
            .trim()
            .replace(/^`+|`+$/g, "")
            .trim();
        }
      }
    }
    throw new Error("Model call failed");
  }

  async _setMemories(
    memories: string,
    oldMemories: string,
    trace: RememberToolCallData,
  ): Promise<ToolUseResponse> {
    trace.setFlag(RememberToolCallDebugFlag.setMemoriesStart);

    // Number of lines in memories that trigger compression
    const upperBoundSize = getClientFeatureFlags().flags.memoriesParams
      .upper_bound_size as number;
    if (!upperBoundSize) {
      trace.setFlag(RememberToolCallDebugFlag.setMemoriesUpperBoundSizeMissing);
      return Promise.resolve(
        errorToolResponse("Failed to save memories: upper bound size missing"),
      );
    }

    const nonEmptyLineCount = memories
      .split("\n")
      .filter((line) => line.trim()).length;
    trace.setNum(
      RememberToolCallDebugFlag.setMemoriesNonEmptyLines,
      nonEmptyLineCount,
    );
    if (nonEmptyLineCount >= upperBoundSize) {
      memories = await this._compressMemories(memories, trace);
    }

    const memoriesPath = this._getAgentMemoriesAbsPath();
    if (!memoriesPath) {
      trace.setFlag(RememberToolCallDebugFlag.setMemoriesNoMemoriesFile);
      return Promise.resolve(
        errorToolResponse(
          "Failed to save memories: no memories file available",
        ),
      );
    }

    // TODO: move it to cross-platform per-workspace location
    const document = new AgentEditDiffViewDocument(
      new QualifiedPathName("", memoriesPath),
      oldMemories,
      oldMemories,
      {},
    );
    const success = document.updateBuffer(memories);
    trace.setFlag(
      RememberToolCallDebugFlag.setMemoriesUpdateBufferFailed,
      !success,
    );

    if (memories === oldMemories) {
      trace.setFlag(RememberToolCallDebugFlag.setMemoriesNoChangesMade);
      document.dispose();
      return Promise.resolve(
        successToolResponse(`No changes made to memories`),
      );
    }

    document.dispose();

    // Notify listeners that memories have been updated if the update was successful
    if (success && this._memoryUpdateManager) {
      this._memoryUpdateManager.notifyMemoryHasUpdates();
    }

    return Promise.resolve(
      success
        ? successToolResponse(`Memories saved successfully.`)
        : errorToolResponse("Failed to update buffer"),
    );
  }

  async _compressMemories(
    memories: string,
    trace: RememberToolCallData,
  ): Promise<string> {
    trace.setFlag(RememberToolCallDebugFlag.compressionStarted);

    // Number of bullet points that compression tries to keep
    const compressionTarget = getClientFeatureFlags().flags.memoriesParams
      .compression_target as number;
    if (!compressionTarget) {
      trace.setFlag(RememberToolCallDebugFlag.compressionTargetMissing);
      return memories;
    }

    let compressionPrompt = getClientFeatureFlags().flags.memoriesParams
      .compression_prompt as string;
    if (!compressionPrompt) {
      trace.setFlag(RememberToolCallDebugFlag.compressionPromptMissing);
      return memories;
    }

    // To prevent model from compressing the most recent memories, we include them in the prompt
    const numRecentMemoriesToKeep = getClientFeatureFlags().flags.memoriesParams
      .num_recent_memories_to_keep as number;
    if (numRecentMemoriesToKeep === undefined) {
      trace.setFlag(
        RememberToolCallDebugFlag.compressionNumRecentMemoriesToKeepMissing,
      );
      return memories;
    }

    trace.setNum(
      RememberToolCallDebugFlag.compressionMemoriesQueueSize,
      this.memoryRingBuffer.length,
    );
    if (
      numRecentMemoriesToKeep > 0 &&
      compressionPrompt.includes("{recentMemoriesSubprompt}") &&
      this.memoryRingBuffer.length > 0
    ) {
      // Part of compression prompt, responsible for formatting the recent memories
      let recentMemoriesSubprompt = getClientFeatureFlags().flags.memoriesParams
        .recent_memories_subprompt as string;
      if (recentMemoriesSubprompt === undefined) {
        trace.setFlag(
          RememberToolCallDebugFlag.compressionRecentMemoriesSubpromptMissing,
        );
        return memories;
      }

      const recentMemoriesFormatted = this.memoryRingBuffer
        .slice(-numRecentMemoriesToKeep)
        .map((memory) => `- ${memory}`)
        .join("\n");

      recentMemoriesSubprompt = recentMemoriesSubprompt.replace(
        "{recentMemories}",
        recentMemoriesFormatted,
      );
      compressionPrompt = compressionPrompt.replace(
        "{recentMemoriesSubprompt}",
        recentMemoriesSubprompt,
      );
    } else if (compressionPrompt.includes("{recentMemoriesSubprompt}")) {
      compressionPrompt = compressionPrompt.replace(
        "{recentMemoriesSubprompt}",
        "",
      );
    }

    compressionPrompt = compressionPrompt
      .replace("{memories}", memories)
      .replace("{compressionTarget}", compressionTarget.toString());
    trace.setStringStats(
      RememberToolCallDebugFlag.compressionPromptStats,
      compressionPrompt,
    );

    // If compression fails, we just return original memories
    try {
      const requestId = createRequestId();
      trace.setRequestId(
        RememberToolCallDebugFlag.compressionRequestId,
        requestId,
      );
      const compressedMemories = await this._callModel(
        compressionPrompt,
        requestId,
        trace,
        ChatMode.memoriesCompression,
      );
      trace.setStringStats(
        RememberToolCallDebugFlag.compressionMemoriesStats,
        compressedMemories,
      );

      // It might happen if model goes off rails
      if (
        compressedMemories.split("\n").length >= memories.split("\n").length
      ) {
        return memories;
      }
      return compressedMemories;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (e: any) {
      trace.setFlag(RememberToolCallDebugFlag.compressionFailed);
      return memories;
    }
  }
}
