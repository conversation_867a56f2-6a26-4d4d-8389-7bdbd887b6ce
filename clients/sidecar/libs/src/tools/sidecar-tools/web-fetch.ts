import { Exchange } from "../../chat/chat-types";
import { ToolBase, ToolSafety, ToolUseResponse } from "../tool-types";
import { SidecarToolType } from "./sidecar-tool-types";
import TurndownService from "turndown";

const DEFAULT_USER_AGENT = "Augment-WebFetch/1.0";

export class WebFetchTool extends ToolBase<SidecarToolType> {
  private _turndownService: TurndownService;
  private _userAgent: string;

  constructor(userAgent?: string) {
    super(SidecarToolType.webFetch, ToolSafety.Unsafe);

    const turndownService = new TurndownService();

    // Remove style and script tags
    turndownService.addRule("removeStyleAndScriptTags", {
      filter: ["style", "script"],
      replacement: function () {
        return "";
      },
    });

    this._turndownService = turndownService;
    this._userAgent = userAgent || DEFAULT_USER_AGENT;
  }

  public readonly description: string = `\
Fetches data from a webpage and converts it into Markdown.

1. The tool takes in a URL and returns the content of the page in Markdown format;
2. If the return is not valid Markdown, it means the tool cannot successfully parse this page.`;

  public readonly inputSchemaJson: string = JSON.stringify({
    type: "object",
    properties: {
      url: {
        type: "string",
        description: "The URL to fetch.",
      },
    },
    required: ["url"],
  });

  public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
    return false;
  }

  public async call(
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    const url = toolInput.url as string;
    try {
      const response = await fetch(url, {
        signal: abortSignal,
        headers: {
          "User-Agent": this._userAgent,
        },
      });
      const text = await response.text();
      const markdown = this._turndownService.turndown(text);
      return {
        text: markdown,
        isError: false,
      };
    } catch (e: unknown) {
      return {
        text: `Failed to fetch URL: ${url}: ${e instanceof Error ? e.message : String(e)}`,
        isError: true,
      };
    }
  }
}
