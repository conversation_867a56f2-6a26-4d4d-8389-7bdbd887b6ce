import { Exchange } from "../../chat/chat-types";
import { getLogger, type AugmentLogger } from "../../logging";
import { ToolBase, ToolSafety, ToolUseResponse } from "../tool-types";
import { SidecarToolType } from "./sidecar-tool-types";
import { successToolResponse, errorToolResponse } from "./tool-use-response";
import { getClientWorkspaces } from "../../client-interfaces/client-workspaces";
import { DiffViewDocument } from "../../diff-view/document";
import { AggregateCheckpointManager } from "../../agent/checkpoint/aggregate-checkpoint-manager";
import { createRequestId } from "../../utils/request-id";
import { getClientFeatureFlags } from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import { validatePath } from "./file-utils";

/**
 * A tool that saves a new file.
 */
export class SaveFileTool extends ToolBase<SidecarToolType> {
  private readonly _logger: AugmentLogger;

  constructor(private readonly _checkpointManager: AggregateCheckpointManager) {
    super(SidecarToolType.saveFile, ToolSafety.Safe);
    if (getClientFeatureFlags().flags.agentSaveFileToolInstructionsReminder) {
      this.description = this.descriptionWithInstructionsReminder;
      this.inputSchemaJson = this.inputSchemaJsonWithInstructionsReminder;
    }

    this._logger = getLogger("SaveFileTool");
  }

  public readonly description: string =
    "Save a new file. " +
    "Use this tool to write new files with the attached content. " +
    "It CANNOT modify existing files. " +
    "Do NOT use this tool to edit an existing file by overwriting it entirely. " +
    "Use the str-replace-editor tool to edit existing files instead.";

  public readonly inputSchemaJson: string = JSON.stringify({
    /* eslint-disable @typescript-eslint/naming-convention */
    type: "object",
    properties: {
      path: {
        type: "string",
        description: "The path of the file to save.",
      },
      file_content: {
        type: "string",
        description: "The content of the file.",
      },
      add_last_line_newline: {
        type: "boolean",
        description:
          "Whether to add a newline at the end of the file (default: true).",
      },
    },
    required: ["path", "file_content"],
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  private readonly maxLines = 300;
  private readonly instructions = `LIMIT THE FILE CONTENT TO AT MOST ${this.maxLines} LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.`;

  private readonly descriptionWithInstructionsReminder: string =
    "Save a new file. " +
    "Use this tool to write new files with the attached content. " +
    `Generate \`instructions_reminder\` first to remind yourself to limit the file content to at most ${this.maxLines} lines. ` +
    "It CANNOT modify existing files. " +
    "Do NOT use this tool to edit an existing file by overwriting it entirely. " +
    "Use the str-replace-editor tool to edit existing files instead.";

  private readonly inputSchemaJsonWithInstructionsReminder: string =
    JSON.stringify({
      /* eslint-disable @typescript-eslint/naming-convention */
      type: "object",
      properties: {
        instructions_reminder: {
          type: "string",
          description: `Should be exactly this string: '${this.instructions}'`,
        },
        path: {
          type: "string",
          description: "The path of the file to save.",
        },
        file_content: {
          type: "string",
          description: "The content of the file.",
        },
        add_last_line_newline: {
          type: "boolean",
          description:
            "Whether to add a newline at the end of the file (default: true).",
        },
      },
      required: ["instructions_reminder", "path", "file_content"],
      /* eslint-enable @typescript-eslint/naming-convention */
    });

  public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
    return true;
  }

  public async call(
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    this._logger.debug(`Tool called with path: ${String(toolInput.path)}`);
    try {
      const filePath = validatePath(toolInput.path);
      const fileContent = (toolInput.file_content ?? "") as string;
      const addLastLineNewline = (toolInput.add_last_line_newline ??
        true) as boolean;
      const contentToWrite = fileContent + (addLastLineNewline ? "\n" : "");
      const qualifiedPathName =
        await getClientWorkspaces().getQualifiedPathName(filePath);
      if (qualifiedPathName === undefined) {
        return errorToolResponse(`Cannot resolve path: ${filePath}`);
      }
      this._logger.debug(`Resolved path: ${String(qualifiedPathName)}`);
      let fileDetails = undefined;
      try {
        fileDetails = await getClientWorkspaces().readFile(
          qualifiedPathName.absPath,
        );
      } catch (e) {
        // File does not exist, which is fine
      }
      const originalContent = fileDetails?.contents ?? "";
      const fileExists =
        fileDetails !== undefined && fileDetails.contents !== undefined;

      // If file exists and content is the same, do not fail or create a checkpoint
      if (fileExists && originalContent === contentToWrite) {
        return successToolResponse(
          `File already exists and content is the same: {${qualifiedPathName.absPath}}`,
        );
      }

      if (fileExists) {
        return errorToolResponse(
          `File already exists: ${qualifiedPathName.absPath}`,
        );
      }

      // Make a new document
      const document = new DiffViewDocument(
        qualifiedPathName,
        undefined,
        contentToWrite,
        {},
      );

      // Get the exchange ID that resulted in this tool call.
      const toolUseExchangeRequestId =
        chatHistory.at(-1)?.request_id ?? createRequestId();
      const conversationId =
        this._checkpointManager.currentConversationId ?? "";
      await this._checkpointManager.addCheckpoint(
        {
          conversationId,
          path: qualifiedPathName,
        },
        {
          sourceToolCallRequestId: toolUseExchangeRequestId,
          timestamp: Date.now(),
          document,
          conversationId,
        },
      );

      if (fileContent === "") {
        return successToolResponse(
          `File saved.  Created empty file {${filePath}}`,
        );
      }
      return successToolResponse(`File saved.  Saved file {${filePath}}`);
    } catch (e) {
      const message = e instanceof Error ? e.message : String(e);
      return errorToolResponse(
        `Failed to save file: ${toolInput.path as string}: ${message}`,
      );
    }
  }
}
