import { promisify } from "util";
import { exec } from "child_process";
import { ChatMode, Exchange } from "../../chat/chat-types";
import { ToolBase, ToolSafety, ToolUseResponse } from "../tool-types";
import { SidecarToolType } from "./sidecar-tool-types";
import { getClientWorkspaces } from "../../client-interfaces/client-workspaces";
import {
  checkShellAllowlist,
  getShellAllowlist,
  ShellAllowlist,
} from "./shell-allowlist";
import { quote } from "./shell-utils";

/**
 * A tool that executes a shell command but always asks for permission first.
 */
export class SimpleShellTool extends ToolBase<SidecarToolType> {
  readonly execPromise = promisify(exec);
  private readonly _allowlist: ShellAllowlist;

  constructor(
    private readonly _chatMode: ChatMode,
    private readonly _platform: string,
    private readonly _shellName: string,
  ) {
    super(SidecarToolType.shell, ToolSafety.Check);
    this._allowlist = getShellAllowlist(process.platform, this._shellName);
  }

  public get description(): string {
    return `\
Execute a shell command.

- You can use this tool to interact with the user's local version control system. Do not use the
retrieval tool for that purpose.
- If there is a more specific tool available that can perform the function, use that tool instead of
this one.

The OS is ${process.platform}. The shell is '${this._shellName}'.`;
  }

  public readonly inputSchemaJson: string = JSON.stringify({
    /* eslint-disable @typescript-eslint/naming-convention */
    type: "object",
    properties: {
      command: {
        type: "string",
        description: "The shell command to execute.",
      },
    },
    required: ["command"],
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  public checkToolCallSafe(toolInput: Record<string, unknown>): boolean {
    if (
      this._chatMode === ChatMode.agent ||
      this._chatMode === ChatMode.remoteAgent
    ) {
      const command = toolInput.command as string;
      return checkShellAllowlist(this._allowlist, command, this._shellName);
    }
    return false;
  }

  public async call(
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    const command = toolInput.command as string;
    let execCommand = command;
    if (this._shellName === "bash") {
      execCommand = `bash -l -c ${quote([command])}`;
    }
    try {
      const result = await this.execPromise(execCommand, {
        signal: abortSignal,
        cwd: await getClientWorkspaces().getCwd(),
        shell: this._shellName,
      });
      return {
        text: result.stdout + result.stderr,
        isError: false,
      };
    } catch (e: unknown) {
      // exec will throw an error if the exit code is non-zero, but the output
      // of a test runner may still be valid
      // Check if code, stdout and stderr are defined and if so, treat
      // as a successful run
      if (isExecException(e)) {
        if (e.stdout !== undefined && e.stderr !== undefined) {
          return {
            text: e.stdout + e.stderr,
            isError: false,
          };
        }
      }

      return {
        text: `Failed to execute shell command: ${e instanceof Error ? e.message : String(e)}`,
        isError: true,
      };
    }
  }
}

function isExecException(obj: unknown): obj is ExecException {
  return (
    obj !== null &&
    typeof obj === "object" &&
    "code" in obj &&
    "stderr" in obj &&
    "stdout" in obj &&
    "message" in obj &&
    "name" in obj
  );
}

type ExecException = Error & {
  code?: number;
  stdout?: string;
  stderr?: string;
};
