import { Exchange } from "../chat/chat-types";
import { ITool } from "./tool-host-base";
import { SidecarToolType } from "./sidecar-tools/sidecar-tool-types";

export enum LocalToolType {
  readFile = "read-file",
  saveFile = "save-file",
  editFile = "edit-file",
  clarify = "clarify",
  onboardingSubAgent = "onboarding-sub-agent",
  launchProcess = "launch-process",
  killProcess = "kill-process",
  readProcess = "read-process",
  writeProcess = "write-process",
  listProcesses = "list-processes",
  waitProcess = "wait-process",
  openBrowser = "open-browser",
  strReplaceEditor = "str-replace-editor",
  remember = "remember",
  diagnostics = "diagnostics",
  setupScript = "setup-script",
  readTerminal = "read-terminal",
  gitCommitRetrieval = "git-commit-retrieval",
}

export enum ToolHostName {
  remoteToolHost = "remoteToolHost",
  localToolHost = "localToolHost",
  sidecarToolHost = "sidecarToolHost",
  mcpHost = "mcpHost",
}

export type MCPToolType = string;
export type ToolType =
  | RemoteToolId
  | LocalToolType
  | SidecarToolType
  | MCPToolType;

/**
 * Interface representing a unique tool identifier in the system
 * Uses discriminated union to ensure type safety between host names and tool IDs
 */
export type ToolIdentifier =
  | {
      hostName: ToolHostName.remoteToolHost;
      toolId: RemoteToolId;
    }
  | {
      hostName: ToolHostName.localToolHost;
      toolId: LocalToolType;
    }
  | {
      hostName: ToolHostName.sidecarToolHost;
      toolId: SidecarToolType;
    }
  | {
      hostName: ToolHostName.mcpHost;
      toolId: MCPToolType;
    };

export interface ToolDefinitionWithSettings {
  definition: ToolDefinition;
  identifier: ToolIdentifier;
  isConfigured: boolean;
  enabled: boolean;
  toolSafety: ToolSafety;
  oauthUrl?: string;
}

export type McpServerConfig = {
  command: string;
  args?: string[];
  timeoutMs?: number;
  env?: { [key: string]: string };
  useShellInterpolation?: boolean;
  name?: string; // User-defined name for the MCP server, used for namespacing
  disabled?: boolean;
};

export interface ToolUseRequest {
  // The request ID of the chat exchange that generated the tool use
  chatRequestId: string;
  toolUseId: string;
  name: string;
  input: Record<string, unknown>;
  chatHistory: Exchange[];
  conversationId: string;
}

// Response types using the enum-based pattern
export enum ToolResponseContentNodeType {
  ContentText = 0,
  ContentImage = 1,
}

export interface ToolResponseImageContent {
  image_data: string; // Base64-encoded image data
  media_type: string;
}

export interface ToolResponseContentNode {
  type: ToolResponseContentNodeType;
  text_content?: string;
  image_content?: ToolResponseImageContent;
}

export interface ToolUseResponse {
  text: string;
  isError: boolean;
  // The request ID of the tool execution itself, if applicable
  requestId?: string;
  // Array of content nodes (text and images)
  contentNodes?: ToolResponseContentNode[];
}

export interface ToolCheckSafe {
  name: string;
  input: Record<string, unknown>;
}

export interface ToolCheckSafeResult {
  isSafe: boolean;
}

export type ToolDefinition = {
  name: ToolType;
  description: string;
  input_schema_json: string;
  tool_safety: ToolSafety;
  mcp_server_name?: string; // MCP server name for MCP tools
  mcp_tool_name?: string; // Original MCP tool name without server suffix
};

export enum ToolSafety {
  /** Tool always needs user approval to run. */
  Unsafe = 0,

  /** Tool does not need user approval to run. */
  Safe = 1,

  /** For some inputs, the tool needs user approval and for some it does not. */
  Check = 2,
}

export function convertIntToToolSafety(value: number): ToolSafety {
  // Check if the value is a valid ToolSafety enum value
  if (Object.values(ToolSafety).includes(value as ToolSafety)) {
    return value as ToolSafety;
  }
  // Default to Unsafe for invalid values
  return ToolSafety.Unsafe;
}

export interface ToolStartupError {
  command?: string;
  args?: string[];
  error?: string;
  stderr?: string;
}

export type ToolStartupErrorFn = (details: ToolStartupError) => void;

export abstract class ToolBase<T extends ToolType> implements ITool<T> {
  constructor(
    public name: T,
    public toolSafety: ToolSafety,
  ) {}

  abstract description: string;

  abstract inputSchemaJson: string;

  public version: number = 1;

  abstract checkToolCallSafe(toolInput: Record<string, unknown>): boolean;

  abstract call(
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    abortSignal: AbortSignal,
  ): Promise<ToolUseResponse>;
}

export enum RemoteToolId {
  Unknown = 0,

  // Google search
  WebSearch = 1,

  // Jira tools (DEPRECATED)
  // JiraSearch = 2,
  // JiraIssue = 3,
  // JiraProject = 4,

  // Notion tools (DEPRECATED)
  // NotionSearch = 5,
  // NotionPage = 6,

  // Linear tools (DEPRECATED)
  // LinearSearchIssues = 7,

  // GitHub tools
  GitHubApi = 8,

  // Confluence tools (DEPRECATED)
  // ConfluenceSearch = 9,
  // ConfluenceContent = 10,
  // ConfluenceSpace = 11,

  // New integration tools
  Linear = 12,
  Jira = 13,
  Confluence = 14,
  Notion = 15,
  Supabase = 16,
  Glean = 17,
}

export enum ToolAvailabilityStatus {
  UnknownStatus = 0,
  Available = 1,
  UserConfigRequired = 2,
}

export type AtlassianToolExtraInput = {
  // The URL of the Atlassian server, e.g. https://augmentcode.atlassian.net
  serverUrl: string;

  // The user's API token
  personalApiToken: string;

  // The user's email address
  username: string;
};

export type NotionToolExtraInput = {
  // The Notion API token
  apiToken: string;
};

export type LinearToolExtraInput = {
  // The Linear API token
  apiToken: string;
};

export type GitHubToolExtraInput = {
  // The GitHub API token
  apiToken: string;
};

export type ExtraToolInput =
  | AtlassianToolExtraInput
  | NotionToolExtraInput
  | LinearToolExtraInput
  | GitHubToolExtraInput;

export type ToolUseRequestEventReporter = {
  reportEvent: (
    chatRequestId: string,
    toolName: string,
    toolUseId: string,
    toolInput: Record<string, unknown>,
    toolOutputIsError: boolean,
    toolRunDurationMs: number,
    isMcpTool: boolean,
    conversationId: string,
    chatHistoryLength: number,
    toolRequestId?: string,
    toolOutputLen?: number,
  ) => void;
};

/**
 * Common shell configuration type shared across the codebase
 */
export interface ShellConfig {
  /** The internal name of the shell (e.g., 'bash', 'zsh', 'powershell') */
  name: string;

  /** The path to the shell executable */
  path?: string;

  /** Arguments to pass to the shell */
  args?: string[];

  /** Environment variables to set for the shell */
  env?: Record<string, string>;

  /** User-friendly display name for the shell */
  friendlyName: string;

  /** Information about the shell's capabilities to show the user */
  supportString?: string;
}

/**
 * Interface for terminal settings
 */
export interface TerminalSettings {
  /** List of supported shells */
  supportedShells: ShellConfig[];

  /** The selected shell's friendlyName */
  selectedShell?: string;

  /** Startup script to run when a new terminal is created */
  startupScript?: string;
}
