import { AggregateCheckpointManager } from "../agent/checkpoint/aggregate-checkpoint-manager";
import { ChatMode, Exchange } from "../chat/chat-types";
import { IClientFeatureFlags } from "../client-interfaces/feature-flags";
import { getLogger } from "../logging";
import { McpHost } from "./mcp/mcp-host";
import {
  RemoteInfoSource,
  RemoteToolHost,
} from "./remote-tools/remote-tool-host";
import { SidecarToolHost } from "./sidecar-tools/sidecar-tool-host";
import { SidecarToolType } from "./sidecar-tools/sidecar-tool-types";
import { IToolHost } from "./tool-host";
import { ITool } from "./tool-host-base";
import {
  McpServerConfig,
  ToolDefinitionWithSettings,
  ToolHostName,
  ToolStartupErrorFn,
  ToolType,
  ToolUseRequestEventReporter,
  ToolUseResponse,
} from "./tool-types";
import { EventEmitter } from "events";
import {
  PluginStateNamespace,
  PluginStateScope,
} from "../client-interfaces/plugin-state";
import { getStateForSidecar } from "../client-interfaces/plugin-state";
import { MemorySnapshotManager } from "../agent/memory/memory-snapshot-manager";
import { TaskManager } from "../agent/task/task-manager";

/**
 * Manages the tools functionality.
 *
 * These are "tools" in the sense of Claude's Tool Use
 * {@link https://docs.anthropic.com/en/docs/build-with-claude/tool-use}
 * or Open AI Function Calling
 * {@link https://platform.openai.com/docs/guides/function-calling}
 *
 * `ToolsModel` aggregates multiple tool hosts implementing the `IToolHost`
 * interface and provides an abstraction over them . Examples of tools hosts
 * include:
 * - MCP servers (`McpHost`)
 * - Our backend with its remote tools (`RemoteToolHost`) like web search or the
 *   Github API
 * - Client side tools (`SidecarToolHost`) like the shell tool
 *
 * Currently, tools from local MCP (Model Context Protocol) servers are supported.
 */
export class ToolsModel {
  // Static event name for host restart events
  private static readonly RESTART_HOSTS_EVENT =
    "augment/clients/sidecar/events/restart-hosts";

  // Storage key for persisting chat mode
  private static readonly CHAT_MODE_STORAGE_KEY =
    "augment/clients/sidecar/chat-mode";

  // hosts with tools available to the model based on the current policy
  private _hosts: IToolHost[] = [];
  // all tool hosts, even disabled ones, to provide information for the settings panel
  private _allToolHosts: IToolHost[] = [];

  private _chatMode?: ChatMode;
  private readonly _logger = getLogger("ToolsModel");
  private _memorySnapshotManager: MemorySnapshotManager;

  /**
   * Event emitter for host restart events
   * @private
   */
  private readonly _onRestartHostsEmitter = new EventEmitter();

  /**
   * Event that fires when hosts are restarted
   * Subscribers can use this to perform actions after hosts are restarted
   */
  public readonly onRestartHosts = (
    listener: () => void,
  ): { dispose: () => void } => {
    this._onRestartHostsEmitter.on(ToolsModel.RESTART_HOSTS_EVENT, listener);
    return {
      dispose: () => {
        this._onRestartHostsEmitter.off(
          ToolsModel.RESTART_HOSTS_EVENT,
          listener,
        );
      },
    };
  };

  constructor(
    private _mcpServers: McpServerConfig[],
    private _clientToolHostFactory: (chatMode: ChatMode) => IToolHost,
    private _remoteInfoSource: RemoteInfoSource,
    private _mcpToolsStartupErrorFn: ToolStartupErrorFn,
    private _clientFeatureFlags: IClientFeatureFlags,
    private _checkpointManager: AggregateCheckpointManager,
    private _getAgentMemories: () => Promise<string | undefined>,
    private _getMemoriesAbsPath: () => string | undefined,
    private _getToolUseRequestEventReporter: () =>
      | ToolUseRequestEventReporter
      | undefined,
    private _options: ToolsModelOptions = {},
    private _taskManager: TaskManager | undefined = undefined,
  ) {
    // Initialize the memory snapshot manager
    this._memorySnapshotManager = new MemorySnapshotManager(
      this._getAgentMemories,
    );

    // Load saved mode (if any) and then initialize
    void this.loadSavedMode().then(() => {
      // Apply the initial config after loading the saved mode
      this.restartHosts();
    });
  }

  /**
   * Loads the saved chat mode from plugin state storage.
   * @returns A promise that resolves when the mode is loaded
   */
  private async loadSavedMode(): Promise<void> {
    try {
      const pluginState = getStateForSidecar();
      const savedMode = await pluginState.getValue<ChatMode>(
        PluginStateNamespace.agent,
        ToolsModel.CHAT_MODE_STORAGE_KEY,
        PluginStateScope.global,
      );

      // Only load if the saved mode is valid and we dont have a mode already
      if (
        savedMode &&
        Object.values(ChatMode).includes(savedMode) &&
        this._chatMode === undefined
      ) {
        this._chatMode = savedMode;
        this._logger.info(`Loaded saved chat mode: ${String(savedMode)}`);
      }
    } catch (error) {
      this._logger.warn(`Failed to load saved chat mode: ${String(error)}`);
    }
  }

  public get memoriesAbsPath(): string | undefined {
    return this._getMemoriesAbsPath();
  }

  /**
   * Gets agent memories with snapshot support.
   * @param conversationId The current conversation ID
   * @returns The agent memories content
   *
   * NOTE: This ideally should not live here for much longer. Yes, the toolsModel uses
   * memories, but ideally the memories should move into their own independent model. It's
   * semantically incorrect to say that tools "has" memories.
   */
  public async getAgentMemoriesWithSnapshot(
    conversationId: string,
  ): Promise<string | undefined> {
    return this._memorySnapshotManager.getMemorySnapshot(conversationId);
  }

  /**
   * Forces an update of the memory snapshot.
   * This should be called when the memories file is manually edited.
   *
   * NOTE: This ideally should not live here for much longer. Yes, the toolsModel uses
   * memories, but ideally the memories should move into their own independent model. It's
   * semantically incorrect to say that tools "has" memories.
   */
  public async forceMemorySnapshotUpdate(): Promise<void> {
    await this._memorySnapshotManager.forceUpdateSnapshot();
  }

  /**
   * @returns A map of *enabled* tool names to tool hosts.
   */
  private async getToolMap(): Promise<Map<string, IToolHost>> {
    const toolMap = new Map<string, IToolHost>();
    for (const host of this.hosts) {
      const toolDefinitions = await host.getToolDefinitions();
      for (const toolDefinition of toolDefinitions) {
        if (!this.isEnabled(toolDefinition)) {
          continue;
        }
        toolMap.set(toolDefinition.definition.name.toString(), host);
      }
    }

    return toolMap;
  }

  public get hosts(): readonly IToolHost[] {
    return this._hosts;
  }

  public get chatMode(): ChatMode {
    return this._chatMode ?? ChatMode.chat;
  }

  /**
   * Returns the SidecarToolHost instance if available.
   * This is used to access the MemoryUpdateManager for memory update notifications.
   * @returns The SidecarToolHost instance or undefined if not available
   */
  public getSidecarToolHost(): SidecarToolHost | undefined {
    for (const host of this._hosts) {
      if (host instanceof SidecarToolHost) {
        return host;
      }
    }
    return undefined;
  }

  public setMode(mode: ChatMode) {
    if (mode === this._chatMode) {
      return;
    }

    this._chatMode = mode;

    // Save mode to plugin state
    try {
      const pluginState = getStateForSidecar();
      void pluginState.setValue(
        PluginStateNamespace.agent,
        ToolsModel.CHAT_MODE_STORAGE_KEY,
        mode,
        PluginStateScope.global,
      );
      this._logger.info(`Saved chat mode: ${String(mode)}`);
    } catch (error) {
      this._logger.warn(`Failed to save chat mode: ${String(error)}`);
    }

    this.restartHosts();
  }

  public onFlagsChanged() {
    this.restartHosts();
  }

  public setMcpServers(mcpServers: McpServerConfig[]) {
    this._mcpServers = mcpServers;
    this.restartHosts(true);
  }

  public async getToolDefinitions(): Promise<ToolDefinitionWithSettings[]> {
    const toolDefinitions: ToolDefinitionWithSettings[] = [];
    for (const host of this.hosts) {
      const tools = await host.getToolDefinitions();
      for (const tool of tools) {
        if (!this.isEnabled(tool)) {
          continue;
        }
        toolDefinitions.push(tool);
      }
    }
    return toolDefinitions;
  }

  public async getToolStatusForSettingsPanel(
    useCache: boolean = true,
  ): Promise<ToolDefinitionWithSettings[]> {
    const toolDefinitions: ToolDefinitionWithSettings[] = [];

    for (const host of this._allToolHosts) {
      const tools = await host.getAllToolDefinitions(useCache);
      toolDefinitions.push(...tools);
    }

    return toolDefinitions;
  }

  public async callTool(
    chatRequestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: Record<string, unknown>,
    chatHistory: Exchange[],
    conversationId: string,
  ): Promise<ToolUseResponse> {
    const toolMap = await this.getToolMap();
    const host = toolMap.get(toolName);
    if (host === undefined) {
      return {
        isError: true,
        text: `Tool ${toolName} not found.`,
      };
    }

    const startTime = Date.now();
    const result = await host.callTool(
      chatRequestId,
      toolUseId,
      toolName,
      toolInput,
      chatHistory,
    );
    const toolUseRequestEventReporter = this._getToolUseRequestEventReporter();
    if (toolUseRequestEventReporter !== undefined) {
      // Calculate the tool output length in characters
      const toolOutputLen = result.text ? result.text.length : undefined;

      toolUseRequestEventReporter.reportEvent(
        chatRequestId,
        toolName,
        toolUseId,
        toolInput,
        result.isError,
        Date.now() - startTime,
        host instanceof McpHost,
        conversationId,
        chatHistory.length,
        result.requestId,
        toolOutputLen,
      );
    }
    return result;
  }

  public async checkToolCallSafe(
    toolName: string,
    toolInput: Record<string, unknown>,
  ): Promise<boolean> {
    const toolMap = await this.getToolMap();
    const host = toolMap.get(toolName);
    if (host === undefined) {
      throw new Error(`Cannot find the host for tool '${toolName}'.`);
    }
    return await host.checkToolCallSafe(toolName, toolInput);
  }

  public async checkToolExists(toolName: string): Promise<boolean> {
    const toolMap = await this.getToolMap();
    return toolMap.has(toolName);
  }

  public async cancelToolRun(
    requestId: string,
    toolUseId: string,
  ): Promise<void> {
    // Find the host that is running the tool and close / reopen it.
    //
    // While the MCP SDK has a cancellation API, its usage still amounts to closing
    // and restarting the server, and so essentially relaunching the local process.
    // Implementing the cancellation by restarting the server ourselves seems to
    // be a bit simpler than using the cancellation API, and not much efficiency
    // is lost.
    for (let i = 0; i < this.hosts.length; i++) {
      const host = this.hosts[i];
      if (host.isRequestActive(requestId, toolUseId)) {
        // Close the old host
        const closePromise = host.close(true);

        // Create a new host
        this._hosts[i] = host.factory(closePromise);

        // Return the close promise so that the caller waits for the cancellation to finish
        return closePromise;
      }
    }

    // Nothing to cancel.
    return Promise.resolve();
  }

  public restartHosts(mcpOnly: boolean = false): void {
    // All tool hosts
    // Remarks:
    // - The new mcp hosts will wait for the closeAllPromise to resolve
    //   before serving any requests. This ensures that all past work has
    //   quiesced.
    // - In TypeScript, it's fine to await the same promise (closeAllPromise)
    //   multiple times.
    let hostsToClose = this._allToolHosts;
    if (mcpOnly) {
      hostsToClose = this._allToolHosts.filter(
        (host) => host.getName() === ToolHostName.mcpHost,
      );
    }
    const closePromises = hostsToClose.map((host) => host.close());
    const closeAllPromise = Promise.all(closePromises).then(() => {});

    const mcpHosts = this._mcpServers.map(
      (config) =>
        new McpHost(config, closeAllPromise, this._mcpToolsStartupErrorFn),
    );

    let remoteToolHost: RemoteToolHost | undefined;
    let localToolHost: IToolHost | undefined;
    let sidecarToolHost: SidecarToolHost | undefined;
    if (mcpOnly) {
      remoteToolHost = this._allToolHosts.find(
        (host) => host.getName() === ToolHostName.remoteToolHost,
      ) as RemoteToolHost;
      localToolHost = this._allToolHosts.find(
        (host) => host.getName() === ToolHostName.localToolHost,
      );
      sidecarToolHost = this._allToolHosts.find(
        (host) => host.getName() === ToolHostName.sidecarToolHost,
      ) as SidecarToolHost;
    }
    remoteToolHost =
      remoteToolHost ?? new RemoteToolHost(this._remoteInfoSource);
    localToolHost = localToolHost ?? this._clientToolHostFactory(this.chatMode);
    sidecarToolHost =
      sidecarToolHost ??
      new SidecarToolHost(
        this.chatMode,
        this._clientFeatureFlags,
        this._checkpointManager,
        this._getAgentMemories,
        this._getMemoriesAbsPath,
        this._options.unsupportedSidecarTools ?? new Set<SidecarToolType>(),
        this._options.userAgent,
        this._taskManager,
      );

    // keep all hosts for reporting to the settings panel
    this._allToolHosts = [
      ...mcpHosts,
      localToolHost,
      remoteToolHost,
      sidecarToolHost,
    ];

    // the model might use a subset of the hosts, depending on the mode
    const useTools =
      (this._chatMode === ChatMode.chat &&
        this._clientFeatureFlags.flags.enableChatWithTools) ||
      (this._chatMode === ChatMode.agent &&
        this._clientFeatureFlags.flags.enableAgentMode) ||
      this._chatMode === ChatMode.remoteAgent; // Always use tools in the remote agent
    if (useTools) {
      this._hosts = [
        ...mcpHosts,
        localToolHost,
        remoteToolHost,
        sidecarToolHost,
      ];
    } else {
      this._hosts = [];
    }

    void this.logTools();

    // Notify all listeners that hosts have been restarted
    this._onRestartHostsEmitter.emit(ToolsModel.RESTART_HOSTS_EVENT);
  }

  public closeAllToolProcesses() {
    for (const host of this.hosts) {
      void host.closeAllToolProcesses();
    }
  }

  public getTool<T extends ToolType>(toolName: string): ITool<T> | undefined {
    for (const host of this.hosts) {
      const tool = host.getTool<T>(toolName);
      if (tool) {
        return tool;
      }
    }
    return undefined;
  }

  private async logTools() {
    this._logger.info(
      `Tools Mode: ${this._chatMode} (${this.hosts.length} hosts)`,
    );
    for (const host of this.hosts) {
      const toolDefs = await host.getToolDefinitions();

      const enabledTools: ToolType[] = [];
      const disabledTools: ToolType[] = [];
      for (const toolDef of toolDefs) {
        const list = this.isEnabled(toolDef) ? enabledTools : disabledTools;
        list.push(toolDef.definition.name);
      }
      const enabledToolNames = enabledTools
        .map((name) => ` + ${name}`)
        .join("\n");
      const disabledToolNames = disabledTools
        .map((name) => ` - ${name}`)
        .join("\n");

      this._logger.info(
        `Host: ${host.getName()} (${toolDefs.length} tools: ${enabledToolNames.length} enabled, ${disabledToolNames.length} disabled})\n${enabledToolNames}\n${disabledToolNames}`,
      );
    }
  }

  private isEnabled(_toolDefinition: ToolDefinitionWithSettings) {
    return _toolDefinition.enabled;
  }
}

type ToolsModelOptions = {
  userAgent?: string;
  unsupportedSidecarTools?: Set<SidecarToolType>;
};
