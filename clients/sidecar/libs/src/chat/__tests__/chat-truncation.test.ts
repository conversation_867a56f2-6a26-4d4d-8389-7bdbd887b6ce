import { limitChatHistoryTruncate } from "../chat-truncation";
import { Exchange } from "../chat-types";

describe("limitChatHistoryTruncate", () => {
  test("returns full history when under segment limit", () => {
    // Create a small history that should be under the limit
    /* eslint-disable @typescript-eslint/naming-convention */
    const smallHistory: Exchange[] = [
      {
        request_message: "Hello",
        response_text: "Hi there",
        request_id: "1",
      },
      {
        request_message: "How are you?",
        response_text: "I'm good",
        request_id: "2",
      },
    ];
    /* eslint-enable @typescript-eslint/naming-convention */

    const result = limitChatHistoryTruncate(smallHistory);
    expect(result).toEqual(smallHistory);
  });

  test("history stable while growing", () => {
    // Create a large history that will exceed the limit
    // This test does rely on the limit hard-coded in the implementation: currently 800000
    /* eslint-disable @typescript-eslint/naming-convention */
    const largeHistory = Array(20)
      .fill(0)
      .map((_, i) => ({
        request_message: `Message ${i}`,
        response_text: `Response ${i}`,
        request_id: "x".repeat(100000),
      }));
    /* eslint-enable @typescript-eslint/naming-convention */

    const cases = [
      { count: 1, start: 0 },
      { count: 2, start: 0 },
      { count: 3, start: 0 },
      { count: 4, start: 0 },
      { count: 5, start: 0 },
      { count: 6, start: 0 },
      { count: 7, start: 4 },
      { count: 8, start: 4 },
      { count: 9, start: 4 },
      { count: 10, start: 4 },
      { count: 11, start: 8 },
      { count: 12, start: 8 },
      { count: 13, start: 8 },
      { count: 14, start: 8 },
      { count: 15, start: 12 },
      { count: 16, start: 12 },
      { count: 17, start: 12 },
      { count: 18, start: 12 },
      { count: 19, start: 16 },
      { count: 20, start: 16 },
    ];
    for (const testCase of cases) {
      const result = limitChatHistoryTruncate(
        largeHistory.slice(0, testCase.count),
      );
      expect(JSON.stringify(result).length).toBeLessThan(800000);
      expect(result[0].request_message).toEqual(
        largeHistory[testCase.start].request_message,
      );
    }
  });
});
