import { Exchange } from "./chat-types";

// The maximum number of characters to keep in history when sending to the backend.
// The backend will likely further truncate turns of history.
const MAX_HISTORY_CHARS = 800_000;

export function limitChatHistoryWindow(history: Exchange[]): Exchange[] {
  // Only send the latest exchanges up to estimated character budget
  // Reason: limitation on max HTTP payload size / avoid 413 error
  // For Chat, this is already a massive history; particularly if
  // there are no tool uses or results.
  // For Agent, it is not that rare to approach the payload limit.
  // TODO: do the truncation differently for agent to preserve prompt
  // caching
  let i = history.length - 1;
  let totalCharEstimation = 0;
  while (i >= 0) {
    const exchange = history[i];
    totalCharEstimation += JSON.stringify(exchange).length;
    if (totalCharEstimation > MAX_HISTORY_CHARS) {
      break;
    }
    i--;
  }
  return history.slice(i + 1);
}

export function limitChatHistoryTruncate(history: Exchange[]): Exchange[] {
  // Only send the latest exchanges up to estimated character budget
  // Reason: limitation on max HTTP payload size / avoid 413 error

  // Process exchanges oldest to newest. Upon reaching maximum budget, we
  // remove 2/3 of the history so far from the "oldest" end, then continue
  // in this way until we've processed the entire history.
  // The reason for this is so that requests are presented to the backend
  // with a stable history prefix for a longer period of time. If the start
  // of history is constantly changing, the backend can't reliably build
  // cacheable prompts without storing information between requests.
  const SEGMENT_CHARS = MAX_HISTORY_CHARS / 3;

  // Actual algorithm just keeps track of positions in history separating it into
  // segments of size <= SEGMENT_CHARS. Based on above description, candidate starting
  // positions for truncated history would be at indexes 0, 2, 4, 6, etc., and we
  // advance upon reaching array of length 4, 6, 8, etc.
  const segmentStarts = [0];
  let runningCharEstimation = 0;
  for (let i = 0; i < history.length; i++) {
    const exchange = history[i];
    const exchangeChars = JSON.stringify(exchange).length;
    if (runningCharEstimation + exchangeChars > SEGMENT_CHARS) {
      segmentStarts.push(i);
      runningCharEstimation = 0;
    }
    runningCharEstimation += exchangeChars;
  }

  if (segmentStarts.length < 4) {
    return history;
  }

  const segmentIndex = 2 * Math.floor((segmentStarts.length - 2) / 2);
  return history.slice(segmentStarts[segmentIndex]);
}
