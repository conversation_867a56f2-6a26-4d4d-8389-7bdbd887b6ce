/**
 * @file task-utils.ts
 * This file contains utility functions for working with tasks.
 */

import {
  HydratedTask,
  type SerializedTask,
  TaskState,
  TaskUpdatedBy,
} from "./task-types";

/**
 * Mapping from TaskState to markdown state markers.
 * Used for consistent representation of task states in markdown.
 */
export const STATE_MARKDOWN: Record<TaskState, string> = {
  [TaskState.NOT_STARTED]: "[ ]",
  [TaskState.IN_PROGRESS]: "[/]", // Use [/] to represent in-progress tasks
  [TaskState.COMPLETE]: "[x]",
  [TaskState.CANCELLED]: "[-]",
};

/**
 * Utility functions for working with tasks.
 */
export class TaskFactory {
  /**
   * Creates a new task object.
   * @param name - The name of the task
   * @param description - The description of the task
   * @param state - The initial state of the task (default: NOT_STARTED)
   * @param subTasks - The initial sub-tasks of the task (default: [])
   * @returns A new task object
   */
  public static createTask(
    name: string,
    description: string,
    state: TaskState = TaskState.NOT_STARTED,
    subTasks: string[] = [],
  ): SerializedTask {
    return {
      uuid: crypto.randomUUID(),
      name,
      description,
      state,
      subTasks,
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };
  }

  /**
   * Creates a sub-task and adds it to the parent task.
   * @param parentTask - The parent task
   * @param name - The name of the sub-task
   * @param description - The description of the sub-task
   * @param state - The initial state of the sub-task (default: NOT_STARTED)
   * @returns A tuple containing the updated parent task and the new sub-task
   */
  public static createSubTask(
    parentTask: SerializedTask,
    name: string,
    description: string,
    state: TaskState = TaskState.NOT_STARTED,
  ): [SerializedTask, SerializedTask] {
    const subTask = this.createTask(name, description, state);
    const updatedParentTask = {
      ...parentTask,
      subTasks: [...parentTask.subTasks, subTask.uuid],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    return [updatedParentTask, subTask];
  }
}

/**
 * Utility functions for querying tasks.
 */
export class TaskQueries {
  /**
   * Filters tasks by state.
   * @param tasks - The tasks to filter
   * @param states - The states to filter by
   * @returns The filtered tasks
   */
  public static filterByState(
    tasks: SerializedTask[],
    states: TaskState[],
  ): SerializedTask[] {
    return tasks.filter((task) => states.includes(task.state));
  }

  /**
   * Gets active tasks (not cancelled or completed).
   * @param tasks - The tasks to filter
   * @returns The active tasks
   */
  public static getActiveTasks(tasks: SerializedTask[]): SerializedTask[] {
    return this.filterByState(tasks, [
      TaskState.NOT_STARTED,
      TaskState.IN_PROGRESS,
    ]);
  }

  /**
   * Gets completed tasks.
   * @param tasks - The tasks to filter
   * @returns The completed tasks
   */
  public static getCompletedTasks(tasks: SerializedTask[]): SerializedTask[] {
    return this.filterByState(tasks, [TaskState.COMPLETE]);
  }

  /**
   * Gets cancelled tasks.
   * @param tasks - The tasks to filter
   * @returns The cancelled tasks
   */
  public static getCancelledTasks(tasks: SerializedTask[]): SerializedTask[] {
    return this.filterByState(tasks, [TaskState.CANCELLED]);
  }

  /**
   * Filters tasks by last updated time range.
   * @param tasks - The tasks to filter
   * @param minTime - The minimum time (inclusive, optional)
   * @param maxTime - The maximum time (inclusive, optional)
   * @returns The filtered tasks
   */
  public static filterByTimeRange(
    tasks: SerializedTask[],
    minTime?: number,
    maxTime?: number,
  ): SerializedTask[] {
    return tasks.filter((task) => {
      if (minTime !== undefined && task.lastUpdated < minTime) {
        return false;
      }
      if (maxTime !== undefined && task.lastUpdated > maxTime) {
        return false;
      }
      return true;
    });
  }

  /**
   * Filters tasks by who last updated them.
   * @param tasks - The tasks to filter
   * @param updatedBy - Who last updated the tasks
   * @returns The filtered tasks
   */
  public static filterByUpdatedBy(
    tasks: SerializedTask[],
    updatedBy: TaskUpdatedBy,
  ): SerializedTask[] {
    return tasks.filter((task) => task.lastUpdatedBy === updatedBy);
  }
}

export function findTaskInTree(
  rootTask: HydratedTask,
  uuid: string,
): HydratedTask | undefined {
  if (rootTask.uuid === uuid) {
    return rootTask;
  }
  if (rootTask.subTasksData) {
    for (const subTask of rootTask.subTasksData) {
      const found = findTaskInTree(subTask, uuid);
      if (found) {
        return found;
      }
    }
  }
  return undefined;
}

export function toFlatTask(task: HydratedTask): HydratedTask[] {
  // DFS and collect all tasks
  const tasks: HydratedTask[] = [];
  const stack: HydratedTask[] = [task];
  while (stack.length > 0) {
    const currentTask = stack.pop()!;
    tasks.push(currentTask);
    if (currentTask.subTasksData) {
      stack.push(...currentTask.subTasksData);
    }
  }
  return tasks;
}

/**
 * Generates a markdown representation of a task.
 * @param task - The task to represent
 * @param shallow - If true, only include the task itself, not its subtasks (default: false)
 * @returns A string containing the markdown representation of the task
 */
export function getMarkdownRepresentation(
  task: HydratedTask,
  shallow: boolean = false,
): string {
  return _getMarkdownRepresentation(task, shallow).join("\n");
}

/**
 * Internal helper function to generate markdown representation of a task.
 * @param task - The task to represent
 * @param shallow - If true, only include the task itself, not its subtasks
 * @returns An array of strings, each representing a line in the markdown
 */
function _getMarkdownRepresentation(
  task: HydratedTask,
  shallow: boolean = false,
): string[] {
  const currentTaskMarkdown = `${STATE_MARKDOWN[task.state]} UUID:${task.uuid} NAME:${task.name} DESCRIPTION:${task.description}`;

  // If shallow is true or there are no subtasks, just return the current task
  if (shallow || !task.subTasksData || task.subTasksData.length === 0) {
    return [currentTaskMarkdown];
  }

  // Otherwise, include subtasks recursively
  const subtaskMarkdown = (task.subTasksData || [])
    .map((subtask) => {
      const subtaskMd = _getMarkdownRepresentation(subtask, shallow);
      return subtaskMd.map((line) => `-${line}`);
    })
    .flat();

  return [currentTaskMarkdown, ...subtaskMarkdown];
}

/**
 * Deep clone a task with new UUIDs
 *
 * @param task
 * @returns
 */
export function deepCloneTask(
  task: HydratedTask,
  options?: {
    keepUuid?: boolean;
  },
): HydratedTask {
  const subTasksData = task.subTasksData?.map((subtask) =>
    deepCloneTask(subtask, options),
  );
  return {
    ...task,
    uuid: options?.keepUuid ? task.uuid : crypto.randomUUID(),
    subTasks: subTasksData?.map((subtask) => subtask.uuid) || [],
    subTasksData,
  };
}

// Get serialized tasks to update
/**
 * Compare two task trees and identify created, updated, and deleted tasks.
 * @param oldTree - The original task tree
 * @param newTree - The updated task tree
 * @returns Object containing created, updated, and deleted tasks
 */
export function diffTaskTrees(
  oldTree: HydratedTask,
  newTree: HydratedTask,
): {
  created: HydratedTask[];
  updated: HydratedTask[];
  deleted: HydratedTask[];
} {
  const oldList = toFlatTask(oldTree);
  const newList = toFlatTask(newTree);

  // Create maps for quick lookup by UUID
  const uuidToOld = new Map(oldList.map((task) => [task.uuid, task]));
  const uuidToNew = new Map(newList.map((task) => [task.uuid, task]));

  // Get all tasks that are new (in newList but not in oldList)
  const newTasks = newList.filter((task) => !uuidToOld.has(task.uuid));

  // Get all tasks that are deleted (in oldList but not in newList)
  const deletedTasks = oldList.filter((task) => !uuidToNew.has(task.uuid));

  // Get all tasks that exist in both trees and have actually changed
  const updatedTasks: HydratedTask[] = [];
  for (const oldTask of oldList) {
    const newTask = uuidToNew.get(oldTask.uuid);
    if (newTask && hasTaskChanged(oldTask, newTask)) {
      updatedTasks.push(newTask);
    }
  }

  return {
    created: newTasks,
    updated: updatedTasks,
    deleted: deletedTasks,
  };
}

/**
 * Check if a task has actually changed by comparing relevant fields.
 * @param oldTask - The original task
 * @param newTask - The new task
 * @returns True if the task has changed, false otherwise
 */
function hasTaskChanged(oldTask: HydratedTask, newTask: HydratedTask): boolean {
  // Compare the fields that matter for task updates
  return (
    oldTask.name !== newTask.name ||
    oldTask.description !== newTask.description ||
    oldTask.state !== newTask.state ||
    // Compare subTasks arrays (order matters)
    JSON.stringify(oldTask.subTasks) !== JSON.stringify(newTask.subTasks)
  );
}

export function parseMarkdownToTaskTree(markdown: string): HydratedTask {
  if (!markdown.trim()) {
    throw new Error("Empty markdown");
  }

  const lines = markdown.split("\n");
  let rootTaskCount = 0;

  // First pass: count root tasks (level 0) to ensure there's only one
  for (const line of lines) {
    const trimmedLine = line.trim();
    if (!trimmedLine) continue;

    const indentLevel = getIndentationLevel(line);
    if (indentLevel === 0) {
      try {
        parseTaskLine(line, indentLevel);
        rootTaskCount++;
      } catch (error) {
        // Ignore invalid lines
      }
    }
  }

  if (rootTaskCount === 0) {
    throw new Error("No root task found");
  }

  if (rootTaskCount > 1) {
    throw new Error(
      `Multiple root tasks found (${rootTaskCount}). There can only be one root task per conversation. ` +
        `All other tasks must be subtasks (indented with dashes). ` +
        `Root task format: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (no dashes). ` +
        `Subtask format: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (with dashes).`,
    );
  }

  // Reset lines for actual parsing
  const parseLines = markdown.split("\n");
  function popNextValidTask():
    | {
        task: HydratedTask;
        level: number;
      }
    | undefined {
    while (parseLines.length > 0) {
      const line = parseLines.shift()!;
      const indentLevel = getIndentationLevel(line);
      try {
        return {
          task: parseTaskLine(line, indentLevel),
          level: indentLevel,
        };
      } catch (error) {
        // Ignore and try next line
      }
    }
    return undefined;
  }

  const rootTask = popNextValidTask();
  if (!rootTask) {
    throw new Error("No root task found");
  }
  const taskStack: HydratedTask[] = [rootTask.task];

  let currTaskInfo: { task: HydratedTask; level: number } | undefined;
  while ((currTaskInfo = popNextValidTask())) {
    const parentTask = taskStack[currTaskInfo.level - 1];
    if (!parentTask) {
      throw new Error(
        `Invalid markdown: level ${currTaskInfo.level + 1} has no parent\n` +
          `Line: ${currTaskInfo.task.name} is missing a parent\n` +
          `Current tasks: \n${getMarkdownRepresentation(rootTask.task)}`,
      );
    }

    if (!parentTask.subTasksData || !parentTask.subTasks) {
      parentTask.subTasks = [];
      parentTask.subTasksData = [];
    }

    parentTask.subTasksData.push(currTaskInfo.task);
    parentTask.subTasks.push(currTaskInfo.task.uuid);
    taskStack[currTaskInfo.level] = currTaskInfo.task;
    taskStack.splice(currTaskInfo.level + 1);
  }

  return rootTask.task;
}

function getIndentationLevel(line: string): number {
  // Count the number of dashes at the beginning of the line, after trim
  const match = line.trimStart().match(/^-+/);
  return match ? match[0].length : 0;
}

/**
 * Parse a line of markdown into a task.
 * @param line - The line to parse
 * @param level - The indentation level of the line
 * @returns The parsed task
 */
function parseTaskLine(line: string, level: number): HydratedTask {
  const cleanLine = line.trimStart().substring(level);

  // First, check if the line has a valid state marker
  const stateMatch = cleanLine.match(/^\[([ x\-/?])\]/);
  if (!stateMatch) {
    throw new Error(`Invalid task line: ${line} (missing state)`);
  }

  // Extract the state character
  const stateChar = stateMatch[1];

  // Create a reverse mapping from markdown markers to task states
  const markdownToState = Object.entries(STATE_MARKDOWN).reduce(
    (acc, [state, marker]) => {
      // Extract just the character inside the brackets
      const char = marker.substring(1, 2);
      acc[char] = state as TaskState;
      return acc;
    },
    {} as Record<string, TaskState>,
  );

  // Determine the task state based on the state character
  const state = markdownToState[stateChar] || TaskState.NOT_STARTED;

  // Regex pattern to extract UUID, name, and description
  // This pattern is more flexible with whitespace between fields
  // It also handles the case where there are no spaces between fields
  const fieldsPattern =
    /(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i;

  const fieldsMatch = cleanLine.match(fieldsPattern);

  if (!fieldsMatch) {
    // Check if it's missing required fields
    const hasUuid = /\b(?:uuid|UUID):/i.test(cleanLine);
    const hasName = /\b(?:name|NAME):/i.test(cleanLine);
    const hasDesc = /\b(?:description|DESCRIPTION):/i.test(cleanLine);

    if (!hasUuid || !hasName || !hasDesc) {
      throw new Error(`Invalid task line: ${line} (missing required fields)`);
    }

    // Check if the fields are in the wrong order
    const uuidPos = cleanLine.toLowerCase().indexOf("uuid:");
    const namePos = cleanLine.toLowerCase().indexOf("name:");
    const descPos = cleanLine.toLowerCase().indexOf("description:");

    if (!(uuidPos < namePos && namePos < descPos)) {
      throw new Error(`Invalid task line: ${line} (incorrect field order)`);
    }

    // If we get here, it's some other format issue
    throw new Error(`Invalid task line: ${line} (invalid format)`);
  }

  // Extract values from the regex match
  let uuid = fieldsMatch[1].trim();
  const name = fieldsMatch[2].trim();
  const description = fieldsMatch[3].trim();

  // Validate that UUID and name are not empty
  if (!uuid || !name) {
    throw new Error(`Invalid task line: ${line} (missing required fields)`);
  }

  // Use the UUID from the markdown. If it's exactly "NEW_UUID", generate a new one.
  // Otherwise, use the provided string (e.g., "NEW_UUID_123" or an actual UUID).
  if (uuid === "NEW_UUID") {
    uuid = crypto.randomUUID();
  }

  return {
    uuid,
    name,
    description,
    state,
    subTasks: [],
    lastUpdated: Date.now(),
    lastUpdatedBy: TaskUpdatedBy.USER,
  };
}

const createTaskFromPartial = (
  partialTask: Partial<HydratedTask>,
): HydratedTask => {
  return {
    uuid: crypto.randomUUID(),
    name: "New Task",
    description: "New task description",
    state: TaskState.NOT_STARTED,
    subTasks: [],
    lastUpdated: Date.now(),
    lastUpdatedBy: TaskUpdatedBy.USER,
    ...partialTask,
  };
};

const SAMPLE_TASK_1_1: HydratedTask = createTaskFromPartial({
  name: "Task 1.1",
  description: "This is the first sub task",
  state: TaskState.IN_PROGRESS,
});

const SAMPLE_TASK_1_2_1: HydratedTask = createTaskFromPartial({
  name: "Task 1.2.1",
  description: "This is a nested sub task, child of Task 1.2",
  state: TaskState.NOT_STARTED,
});

const SAMPLE_TASK_1_2_2: HydratedTask = createTaskFromPartial({
  name: "Task 1.2.2",
  description: "This is another nested sub task, child of Task 1.2",
  state: TaskState.IN_PROGRESS,
});

const SAMPLE_TASK_1_2: HydratedTask = createTaskFromPartial({
  name: "Task 1.2",
  description: "This is the second sub task",
  state: TaskState.COMPLETE,
  subTasks: [SAMPLE_TASK_1_2_1.uuid, SAMPLE_TASK_1_2_2.uuid],
  subTasksData: [SAMPLE_TASK_1_2_1, SAMPLE_TASK_1_2_2],
});

const SAMPLE_TASK_1_3: HydratedTask = createTaskFromPartial({
  name: "Task 1.3",
  description: "This is the third sub task",
  state: TaskState.CANCELLED,
});

const SAMPLE_TASK_1: HydratedTask = createTaskFromPartial({
  name: "Task 1",
  description: "This is the first task",
  state: TaskState.NOT_STARTED,
  subTasks: [SAMPLE_TASK_1_1.uuid, SAMPLE_TASK_1_2.uuid, SAMPLE_TASK_1_3.uuid],
  subTasksData: [SAMPLE_TASK_1_1, SAMPLE_TASK_1_2, SAMPLE_TASK_1_3],
});

export const SAMPLE_TASK_MARKDOWN = getMarkdownRepresentation(SAMPLE_TASK_1);

/**
 * Interface representing the diff results from diffTaskTrees
 */
export interface TaskTreeDiff {
  created: HydratedTask[];
  updated: HydratedTask[];
  deleted: HydratedTask[];
}

/**
 * Interface representing parsed diff counts
 */
export interface TaskDiffCounts {
  created: number;
  updated: number;
  deleted: number;
}

/**
 * Converts task tree diff results into a markdown format
 * @param diff - The diff results from diffTaskTrees
 * @returns Markdown string representing the changes
 */
export function taskDiffToMarkdown(diff: TaskTreeDiff): string {
  const sections: string[] = [];

  // Created tasks section
  if (diff.created.length > 0) {
    sections.push("## Created Tasks");
    sections.push("");
    for (const task of diff.created) {
      sections.push(
        `${STATE_MARKDOWN[task.state]} UUID:${task.uuid} NAME:${task.name} DESCRIPTION:${task.description}`,
      );
    }
    sections.push("");
  }

  // Updated tasks section
  if (diff.updated.length > 0) {
    sections.push("## Updated Tasks");
    sections.push("");
    for (const task of diff.updated) {
      sections.push(
        `${STATE_MARKDOWN[task.state]} UUID:${task.uuid} NAME:${task.name} DESCRIPTION:${task.description}`,
      );
    }
    sections.push("");
  }

  // Deleted tasks section
  if (diff.deleted.length > 0) {
    sections.push("## Deleted Tasks");
    sections.push("");
    for (const task of diff.deleted) {
      sections.push(
        `${STATE_MARKDOWN[task.state]} UUID:${task.uuid} NAME:${task.name} DESCRIPTION:${task.description}`,
      );
    }
    sections.push("");
  }

  return sections.join("\n");
}

/**
 * Parses markdown format and extracts task data from diff sections
 * @param markdown - The markdown string containing task changes
 * @returns Object with arrays of tasks by category
 */
export function parseTaskDiffMarkdown(markdown: string): TaskTreeDiff {
  const lines = markdown.split("\n");
  let currentSection: "created" | "updated" | "deleted" | null = null;
  const result = {
    created: [] as HydratedTask[],
    updated: [] as HydratedTask[],
    deleted: [] as HydratedTask[],
  };

  for (const line of lines) {
    const trimmedLine = line.trim();

    // Check for section headers
    if (trimmedLine === "## Created Tasks") {
      currentSection = "created";
      continue;
    } else if (trimmedLine === "## Updated Tasks") {
      currentSection = "updated";
      continue;
    } else if (trimmedLine === "## Deleted Tasks") {
      currentSection = "deleted";
      continue;
    }

    // Parse task lines
    if (
      currentSection &&
      (trimmedLine.startsWith("[ ]") ||
        trimmedLine.startsWith("[/]") ||
        trimmedLine.startsWith("[x]") ||
        trimmedLine.startsWith("[-]"))
    ) {
      try {
        const task = parseTaskLine(trimmedLine, 0); // Level 0 for flat diff display
        if (task) {
          result[currentSection].push(task);
        }
      } catch (error) {
        // Skip invalid task lines
      }
    }
  }

  return result;
}

/**
 * Extracts task counts from full tool result text
 * @param text - The full tool result text containing counts and task changes
 * @returns Object with created, updated, deleted counts
 */
export function getTaskDiffCounts(text: string): TaskDiffCounts {
  // First try to extract from the summary line (e.g., "Created: 2, Updated: 1, Deleted: 0")
  const match = text.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);
  if (match) {
    return {
      created: parseInt(match[1], 10),
      updated: parseInt(match[2], 10),
      deleted: parseInt(match[3], 10),
    };
  }

  // Fallback to parsing the markdown section
  const markdown = extractTaskDiffMarkdown(text);
  const diff = parseTaskDiffMarkdown(markdown);
  return {
    created: diff.created.length,
    updated: diff.updated.length,
    deleted: diff.deleted.length,
  };
}

/**
 * Extracts the task diff markdown section from full tool result text
 * @param text - The full tool result text
 * @returns The markdown section containing task changes
 */
export function extractTaskDiffMarkdown(text: string): string {
  const startMarker = "# Task Changes";
  const startIndex = text.indexOf(startMarker);
  if (startIndex === -1) return "";

  const afterStart = text.substring(startIndex);
  const endMarkers = ["\nNew and Updated Tasks:", "\nRemember:", "\n\n---"];

  let endIndex = afterStart.length;
  for (const marker of endMarkers) {
    const markerIndex = afterStart.indexOf(marker);
    if (markerIndex !== -1 && markerIndex < endIndex) {
      endIndex = markerIndex;
    }
  }

  const fullSection = afterStart.substring(0, endIndex);
  const contentStart = fullSection.indexOf("\n");
  if (contentStart === -1) return "";

  return fullSection.substring(contentStart + 1).trim();
}

/**
 * Parses full tool result text and extracts task data from diff sections
 * @param text - The full tool result text containing task changes
 * @returns Object with arrays of tasks by category
 */
export function parseTaskDiffFromFullText(text: string): TaskTreeDiff {
  const markdown = extractTaskDiffMarkdown(text);
  return parseTaskDiffMarkdown(markdown);
}

/**
 * Utility functions for generating instructional text for task tools.
 * These functions provide consistent messaging across different task tools.
 */
export class TaskInstructionUtils {
  /**
   * Generates the main task list instructions for the ViewTaskListTool.
   * @returns Formatted instruction text for viewing and updating task lists
   */
  public static getTaskListInstructions(): string {
    return (
      `To update this task list, use the update_tasks tool. To add new tasks, use the add_tasks tool.\n\n` +
      `IMPORTANT: When updating the task list:\n` +
      `1. Maintain the proper hierarchy with correct indentation:\n` +
      `   - Root tasks have no dashes: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
      `   - Level 1 tasks have one dash: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
      `   - Level 2 tasks have two dashes: --[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
      `   - Every sub-task MUST have a parent task one level above it\n` +
      `   - New tasks should use UUID:NEW_UUID\n\n` +
      `2. Use the correct task state markers:\n` +
      `   - [ ] = Not started (for tasks you haven't begun working on yet)\n` +
      `   - [/] = In progress (for tasks you're currently working on)\n` +
      `   - [-] = Cancelled (for tasks that are no longer relevant)\n` +
      `   - [x] = Completed (for tasks the user has confirmed are complete)\n\n` +
      `3. Update task states as you work:\n` +
      `   - Start with tasks in the not started state ([ ])\n` +
      `   - Mark tasks as in progress ([/]) when you start working on them\n` +
      `   - Mark tasks as completed ([x]) when they are done\n` +
      `   - Mark tasks as cancelled ([-]) if they're no longer needed`
    );
  }

  /**
   * Generates help text for markdown parsing errors in BulkUpdateTaskListTool.
   * @returns Formatted help text for fixing markdown parsing issues
   */
  public static getMarkdownParsingHelpText(): string {
    return (
      `IMPORTANT: Make sure each task follows the correct hierarchy:\n` +
      `- There can only be ONE root task (no dashes): [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
      `- Level 1 tasks have one dash: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
      `- Level 2 tasks have two dashes: --[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
      `- Every task at level N (N > 0) MUST have a parent task at level N-1\n` +
      `- Do not skip levels (e.g., going from root to -- without a - level)\n` +
      `- Ensure all tasks have a UUID, NAME, and DESCRIPTION in that order\n` +
      `- Use the correct state marker: [ ] = not started, [/] = in progress, [-] = cancelled, [x] = completed\n` +
      `- Remember to update task states as you work: mark as in progress ([/]) when starting, mark as completed ([x]) when finished, and mark as cancelled ([-]) if they're no longer needed.`
    );
  }

  /**
   * Generates reminder text for future task list updates.
   * @returns Formatted reminder text for task state management
   */
  public static getTaskUpdateReminder(): string {
    return (
      `Remember: When updating the task list in the future:\n` +
      `- Mark tasks as in progress ([/]) when you start working on them\n` +
      `- Mark tasks as completed ([x]) when the user explicitly confirms they are done\n` +
      `- Mark tasks as cancelled ([-]) if they're no longer needed\n` +
      `- Use the correct UUID for existing tasks. Make sure to use the generated UUIDs for new tasks.`
    );
  }

  /**
   * Formats a complete task list view response with instructions.
   * @param markdown - The task list markdown content
   * @returns Complete formatted response for ViewTaskListTool
   */
  public static formatTaskListViewResponse(markdown: string): string {
    return `# Current Task List\n\n${markdown}\n\n${this.getTaskListInstructions()}`;
  }

  /**
   * Formats a bulk update success response with diff information.
   * @param taskDiff - The diff results from diffTaskTrees
   * @returns Complete formatted response for ReorganizeTaskListTool
   */
  public static formatBulkUpdateResponse(taskDiff: TaskTreeDiff): string {
    const diffMarkdown = taskDiffToMarkdown(taskDiff);
    const { created, updated, deleted } = taskDiff;
    const createdCount = created.length;
    const updatedCount = updated.length;
    const deletedCount = deleted.length;

    let responseMessage = `Task list updated successfully. Created: ${createdCount}, Updated: ${updatedCount}, Deleted: ${deletedCount}.\n\n`;
    if (diffMarkdown.trim()) {
      responseMessage += `# Task Changes\n\n${diffMarkdown}\n\n`;
    }

    responseMessage += this.getTaskUpdateReminder();
    return responseMessage;
  }

  /**
   * Tool descriptions for consistent messaging across task tools.
   */
  public static getToolDescriptions() {
    return {
      viewTaskList: "View the current task list for the conversation.",

      updateTasks:
        "Update one or more tasks' properties (state, name, description). Can update a single task or multiple tasks in one call.",

      addTasks:
        "Add one or more new tasks to the task list. Can add a single task or multiple tasks in one call. Tasks can be added as subtasks or after specific tasks.",
      reorganizeTaskList:
        "Reorganize the task list structure for the current conversation. Use this only for major restructuring like reordering tasks, changing hierarchy. For individual task updates, use update_tasks tool.",
    };
  }
}

export const testOnlyFunctions = {
  _parseTaskLine: parseTaskLine,
  _parseMarkdownToTaskTree: parseMarkdownToTaskTree,
  _getMarkdownRepresentation: getMarkdownRepresentation,
};
