import { Chat<PERSON><PERSON>, ChatRequestNode, Exchange } from "../chat/chat-types";
import { getLogger } from "../logging";
import { RemoteToolId, ToolDefinition } from "../tools/tool-types";
import {
  AgentCodebaseRetrievalResult,
  AgentRequestEvent,
  AgentSessionEvent,
  ChatResult,
  RemoteAgentSessionEvent,
  ToolUseRequestEvent,
} from "./api-client-types";

/**
 * This interface covers methods relating to Augment API calls.
 */
export interface IAPIClient {
  // For chatStream, blobs are added on the client side.
  // This interface is much smaller than the actual chatStream api endpoint
  // because this is all we need for sidecar right now.
  chatStream(
    message: string,
    requestId: string,
    chatHistory: Exchange[],
    toolDefinitions: ToolDefinition[],
    requestNodes: ChatRequestNode[],
    mode: ChatMode,
    modelId: string | undefined,
    silent: boolean,
  ): Promise<AsyncIterable<ChatResult>>;

  // For agentCodebaseRetrieval, blobs are added on the client side.
  agentCodebaseRetrieval(
    toolRequestId: string,
    informationRequest: string,
    chatHistory: Exchange[],
    maxOutputLength: number,
    signal?: AbortSignal,
  ): Promise<AgentCodebaseRetrievalResult>;
  logToolUseRequestEvent(events: ToolUseRequestEvent[]): Promise<void>;
  logAgentRequestEvent(events: AgentRequestEvent[]): Promise<void>;
  logAgentSessionEvent(events: AgentSessionEvent[]): Promise<void>;
  logRemoteAgentSessionEvent(events: RemoteAgentSessionEvent[]): Promise<void>;
  checkToolSafety(
    toolId: RemoteToolId,
    toolInputJson: string,
  ): Promise<boolean>;
}

class APIClientSingleton {
  private static _instance: IAPIClient | undefined = undefined;

  static setAPIClient(cw: IAPIClient) {
    if (this._instance !== undefined) {
      const logger = getLogger("APICLient");
      logger.warn(
        "Attempting to initialize API clientwhen one is already configured. Keeping existing client workspaces.",
      );
      return;
    }

    this._instance = cw;
  }

  static getAPIClient(): IAPIClient {
    if (this._instance === undefined) {
      throw new Error("API Client not set");
    }
    return this._instance;
  }

  static reset() {
    this._instance = undefined;
  }
}

// Some shorthand functions to make usage a bit easier
export const setLibraryAPIClient = (c: IAPIClient) =>
  APIClientSingleton.setAPIClient(c);
export const getAPIClient = () => APIClientSingleton.getAPIClient();
export const resetLibraryAPIClient = () => APIClientSingleton.reset();
