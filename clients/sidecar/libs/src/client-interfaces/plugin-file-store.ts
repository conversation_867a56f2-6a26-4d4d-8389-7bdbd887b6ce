import { getLogger } from "../logging";

/**
 * This interface covers methods relating to the management of files created
 * and managed by the plugin itself (i.e. not user files).
 */
export interface IPluginFileStore {
  saveAsset: (path: string, content: Uint8Array) => Promise<void>;
  loadAsset: (path: string) => Promise<Uint8Array | undefined>;
  deleteAsset(path: string): Promise<void>;
}

class PluginFileStoreSingleton {
  private static _instance: IPluginFileStore | undefined = undefined;

  static setPluginFileStore(cw: IPluginFileStore) {
    if (this._instance !== undefined) {
      const logger = getLogger("PluginFileStore");
      logger.warn(
        "Attempting to initialize client workspaces when one is already configured. Keeping existing client workspaces.",
      );
      return;
    }

    this._instance = cw;
  }

  static getPluginFileStore(): IPluginFileStore {
    if (this._instance === undefined) {
      throw new Error("PluginFileStore not set");
    }
    return this._instance;
  }

  static reset() {
    this._instance = undefined;
  }
}

// Some shorthand functions to make usage a bit easier
export const setLibraryPluginFileStore = (c: IPluginFileStore) =>
  PluginFileStoreSingleton.setPluginFileStore(c);
export const getPluginFileStore = () =>
  PluginFileStoreSingleton.getPluginFileStore();
export const resetLibraryPluginFileStore = () =>
  PluginFileStoreSingleton.reset();
