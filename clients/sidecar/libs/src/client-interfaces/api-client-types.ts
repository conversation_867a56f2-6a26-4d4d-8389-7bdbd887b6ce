import { ChatResultNode } from "../chat/chat-types";
import {
  AgentRequestEventName,
  AgentSessionEventName,
  AgentTracingData,
  ClassifyAndDistillDebugFlag,
  FlushMemoriesDebugFlag,
  InitialOrientationCaller,
  InitialOrientationDebugFlag,
  RememberToolCallDebugFlag,
  RememberToolCaller,
  RemoteAgentSetupState,
  RemoteAgentSessionEventName,
  InteractionType,
  NotificationBellState,
  MemoriesMoveTarget,
  RulesImportedType,
} from "../metrics/types";

export type ChatResult = {
  text: string;
  unknownBlobNames?: string[];
  checkpointNotFound?: boolean;
  workspaceFileChunks?: WorkspaceFileChunk[];
  nodes?: ChatResultNode[];
};

export interface WorkspaceFileChunk {
  charStart: number;
  charEnd: number;
  blobName: string;
  file?: FileDetails;
}

export interface FileDetails {
  repoRoot: string;
  pathName: string;
  fileType?: FileType;
  uriScheme?: string;
  // The display range of the file. Expressed with 1-indexing for historical reasons.
  // Only contains the start and end lines, 1-indexed.
  range?: {
    start: number;
    stop: number;
  };
  // These are the full range of the file, expressed with 0-indexing.
  // The full range includes the start and end lines, as well as the
  // start and end character columns.
  fullRange?: {
    startLineNumber: number;
    startColumn: number;
    endLineNumber: number;
    endColumn: number;
  };
  originalCode?: string;
  modifiedCode?: string;
  lineChanges?: LineChanges;
  // Whether actions on this file should be triggered from a different tab.
  differentTab?: boolean;
  requestId?: string;
  suggestionId?: string;
  // The snippet to highlight in the file.
  snippet?: string;
}

export interface LineChanges {
  lineChanges: LineChange[];
  lineOffset: number;
}

export interface LineChange {
  originalStart: number;
  originalEnd: number;
  modifiedStart: number;
  modifiedEnd: number;
}

export enum FileType {
  directory = "Directory",
  file = "File",
  other = "Other",
}

export type AgentCodebaseRetrievalResult = {
  formattedRetrieval: string;
};

export type ToolUseRequestEvent = {
  requestId: string;
  toolName: string;
  toolUseId: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  toolInput: Record<string, any>;
  toolOutputIsError: boolean;
  toolRunDurationMs: number;
  isMcpTool: boolean;
  conversationId: string;
  chatHistoryLength: number;
  toolRequestId?: string;
  toolOutputLen?: number;
  eventTimeSec: number;
  eventTimeNsec: number;
};

export type ToolUseRequestEventPayload = {
  tool_name: string;
  tool_use_id: string;
  tool_input: string;
  tool_output_is_error: boolean;
  tool_run_duration_ms: number;
  is_mcp_tool: boolean;
  conversation_id: string;
  chat_history_length: number;
  tool_request_id?: string;
  tool_output_len?: number;
  tool_input_len?: number;
};

/**
 * AgentSessionEvent is an agent session event that can be logged.
 */
export type MemoriesFileOpenData = {
  memories_path_undefined?: boolean;
};

export type MemoriesMoveData = {
  target?: MemoriesMoveTarget;
};

export type RulesImportedData = {
  type?: RulesImportedType;
  num_files?: number;
  source?: string;
};

export type AgentSessionEvent = {
  event_time_sec: number;
  event_time_nsec: number;
  event_name: AgentSessionEventName;
  conversation_id: string;
  event_data?: {
    agent_reversion_data?: AgentReversionData;
    agent_interruption_data?: AgentInterruptionData;
    remember_tool_call_data?: RememberToolCallData;
    memories_file_open_data?: MemoriesFileOpenData;
    initial_orientation_data?: InitialOrientationData;
    classify_and_distill_data?: ClassifyAndDistillData;
    flush_memories_data?: FlushMemoriesData;
    memories_move_data?: MemoriesMoveData;
    rules_imported_data?: RulesImportedData;
  };
};

/**
 * AgentRequestEvent is an agent request event that can be logged.
 * This is different from AgentSessionEvent as it's tied to specific requests rather than sessions.
 */
export type AgentRequestEvent = {
  event_time_sec: number;
  event_time_nsec: number;
  event_name: AgentRequestEventName;
  conversation_id: string;
  request_id: string;
  chat_history_length: number;
};

export type AgentReversionData = {
  // Leaving this object empty for now as the fields are still in flux.
  // See comment in `clients/vscode/src/metrics/types.ts` for context.
};

export type AgentInterruptionData = {
  request_id: string;
  curr_conversation_length: number;
};

export type RememberToolCallData = {
  caller: RememberToolCaller;
  is_complex_new_memory: boolean;
  tracing_data: AgentTracingData<RememberToolCallDebugFlag>;
};

export type InitialOrientationData = {
  caller: InitialOrientationCaller;
  tracing_data: AgentTracingData<InitialOrientationDebugFlag>;
};

export type ClassifyAndDistillData = {
  tracing_data: AgentTracingData<ClassifyAndDistillDebugFlag>;
};

export type FlushMemoriesData = {
  tracing_data: AgentTracingData<FlushMemoriesDebugFlag>;
};

export type RemoteAgentSessionEvent = {
  event_time_sec: number;
  event_time_nsec: number;
  event_name: RemoteAgentSessionEventName;
  remote_agent_id: string;
  event_data?: {
    remote_agent_setup_data?: RemoteAgentSetupData;
    setup_script_data?: RemoteAgentSetupScriptData;
    ssh_interaction_data?: SSHInteractionData;
    notification_bell_data?: NotificationBellData;
    diff_panel_data?: DiffPanelData;
    setup_page_opened?: SetupPageOpened;
    github_api_failure?: GithubAPIFailure;
    remote_agent_created?: RemoteAgentCreated;
  };
};

export type RemoteAgentSetupData = {
  used_generated_setup_script: boolean;
  setup_state: RemoteAgentSetupState;
};

export type RemoteAgentSetupScriptData = {
  num_tries: number;
  num_messages_sent: number;
  generation_time_ms: number;
  manual_modification: boolean;
};

export type SSHInteractionData = {
  interaction_type: InteractionType;
};

export type NotificationBellData = {
  bell_state: NotificationBellState;
};

export type DiffPanelData = {
  loading_time_ms: number;
  applied: boolean;
};

export type SetupPageOpened = {};

export type GithubAPIFailure = {
  error_code: number;
};

export type RemoteAgentCreated = {
  changed_repo: boolean;
  changed_branch: boolean;
};
