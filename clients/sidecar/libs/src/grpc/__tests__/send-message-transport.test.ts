import {
  SendMessageTransport,
  GrpcSendMessageTransportPayload,
} from "../send-message-transport";
import { MessageTarget } from "../message-target";
import {
  TestResponseSchema,
  TestRequestSchema,
  TestService,
} from "@augment-internal/sidecar-libs/protos/test_service_pb";
import { createClient } from "@connectrpc/connect";
import { create, fromJson, toJson, type JsonValue } from "@bufbuild/protobuf";

// Mock implementation of MessageTarget for testing
class MockMessageTarget implements MessageTarget {
  public messageCallback:
    | ((message: GrpcSendMessageTransportPayload) => void)
    | null = null;
  public sentMessages: GrpcSendMessageTransportPayload[] = [];

  sendMessage(message: GrpcSendMessageTransportPayload): void {
    this.sentMessages.push(message);

    // If this is a request message, simulate the other side responding
    if (
      this.messageCallback &&
      typeof message === "object" &&
      message !== null
    ) {
      const grpcMessage = message;
      if (grpcMessage.type === "com.augmentcode.client.rpc.request") {
        // Simulate some processing time
        setTimeout(() => {
          this.simulateResponse(grpcMessage);
        }, 10);
      }
    }
  }

  onReceiveMessage(
    callback: (message: GrpcSendMessageTransportPayload) => void,
  ): () => void {
    this.messageCallback = callback;
    return () => {
      this.messageCallback = null;
    };
  }

  // Helper method to simulate receiving a response
  simulateResponse(requestMessage: GrpcSendMessageTransportPayload): void {
    if (!this.messageCallback) return;

    // Create a response based on the request
    const responseMessage: GrpcSendMessageTransportPayload = {
      type: "com.augmentcode.client.rpc.response",
      id: requestMessage.id,
      methodLocalName: requestMessage.methodLocalName,
      serviceTypeName: requestMessage.serviceTypeName,
      data: "",
    };

    // Handle different test methods
    if (requestMessage.methodLocalName === "testMethod") {
      // Deserialize the request payload
      const request = fromJson(
        TestRequestSchema,
        requestMessage.data as JsonValue,
      );

      // Create a response
      const response = create(TestResponseSchema, {
        result: `Processed ${request.foo}`,
      });

      // Serialize the response
      responseMessage.data = toJson(TestResponseSchema, response);
    } else if (requestMessage.methodLocalName === "errorMethod") {
      // Simulate an error response
      responseMessage.error = "Intentional error from test";
    }

    // Send the response
    this.messageCallback(responseMessage);
  }

  // Helper to clear sent messages (for test setup)
  clearSentMessages(): void {
    this.sentMessages = [];
  }
}

describe("SendMessageTransport", () => {
  let mockTarget: MockMessageTarget;
  let transport: SendMessageTransport;

  beforeEach(() => {
    // Set up a fresh mock target and transport for each test
    mockTarget = new MockMessageTarget();
    transport = new SendMessageTransport(mockTarget);

    // Mock crypto.randomUUID to return predictable values for testing
    jest
      .spyOn(crypto, "randomUUID")
      .mockImplementation(
        () =>
          "test-uuid-1234-5678-9abc-def0123456789" as `${string}-${string}-${string}-${string}-${string}`,
      );
  });

  afterEach(() => {
    jest.restoreAllMocks();
    transport.dispose();
  });

  test("should create a transport with the given message target", () => {
    expect(transport).toBeDefined();
    expect(transport["target"]).toBe(mockTarget);
  });

  test("should make unary RPC calls", async () => {
    // Create a client using the transport
    const client = createClient(TestService, transport);

    // Make a request
    const response = await client.testMethod({ foo: "test-value" });

    // Verify the response
    expect(response.result).toBe("Processed test-value");

    // Verify the message was sent
    expect(mockTarget.sentMessages.length).toBe(1);
    expect(mockTarget.sentMessages[0]).toMatchObject({
      type: "com.augmentcode.client.rpc.request",
      id: "test-uuid-1234-5678-9abc-def0123456789",
      methodLocalName: "testMethod",
      serviceTypeName: "test.TestService",
      data: { foo: "test-value" },
    });
  });

  test("should handle errors from RPC calls", async () => {
    // Create a client using the transport
    const client = createClient(TestService, transport);

    // Make a request that will result in an error
    await expect(client.errorMethod({})).rejects.toThrow(
      "Intentional error from test",
    );

    // Verify the message was sent
    expect(mockTarget.sentMessages.length).toBe(1);
    expect(mockTarget.sentMessages[0]).toMatchObject({
      type: "com.augmentcode.client.rpc.request",
      id: "test-uuid-1234-5678-9abc-def0123456789",
      methodLocalName: "errorMethod",
      serviceTypeName: "test.TestService",
    });
  });

  test("should handle request timeouts", async () => {
    // Create a client using the transport
    const client = createClient(TestService, transport);

    // Override the simulateResponse method to not respond
    mockTarget.simulateResponse = jest.fn();

    // Make a request with a short timeout
    await expect(
      client.testMethod({ foo: "test-value" }, { timeoutMs: 50 }),
    ).rejects.toThrow("Request timed out after 50ms");

    // Verify the message was sent
    expect(mockTarget.sentMessages.length).toBe(1);
  });

  test("should handle request abortion", async () => {
    // Create a client using the transport
    const client = createClient(TestService, transport);

    // Create an abort controller
    const controller = new AbortController();

    // Override the simulateResponse method to not respond
    mockTarget.simulateResponse = jest.fn();

    // Start the request
    const promise = client.testMethod(
      { foo: "test-value" },
      { signal: controller.signal },
    );

    // Abort the request
    controller.abort();

    // Verify the request was aborted
    await expect(promise).rejects.toThrow("Request aborted");
  });

  test("should ignore non-gRPC messages", () => {
    // Send a non-gRPC message
    // @ts-expect-error: Testing invalid message
    mockTarget.messageCallback?.({ type: "not-a-grpc-message" });

    // Verify no messages were sent in response
    expect(mockTarget.sentMessages.length).toBe(0);
  });

  test("should properly clean up resources during disposal", () => {
    // Create a new transport for this test
    const localMockTarget = new MockMessageTarget();
    const localTransport = new SendMessageTransport(localMockTarget);

    // Set up some pending requests with timeouts
    const pendingRequests = localTransport["pendingRequests"];

    // Add a mock pending request with a timeout
    const mockTimeout = setTimeout(() => {}, 1000);
    pendingRequests.set("test-id", {
      resolve: jest.fn(),
      reject: jest.fn(),
      timeout: mockTimeout,
    });

    // Spy on clearTimeout
    const clearTimeoutSpy = jest.spyOn(global, "clearTimeout");

    // Dispose the transport
    localTransport.dispose();

    // Verify that clearTimeout was called
    expect(clearTimeoutSpy).toHaveBeenCalledWith(mockTimeout);

    // Verify that the pending requests were cleared
    expect(pendingRequests.size).toBe(0);

    // Clean up
    clearTimeoutSpy.mockRestore();
    clearTimeout(mockTimeout);
  });

  test("should handle multiple dispose calls safely", () => {
    // Create a new transport for this test
    const localMockTarget = new MockMessageTarget();
    const localTransport = new SendMessageTransport(localMockTarget);

    // Call dispose multiple times
    localTransport.dispose();
    localTransport.dispose(); // Should not throw

    // Verify the transport is disposed
    expect(localTransport["pendingRequests"].size).toBe(0);
  });
});
