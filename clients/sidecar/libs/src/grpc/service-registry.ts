import {
  ServiceImpl,
  ServiceImplSpec,
  createServiceImplSpec,
  createHandlerContext,
} from "@connectrpc/connect";
import {
  Message,
  fromJson,
  toJson,
  JsonValue,
  DescService,
} from "@bufbuild/protobuf";
import type {
  GrpcSendMessageTransportPayload,
  GrpcServiceTransport,
} from "./send-message-transport";

export class ServiceRegistry {
  public static readonly PROTOCOL_NAME = "com.augmentcode.client.rpc";

  private readonly services = new Map<string, ServiceImplSpec>();

  /**
   * Creates a new DefaultServiceRegistry
   */
  constructor() {}

  /**
   * Creates a new ServiceRegistry and adds it to the given transport
   *
   * @param transport
   * @returns
   */
  public static withGrpcServiceTransport(
    transport: GrpcServiceTransport,
  ): ServiceRegistry {
    const registry = new ServiceRegistry();
    transport.addServiceRegistry(registry);
    return registry;
  }

  /**
   * Registers a service implementation with this registry
   *
   * @param serviceDesc The protobuf service descriptor
   * @param impl The implementation of the service methods
   */
  registerService<T extends DescService>(
    serviceDesc: T,
    impl: ServiceImpl<T>,
  ): void {
    const serviceImplSpec = createServiceImplSpec(serviceDesc, impl);
    this.services.set(serviceDesc.typeName, serviceImplSpec);
  }

  /**
   * Gets a service implementation by name
   *
   * @param serviceTypeName The name of the service
   * @returns The service implementation spec, or undefined if not found
   */
  getService(serviceTypeName: string): ServiceImplSpec | undefined {
    return this.services.get(serviceTypeName);
  }

  /**
   * Clears all registered services
   */
  clear(): void {
    this.services.clear();
  }

  /**
   * Checks if this handler can handle the given message
   *
   * @param message The gRPC message to check
   * @returns True if this handler can handle the message, false otherwise
   */
  canHandle(message: GrpcSendMessageTransportPayload): boolean {
    if (message.type !== "com.augmentcode.client.rpc.request") {
      return false;
    }

    const service = this.services.get(message.serviceTypeName);
    if (!service) {
      return false;
    }

    const method = service.methods[message.methodLocalName];
    return !!method;
  }

  /**
   * Handles an incoming gRPC message
   *
   * @param message The gRPC message to handle
   * @param sendResponse A function to send a response
   * @returns A promise that resolves when the message has been handled
   * @throws An error if the message cannot be handled
   */
  async handleRequest(
    message: GrpcSendMessageTransportPayload,
    sendResponse: (response: GrpcSendMessageTransportPayload) => void,
  ): Promise<void> {
    if (!this.canHandle(message)) {
      sendResponse({
        type: "com.augmentcode.client.rpc.response",
        id: message.id,
        methodLocalName: message.methodLocalName,
        serviceTypeName: message.serviceTypeName,
        data: "",
        error: `Service not registered: ${message.serviceTypeName}`,
      });
      return;
    }

    const service = this.services.get(message.serviceTypeName)!;
    const method = service.methods[message.methodLocalName];

    try {
      switch (method.kind) {
        case "unary": {
          const { impl, method: methodDesc } = method;

          // Deserialize the request payload
          const request = fromJson(methodDesc.input, message.data as JsonValue);
          const context = createHandlerContext({
            service: service.service,
            method: method.method,
            protocolName: ServiceRegistry.PROTOCOL_NAME,
            requestMethod: "POST", // Not used
            url: "", // Not used
          });

          // Call the implementation
          const response = (await impl(request, context)) as Message<string>;

          // Serialize the response
          sendResponse({
            type: "com.augmentcode.client.rpc.response",
            id: message.id,
            methodLocalName: message.methodLocalName,
            serviceTypeName: message.serviceTypeName,
            data: toJson(methodDesc.output, response),
          });
          break;
        }
        case "server_streaming":
          throw new Error("Server streaming is not supported");
        case "client_streaming":
          throw new Error("Client streaming is not supported");
        case "bidi_streaming":
          throw new Error("Bi-directional streaming is not supported");
      }
    } catch (error) {
      sendResponse({
        type: "com.augmentcode.client.rpc.response",
        id: message.id,
        methodLocalName: message.methodLocalName,
        serviceTypeName: message.serviceTypeName,
        data: "",
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Disposes of the service registry, cleaning up any resources
   */
  dispose(): void {
    this.clear();
  }
}
