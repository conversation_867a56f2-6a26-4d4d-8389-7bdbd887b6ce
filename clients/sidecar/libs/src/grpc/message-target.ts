/**
 * Interface for a message target that can send and receive messages.
 * This abstraction allows the transport layer to be agnostic of the
 * underlying communication mechanism (e.g., VSCode webview, browser postMessage).
 */
export interface MessageTarget<T = unknown> {
  /**
   * Sends a message to the target.
   * @param message The message to send
   */
  sendMessage(message: T): void;

  /**
   * Registers a callback to be called when a message is received.
   * @param callback The callback to call when a message is received
   * @returns A function that can be called to unregister the callback
   */
  onReceiveMessage(callback: (message: T) => void): () => void;
}
