// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file clients/sidecar/node-process/protos/plugin-file-store.proto (package com.augmentcode.sidecar.rpc.clientInterfaces, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.SaveAsset
 */
export declare class SaveAsset extends Message<SaveAsset> {
  /**
   * @generated from field: string path = 1;
   */
  path: string;

  /**
   * @generated from field: bytes contents = 2;
   */
  contents: Uint8Array;

  constructor(data?: PartialMessage<SaveAsset>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.SaveAsset";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SaveAsset;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SaveAsset;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SaveAsset;

  static equals(a: SaveAsset | PlainMessage<SaveAsset> | undefined, b: SaveAsset | PlainMessage<SaveAsset> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.LoadAsset
 */
export declare class LoadAsset extends Message<LoadAsset> {
  /**
   * @generated from field: string path = 1;
   */
  path: string;

  constructor(data?: PartialMessage<LoadAsset>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.LoadAsset";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LoadAsset;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LoadAsset;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LoadAsset;

  static equals(a: LoadAsset | PlainMessage<LoadAsset> | undefined, b: LoadAsset | PlainMessage<LoadAsset> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.LoadAssetResponse
 */
export declare class LoadAssetResponse extends Message<LoadAssetResponse> {
  /**
   * @generated from field: bytes contents = 1;
   */
  contents: Uint8Array;

  constructor(data?: PartialMessage<LoadAssetResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.LoadAssetResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LoadAssetResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LoadAssetResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LoadAssetResponse;

  static equals(a: LoadAssetResponse | PlainMessage<LoadAssetResponse> | undefined, b: LoadAssetResponse | PlainMessage<LoadAssetResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.DeleteAsset
 */
export declare class DeleteAsset extends Message<DeleteAsset> {
  /**
   * @generated from field: string path = 1;
   */
  path: string;

  constructor(data?: PartialMessage<DeleteAsset>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.DeleteAsset";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteAsset;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteAsset;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteAsset;

  static equals(a: DeleteAsset | PlainMessage<DeleteAsset> | undefined, b: DeleteAsset | PlainMessage<DeleteAsset> | undefined): boolean;
}

