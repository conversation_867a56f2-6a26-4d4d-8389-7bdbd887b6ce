import { TextDocument } from "vscode-languageserver-textdocument";
import {
  createConnection,
  InitializeParams,
  InitializeResult,
  TextDocuments,
} from "vscode-languageserver/node";
import { ChatMode as ProtoChatMode } from "$clients/sidecar/node-process/protos/tools_pb";

import { getLogger } from "./logging";
import { registerToolMethods } from "./tools/tool-methods";
import { InitializeParams as InitializeParamsProto } from "$clients/sidecar/node-process/protos/sidecarrpc_pb";
import { JsonValue } from "@bufbuild/protobuf";
import { clientFeatureFlags } from "./client-interfaces/client-feature-flags";
import { ClientWorkspaces } from "./client-interfaces/client-workspaces";
import { TextDocumentChangeEvent } from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { setLibraryClientWorkspaces } from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import { AggregateCheckpointManager } from "@augment-internal/sidecar-libs/src/agent/checkpoint/aggregate-checkpoint-manager";
import { TaskManager } from "@augment-internal/sidecar-libs/src/agent/task/task-manager";
import { FileBackedTaskStorage } from "@augment-internal/sidecar-libs/src/agent/task/task-storage";
import { setLibraryAPIClient } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client";
import { APIClient } from "./client-interfaces/api-client";
import { registerWebviewMethods } from "./webviews/webview-methods";
import { setLibraryWebviewMessaging } from "@augment-internal/sidecar-libs/src/client-interfaces/webview-messaging";
import { WebviewMessaging } from "@augment-internal/sidecar-libs/src/webview-messages/webview-messaging";
import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import { ClientTools } from "./tools/client-tools-host";
import { NodeProcessRemoteInfo } from "./tools/remote-info-source";
import { ChatMode } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  getPluginFileStore,
  setLibraryPluginFileStore,
} from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import { PluginFileStore } from "./client-interfaces/plugin-file-store";
import { AgentShardStorage } from "@augment-internal/sidecar-libs/src/agent/agent-shard-storage";
import { setLibraryClientActions } from "@augment-internal/sidecar-libs/src/client-interfaces/client-actions";
import { ClientActions } from "./client-interfaces/client-actions";
import { setLibraryClientFeatureFlags } from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import { setLibraryStateForSidecar } from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-state";
import { PluginStateForSidecar } from "./client-interfaces/plugin-storage-for-sidecar";
import { ToolStartupError } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";

// Initialize the logger
const logger = getLogger("server");
logger.info("\n\nStarting sidecar node process....");

process
  .on("unhandledRejection", (reason) => {
    logger.error(`Unhandled Rejection at Promise ${reason as string}`);
  })
  .on("uncaughtException", (err) => {
    logger.error(`Uncaught Exception thrown: ${err.message}`);
  });

const connection = createConnection();
const documents = new TextDocuments(TextDocument);

connection.onExit(() => {
  logger.info("Language server exiting");
});

connection.onInitialize((params: InitializeParams): InitializeResult => {
  logger.info(`Initializing Language Server: ${JSON.stringify(params)}`);

  let chatMode = ProtoChatMode.CHAT;
  let memoriesAbsPath: string | undefined = undefined;

  try {
    const request = InitializeParamsProto.fromJson(
      params as unknown as JsonValue,
      {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      },
    );
    logger.info(
      `Initializing Language Server....parsed proto: ${request.toJsonString()}`,
    );

    if (request.capabilities?.featureFlags) {
      // NOTE: This is a bit of a hack to force the node-process to use the
      // str_replace_editor_tool since IntelliJ does not have any other
      // edit tool.
      request.capabilities.featureFlags.agentEditTool =
        "str_replace_editor_tool";
      clientFeatureFlags.updateFlags(request.capabilities.featureFlags);
    } else {
      logger.warn("Failed to get feature flags from initialize params");
    }

    if (request.capabilities?.initialState?.chatMode) {
      chatMode = request.capabilities.initialState.chatMode;
    }
    memoriesAbsPath = request.capabilities?.initialState?.memoriesAbsPath;
  } catch (err: unknown) {
    logger.warn(
      `Failed to parse initialize params: ${err instanceof Error ? err.message : (err as string)}`,
    );
  }

  const result: InitializeResult = {
    capabilities: {},
  };

  // Initialize the client interfaces
  setLibraryPluginFileStore(new PluginFileStore(connection));
  setLibraryStateForSidecar(new PluginStateForSidecar(connection));
  setLibraryClientWorkspaces(new ClientWorkspaces(connection));
  setLibraryAPIClient(new APIClient(connection));
  setLibraryClientActions(new ClientActions(connection));
  setLibraryClientFeatureFlags(clientFeatureFlags);

  const checkpointManager = new AggregateCheckpointManager(
    new AgentShardStorage(),
    () => memoriesAbsPath,
    (_cb: (e: TextDocumentChangeEvent) => unknown) => {
      logger.warn(`documentChangeHandler not implemented yet`);
      return {
        dispose: () => {},
      };
    },
    () => ({ dispose: () => {} }),
    () => ({ dispose: () => {} }),
  );

  // Create a default user agent for the sidecar
  const userAgent = "Augment-Sidecar/1.0";

  const getAgentMemories = async () => {
    const pluginStore = getPluginFileStore();
    const assetPath =
      memoriesAbsPath?.split("/").pop() ?? "augment-memories.md";
    // log that we're looking up this abs path
    logger.info(`Loading memories from ${assetPath}`);
    const asset = await pluginStore.loadAsset(assetPath);
    if (asset) {
      return Buffer.from(asset).toString("utf8");
    }
    logger.warn(`No memories found`);
    return undefined;
  };

  const toolsModel = new ToolsModel(
    [],
    (chatMode) => new ClientTools(chatMode),
    new NodeProcessRemoteInfo(connection),
    (_details: ToolStartupError) => {}, // _mcpToolsStartupErrorFn
    clientFeatureFlags,
    checkpointManager,
    getAgentMemories,
    () => memoriesAbsPath, // _getMemoriesFilePath
    () => undefined, // _getToolUseRequestEventReporter
    {
      userAgent,
      // NOTE: grep search is not supported on intellij yet
      unsupportedSidecarTools: new Set([SidecarToolType.grepSearch]),
    },
  );
  logger.info(`Initial chat mode: ${chatMode}`);
  toolsModel.setMode(
    chatMode === ProtoChatMode.AGENT ? ChatMode.agent : ChatMode.chat,
  );

  // Initialize the task manager
  const taskManager = new TaskManager(new FileBackedTaskStorage());

  // Initialize the webview messaging
  setLibraryWebviewMessaging(
    new WebviewMessaging(checkpointManager, toolsModel, taskManager),
  );

  // Register LSP methods
  registerToolMethods(connection, toolsModel);
  registerWebviewMethods(connection);

  return result;
});

connection.onInitialized(() => {
  logger.info("Language server initialized");
});

documents.listen(connection);
connection.listen();

logger.info("Language server started");
