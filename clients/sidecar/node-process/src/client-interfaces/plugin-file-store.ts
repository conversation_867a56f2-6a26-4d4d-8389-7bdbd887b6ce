import { IPluginFileStore } from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import { Connection } from "vscode-languageserver";
import { getLogger } from "../logging";
import { sendRequestWithTimeout } from "../connection-utils";
import {
  DeleteAsset,
  LoadAsset,
  LoadAssetResponse,
  SaveAsset,
} from "$clients/sidecar/node-process/protos/plugin-file-store_pb";

export class PluginFileStore implements IPluginFileStore {
  private _logger = getLogger("PluginFileStore");
  private _debug = false;

  constructor(private readonly _connection: Connection) {}

  // As of March 27th 2025 the logs from this file are very noisy due to agent
  // shard manager.
  private log(message: string) {
    if (this._debug) {
      this._logger.info(message);
    }
  }

  async saveAsset(path: string, contents: Uint8Array): Promise<void> {
    this.log(`Requesting write file from host: ${path}`);
    await sendRequestWithTimeout(
      this._connection,
      "augmentcode/plugin-filestore/saveAsset",
      new SaveAsset({ path, contents }).toJson(),
    );
    this.log(`Save asset complete`);
  }

  async loadAsset(path: string): Promise<Uint8Array | undefined> {
    this.log(`Requesting load asset from host: ${path}`);
    const jsonResposne = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/plugin-filestore/loadAsset",
      new LoadAsset({ path: path }).toJson(),
    );
    if (!jsonResposne) {
      return undefined;
    }
    try {
      const response = LoadAssetResponse.fromJson(jsonResposne, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      this.log(`Load asset complete`);
      return response.contents;
    } catch (err) {
      this._logger.warn(
        `Failed to parse load asset response: ${err instanceof Error ? err.message : (err as string)}`,
      );
      return undefined;
    }
  }

  async deleteAsset(path: string): Promise<void> {
    this.log(`Requesting delete asset from host: ${path}`);
    await sendRequestWithTimeout(
      this._connection,
      "augmentcode/plugin-filestore/deleteAsset",
      new DeleteAsset({ path }).toJson(),
    );
    this.log(`Delete asset complete`);
  }
}
