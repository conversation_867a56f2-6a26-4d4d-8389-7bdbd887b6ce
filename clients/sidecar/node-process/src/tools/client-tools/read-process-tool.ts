import { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { errorToolResponse } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import {
  LocalToolType,
  ToolBase,
  ToolSafety,
  ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";

/**
 * A tool that reads output from a process.
 * Note that this only reads from processes launched with the launch_process tool.
 */
export class ReadProcessTool extends ToolBase<LocalToolType> {
  constructor() {
    super(LocalToolType.readProcess, ToolSafety.Safe);
  }

  public readonly description: string = `\
Read output from a terminal.

If \`wait=true\` and the process has not yet completed, waits for the terminal to complete up to \`max_wait_seconds\` seconds before returning its output.

If \`wait=false\` or the process has already completed, returns immediately with the current output.`;

  public readonly inputSchemaJson: string = JSON.stringify({
    /* eslint-disable @typescript-eslint/naming-convention */
    type: "object",
    properties: {
      terminal_id: {
        type: "integer",
        description: "Terminal ID to read from.",
      },
      wait: {
        type: "boolean",
        description: "Whether to wait for the command to complete.",
      },
      max_wait_seconds: {
        type: "number",
        description: `Number of seconds to wait for the command to complete. Only relevant when wait=true. 1 minute may be a good default: increase from there if needed.`,
      },
    },
    required: ["terminal_id", "wait", "max_wait_seconds"],
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  public checkToolCallSafe(_toolInput: Record<string, unknown>): boolean {
    return true;
  }

  public call(
    _toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    return Promise.resolve(
      errorToolResponse(`Expected client to implement save file tool.`),
    );
  }
}
