import { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { errorToolResponse } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import {
  checkShellAllowlist,
  getShellAllowlist,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-allowlist";
import { getDefaultShell } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-utils";
import {
  LocalToolType,
  ToolBase,
  ToolSafety,
  ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";

export class LaunchProcessTool extends ToolBase<LocalToolType> {
  private static readonly defaultWaitSeconds = 60;

  constructor() {
    super(LocalToolType.launchProcess, ToolSafety.Check);
  }

  public readonly version = 2;

  public readonly description: string = `\
Launch a new process with a shell command. A process can be waiting (\`wait=true\`) or non-waiting (\`wait=false\`).

If \`wait=true\`, launches the process in an interactive terminal, and waits for the process to complete up to
\`max_wait_seconds\` seconds. If the process ends during this period, the tool call returns. If the timeout
expires, the process will continue running in the background but the tool call will return. You can then
interact with the process using the other process tools.

Note: Only one waiting process can be running at a time. If you try to launch a process with \`wait=true\`
while another is running, the tool will return an error.

If \`wait=false\`, launches a background process in a separate terminal. This returns immediately, while the
process keeps running in the background.

Notes:
- Use \`wait=true\` processes when the command is expected to be short, or when you can't
proceed with your task until the process is complete. Use \`wait=false\` for processes that are
expected to run in the background, such as starting a server you'll need to interact with, or a
long-running process that does not need to complete before proceeding with the task.
- If this tool returns while the process is still running, you can continue to interact with the process
using the other available tools. You can wait for the process, read from it, write to it, kill it, etc.
- You can use this tool to interact with the user's local version control system. Do not use the
retrieval tool for that purpose.
- If there is a more specific tool available that can perform the function, use that tool instead of
this one.

The OS is ${process.platform}.`;

  public readonly inputSchemaJson: string = JSON.stringify({
    /* eslint-disable @typescript-eslint/naming-convention */
    type: "object",
    properties: {
      command: {
        type: "string",
        description: "The shell command to execute.",
      },
      wait: {
        type: "boolean",
        description: "Whether to wait for the command to complete.",
      },
      max_wait_seconds: {
        type: "number",
        description: `Number of seconds to wait for the command to complete. Only relevant when wait=true. 10 minutes may be a good default: increase from there if needed.`,
      },
      cwd: {
        type: "string",
        description:
          "Working directory for the command. If not supplied, uses the current working directory.",
      },
    },
    required: ["command", "wait", "max_wait_seconds"],
    /* eslint-enable @typescript-eslint/naming-convention */
  });

  public checkToolCallSafe(toolInput: Record<string, unknown>): boolean {
    try {
      const command = toolInput.command as string;
      if (!command || typeof command !== "string") {
        return false;
      }
      const shellName = getDefaultShell(process.platform);
      const allowlist = getShellAllowlist(process.platform, shellName);
      return checkShellAllowlist(allowlist, command, shellName);
    } catch (error) {
      return false;
    }
  }

  public call(
    _toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    return Promise.resolve(
      errorToolResponse(`Expected client to implement save file tool.`),
    );
  }
}
