import { Rule } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { RulesParser } from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import {
    AUGMENT_DIRECTORY_ROOT,
    AUGMENT_RULES_FOLDER,
} from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import * as fs from "fs";
import * as path from "path";
import * as vscode from "vscode";

import { getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { directoryExists, fileExists, makeDirs, writeFileUtf8 } from "../utils/fs-utils";
import { baseName, dirName } from "../utils/path-utils";
import { openFileFromMessage } from "../utils/webviews/open-file";
import { WorkspaceManager } from "../workspace/workspace-manager";

const IMPORTED_DIRECTORY = "imported";
/**
 * Watches for changes to rule files in the .augment/rules directory
 */
export class RulesWatcher extends DisposableService {
    private _rulesChangedEmitter = new vscode.EventEmitter<void>();
    private _fileSystemWatcher: vscode.FileSystemWatcher | undefined;
    private _dirSystemWatcher: vscode.FileSystemWatcher | undefined;
    private _logger = getLogger("RulesWatcher");

    constructor(private readonly _workspaceManager: WorkspaceManager) {
        super();
        this._setupFileSystemWatcher();
        this.addDisposable(this._rulesChangedEmitter);
    }

    /**
     * Set up file system watchers for rule files and directories
     */
    private _setupFileSystemWatcher(): void {
        try {
            // Watch for changes to .md files in the .augment/rules directory
            const rulesGlob = `**/${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/**/*.md`;
            this._fileSystemWatcher = vscode.workspace.createFileSystemWatcher(rulesGlob);

            // When a file is created, changed, or deleted, emit an event
            this._fileSystemWatcher.onDidCreate(() => this._rulesChangedEmitter.fire());
            this._fileSystemWatcher.onDidChange(() => this._rulesChangedEmitter.fire());
            this._fileSystemWatcher.onDidDelete(() => this._rulesChangedEmitter.fire());

            this.addDisposable(this._fileSystemWatcher);

            // Watch for changes to directories in the .augment/rules directory
            const dirGlob = `**/${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/**`;
            this._dirSystemWatcher = vscode.workspace.createFileSystemWatcher(
                dirGlob,
                true,
                true,
                false
            );

            // We only care about directory deletions
            this._dirSystemWatcher.onDidDelete(() => {
                this._rulesChangedEmitter.fire();
            });

            this.addDisposable(this._dirSystemWatcher);
        } catch (error) {
            this._logger.error(`Error setting up file system watcher: ${String(error)}`);
        }
    }

    /**
     * Create a new rule file
     * @param rule The rule to create
     * @param autoImport Whether this is an auto-import
     */
    public async createRule(rule: Rule, autoImport = false): Promise<Rule> {
        // Get the workspace root using WorkspaceManager
        const workspaceRoot = this._workspaceManager.getBestFolderRoot();
        if (!workspaceRoot) {
            throw new Error("No workspace root found");
        }

        // Create the .augment/rules directory if it doesn't exist
        let rulesDir = path.join(workspaceRoot, AUGMENT_DIRECTORY_ROOT, AUGMENT_RULES_FOLDER);

        // Create .augment/rules/imported directory if it doesn't exist
        if (autoImport) {
            rulesDir = path.join(
                workspaceRoot,
                AUGMENT_DIRECTORY_ROOT,
                AUGMENT_RULES_FOLDER,
                IMPORTED_DIRECTORY
            );
        }

        const baseFileName = baseName(rule.path);
        const pathDirectoryName = dirName(rule.path);
        const newDirectoryName = path.join(rulesDir, pathDirectoryName);
        if (!directoryExists(newDirectoryName)) {
            await makeDirs(newDirectoryName);
        }
        const fileName = baseFileName.endsWith(".md") ? baseFileName : baseFileName + ".md";
        const filePath = path.join(newDirectoryName, fileName);
        if (fileExists(filePath)) {
            throw new Error(`Rule already exists ${filePath}, not overwriting`);
        }

        const relativePath = path.relative(rulesDir, filePath);
        const newRule = {
            path: relativePath,
            type: rule.type,
            content: rule.content,
        };
        const processedFileContent = RulesParser.formatRuleFileForMarkdown(newRule);
        await writeFileUtf8(filePath, processedFileContent);
        return newRule;
    }

    /**
     * Open a rule file in the editor
     */
    public async openRule(rulePath: string): Promise<void> {
        const workspaceRoot = this._workspaceManager.getBestFolderRoot();
        if (!workspaceRoot) {
            throw new Error("No workspace root found");
        }

        rulePath = path.join(AUGMENT_DIRECTORY_ROOT, AUGMENT_RULES_FOLDER, rulePath);
        const fullPath = path.join(workspaceRoot, rulePath);

        if (fs.existsSync(fullPath)) {
            await openFileFromMessage({
                repoRoot: workspaceRoot,
                pathName: rulePath,
                // Set openTextDocument to undefined to use the custom editor
                openTextDocument: undefined,
            });
        }
    }

    public deleteRule(rulePath: string): void {
        const workspaceRoot = this._workspaceManager.getBestFolderRoot();
        if (!workspaceRoot) {
            throw new Error("No workspace root found");
        }

        rulePath = path.join(AUGMENT_DIRECTORY_ROOT, AUGMENT_RULES_FOLDER, rulePath);
        const fullPath = path.join(workspaceRoot, rulePath);

        if (fs.existsSync(fullPath)) {
            fs.unlinkSync(fullPath);
        }
    }

    /**
     * Event that fires when rules change
     */
    get onDidChange(): vscode.Event<void> {
        return this._rulesChangedEmitter.event;
    }
}
