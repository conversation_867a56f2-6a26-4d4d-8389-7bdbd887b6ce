import { Rule, RuleType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
    AUGMENT_DIRECTORY_ROOT,
    AUGMENT_RULES_FOLDER,
} from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import * as fs from "fs";

import { WorkspaceManager } from "../../workspace/workspace-manager";
import { RulesWatcher } from "../rules-watcher";

// Mock dependencies
jest.mock("fs");

// Create mock functions that we can use in our tests
const mockDirectoryExists = jest.fn();
const mockFileExists = jest.fn();
const mockWriteFileUtf8 = jest.fn();

// Mock the fs-utils module
jest.mock("../../utils/fs-utils", () => ({
    directoryExistsAsync: jest.fn().mockResolvedValue(false),
    directoryExists: jest.fn().mockImplementation((...args) => mockDirectoryExists(...args)),
    fileExists: jest.fn().mockImplementation((...args) => mockFileExists(...args)),
    makeDirs: jest.fn().mockResolvedValue(undefined),
    writeFileUtf8: jest.fn().mockImplementation((...args) => mockWriteFileUtf8(...args)),
}));

jest.mock("@augment-internal/sidecar-libs/src/utils/rules-parser", () => ({
    // eslint-disable-next-line @typescript-eslint/naming-convention
    RulesParser: {
        formatRuleFileForMarkdown: jest.fn((rule) => `Formatted content for ${rule.path}`),
    },
    // Add the constants that are imported from this module
    // eslint-disable-next-line @typescript-eslint/naming-convention
    AUGMENT_DIRECTORY_ROOT: ".augment",
    // eslint-disable-next-line @typescript-eslint/naming-convention
    AUGMENT_RULES_FOLDER: "rules",
}));

describe("RulesWatcher", () => {
    // Mock workspace manager
    const mockWorkspaceManager = {
        getBestFolderRoot: jest.fn(),
    } as unknown as WorkspaceManager;

    // Reset mocks before each test
    beforeEach(() => {
        jest.clearAllMocks();

        // Setup default mock implementations
        (mockWorkspaceManager.getBestFolderRoot as jest.Mock).mockReturnValue("/workspace/root");
        (fs.existsSync as jest.Mock).mockReturnValue(false);
        (fs.mkdirSync as jest.Mock).mockImplementation(() => undefined);

        // Reset our custom mocks
        mockDirectoryExists.mockReturnValue(false);
        mockFileExists.mockReturnValue(false);
        mockWriteFileUtf8.mockResolvedValue(undefined);
    });

    describe("createRule", () => {
        it("should create a rule file in the rules directory", async () => {
            // Arrange
            const rulesWatcher = new RulesWatcher(mockWorkspaceManager);
            const ruleData: Rule = {
                path: "test-rule.md",
                type: RuleType.ALWAYS_ATTACHED,
                content: "# Test Rule\n\nThis is a test rule.",
            };

            // No need to mock directoryExistsAsync as it's already mocked in the jest.mock call

            // Act
            const result = await rulesWatcher.createRule(ruleData);

            // Assert
            // Check that the workspace root was retrieved
            expect(mockWorkspaceManager.getBestFolderRoot).toHaveBeenCalled();

            // Check that the rule file was written with the formatted content
            const filePath = `/workspace/root/${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/test-rule.md`;
            expect(mockWriteFileUtf8).toHaveBeenCalledWith(
                filePath,
                "Formatted content for test-rule.md"
            );

            // Check that the function returns the expected rule object
            expect(result).toEqual({
                path: "test-rule.md",
                type: RuleType.ALWAYS_ATTACHED,
                content: "# Test Rule\n\nThis is a test rule.",
            });
        });

        it("should create a rule file with .md extension if not provided", async () => {
            // Arrange
            const rulesWatcher = new RulesWatcher(mockWorkspaceManager);
            const ruleData: Rule = {
                path: "test-rule",
                type: RuleType.ALWAYS_ATTACHED,
                content: "# Test Rule\n\nThis is a test rule.",
            };

            // Act
            await rulesWatcher.createRule(ruleData);

            // Assert
            // Check that the file was written with the .md extension added
            const filePath = `/workspace/root/${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/test-rule.md`;
            expect(mockWriteFileUtf8).toHaveBeenCalledWith(
                filePath,
                "Formatted content for test-rule.md"
            );
        });

        it("should create a rule file with nested directory structure", async () => {
            // Arrange
            const rulesWatcher = new RulesWatcher(mockWorkspaceManager);
            const ruleData: Rule = {
                path: "category/subcategory/test-rule.md",
                type: RuleType.ALWAYS_ATTACHED,
                content: "# Test Rule\n\nThis is a test rule with nested path.",
            };

            // Act
            const result = await rulesWatcher.createRule(ruleData);

            // Assert
            // Check that the rule file was written with the formatted content
            const filePath = `/workspace/root/${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/category/subcategory/test-rule.md`;
            expect(mockWriteFileUtf8).toHaveBeenCalledWith(
                filePath,
                "Formatted content for category/subcategory/test-rule.md"
            );

            // Check that the returned rule has the correct path
            expect(result.path).toBe("category/subcategory/test-rule.md");
        });

        it("should create a rule file in the imported directory when autoImport is true", async () => {
            // Arrange
            const rulesWatcher = new RulesWatcher(mockWorkspaceManager);
            const ruleData: Rule = {
                path: "test-rule.md",
                type: RuleType.MANUAL,
                content: "# Test Rule\n\nThis is a test rule.",
            };

            // Act
            const result = await rulesWatcher.createRule(ruleData, true);

            // Assert
            // Check that the file was written to the imported directory
            const filePath = `/workspace/root/${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/imported/test-rule.md`;
            expect(mockWriteFileUtf8).toHaveBeenCalledWith(
                filePath,
                "Formatted content for test-rule.md"
            );

            // Check that the returned rule has the correct path
            expect(result.path).toBe("test-rule.md");
        });

        it("should create a rule file with nested directory in the imported directory when autoImport is true", async () => {
            // Arrange
            const rulesWatcher = new RulesWatcher(mockWorkspaceManager);
            const ruleData: Rule = {
                path: "category/subcategory/test-rule.md",
                type: RuleType.MANUAL,
                content: "# Test Rule\n\nThis is a test rule with nested path.",
            };

            // Act
            const result = await rulesWatcher.createRule(ruleData, true);

            // Assert
            // Check that the file was written to the nested imported directory
            const filePath = `/workspace/root/${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/imported/category/subcategory/test-rule.md`;
            expect(mockWriteFileUtf8).toHaveBeenCalledWith(
                filePath,
                "Formatted content for category/subcategory/test-rule.md"
            );

            // Check that the returned rule has the correct path
            expect(result.path).toBe("category/subcategory/test-rule.md");
        });

        it("should throw an error if no workspace root is found", async () => {
            // Arrange
            (mockWorkspaceManager.getBestFolderRoot as jest.Mock).mockReturnValue(undefined);
            const rulesWatcher = new RulesWatcher(mockWorkspaceManager);
            const ruleData: Rule = {
                path: "test-rule.md",
                type: RuleType.ALWAYS_ATTACHED,
                content: "# Test Rule\n\nThis is a test rule.",
            };

            // Act & Assert
            await expect(rulesWatcher.createRule(ruleData)).rejects.toThrow(
                "No workspace root found"
            );
        });

        it("should throw an error if the rule file already exists", async () => {
            // Arrange
            const rulesWatcher = new RulesWatcher(mockWorkspaceManager);
            const ruleData: Rule = {
                path: "existing-rule.md",
                type: RuleType.ALWAYS_ATTACHED,
                content: "# Existing Rule\n\nThis is an existing rule.",
            };

            // Mock fileExists to return true for the specific file path
            const filePath = `/workspace/root/${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/existing-rule.md`;
            mockFileExists.mockImplementation((path: string) => {
                return path === filePath;
            });

            // Act & Assert
            await expect(rulesWatcher.createRule(ruleData)).rejects.toThrow(
                `Rule already exists ${filePath}, not overwriting`
            );
        });
    });
});
