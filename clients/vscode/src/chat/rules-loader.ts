import { Rule } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
    AUGMENT_DIRECTORY_ROOT,
    AUGMENT_RULES_FOLDER,
    RulesParser,
} from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import * as path from "path";

import { AugmentLogger, getLogger } from "../logging";
import { directoryExistsAsync, readDirectory, readFileUtf8 } from "../utils/fs-utils";
import { FileType } from "../utils/types";
import { WorkspaceManager } from "../workspace/workspace-manager";

const MARKDOWN_FILE_EXTENSION = ".md";
const CURSOR_RULES_FILE_EXTENSION = ".mdc";
export const MARKDOWN_FILE_ENDINGS = [MARKDOWN_FILE_EXTENSION, CURSOR_RULES_FILE_EXTENSION];

export class RulesLoader {
    public static async loadRules(workspaceManager: WorkspaceManager): Promise<Rule[]> {
        return RulesLoader.loadDirectory(
            workspaceManager,
            path.join(AUGMENT_DIRECTORY_ROOT, AUGMENT_RULES_FOLDER)
        );
    }
    public static async loadDirectory(
        workspaceManager: WorkspaceManager,
        directory: string
    ): Promise<Rule[]> {
        const logger = getLogger("RulesLoader");
        const rules: Rule[] = [];

        try {
            const workspaceRoot = workspaceManager.getBestFolderRoot();
            if (!workspaceRoot) {
                logger.warn("No workspace root found");
                return rules;
            }

            const rulesFolder = path.join(workspaceRoot, directory);
            if (!(await directoryExistsAsync(rulesFolder))) {
                logger.debug(`Rules folder not found at ${rulesFolder}`);
                return rules;
            }

            // Recursively process the rules folder
            await RulesLoader.processRuleDirectory(rulesFolder, rules, "", logger);

            logger.debug(`Loaded ${rules.length} rules from ${rulesFolder}`);
            return rules;
        } catch (error) {
            logger.error(`Error loading rules: ${String(error)}`);
            return rules;
        }
    }

    public static async processRuleDirectory(
        dirPath: string,
        rules: Rule[],
        relativePath: string,
        logger: AugmentLogger
    ): Promise<void> {
        const entries = await readDirectory(dirPath);

        for (const [fileName, fileType] of entries) {
            const currentRelativePath = relativePath ? path.join(relativePath, fileName) : fileName;
            const fullPath = path.join(dirPath, fileName);

            if (fileType === FileType.directory) {
                await RulesLoader.processRuleDirectory(
                    fullPath,
                    rules,
                    currentRelativePath,
                    logger
                );
            } else if (
                fileType === FileType.file &&
                MARKDOWN_FILE_ENDINGS.some((ending) => fileName.toLowerCase().endsWith(ending))
            ) {
                try {
                    const content = await readFileUtf8(fullPath);

                    const rule = RulesParser.parseRuleFile(content, currentRelativePath);
                    if (rule) {
                        rules.push(rule);
                    }
                } catch (error) {
                    logger.error(`Error reading rule file ${fullPath}: ${String(error)}`);
                }
            }
        }
    }
}
