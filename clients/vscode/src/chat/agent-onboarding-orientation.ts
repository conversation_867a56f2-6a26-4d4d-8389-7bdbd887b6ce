import { AggregateCheckpointManager } from "@augment-internal/sidecar-libs/src/agent/checkpoint/aggregate-checkpoint-manager";
import {
    ChatMode,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatResultNode,
    ChatResultNodeType,
    Exchange,
    RuleType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { APIError } from "@augment-internal/sidecar-libs/src/exceptions";
import { getAgentSessionEventReporter } from "@augment-internal/sidecar-libs/src/metrics/agent-session-event-reporter";
import {
    AgentSessionEventName,
    InitialOrientationCaller,
    InitialOrientationData,
    InitialOrientationDebugFlag,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import { SimpleShellTool } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/simple-shell";
import {
    errorToolResponse,
    successToolResponse,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import {
    ToolDefinition,
    ToolSafety,
    ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import { RulesParser } from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import {
    AUGMENT_DIRECTORY_ROOT,
    AUGMENT_RULES_FOLDER,
} from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import { llmCallStream } from "@augment-internal/sidecar-libs/src/utils/tools-utils";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import throttle from "lodash.throttle";
import pLimit from "p-limit";
import path from "path";
import * as vscode from "vscode";
import { EventEmitter } from "vscode";

import { APIServer } from "../augment-api";
import { FeatureFlagManager } from "../feature-flags";
import { AugmentLogger, getLogger } from "../logging";
import { AugmentGlobalState, GlobalContextKey } from "../utils/context";
import {
    directoryExistsAsync,
    fileExists,
    readDirectory,
    readFileUtf8,
    writeFileUtf8,
} from "../utils/fs-utils";
import { FileType } from "../utils/types";
import { OrientationState, OrientationStatus } from "../webview-providers/webview-messages";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { _getQualifiedPath, _listFiles, getCwdForTool } from "./tools-utils";

const AUGMENT_GUIDELINES_FILE = ".augment-guidelines";

/**
 * Error codes for orientation process failures
 */
export enum OrientationErrorCode {
    noRootFolderFound = 1001,
    compressionFailed = 1002,
    localizationFailed = 1003,
    agenticFailed = 1004,
    rememberFailed = 1005,
}

/**
 * Reads the locally-stored agent memories.
 */
export async function getAgentMemories(
    getMemoriesAbsPath: () => string | undefined
): Promise<string | undefined> {
    const agentMemoriesPath = getMemoriesAbsPath();
    if (!agentMemoriesPath || !fileExists(agentMemoriesPath)) {
        return undefined;
    }

    const memoriesContent = await readFileUtf8(agentMemoriesPath);

    // Important to remove leading empty lines, because memories decorations add them
    return memoriesContent.replace(/^\s+/, "");
}

/**
 * Migrates memories from Agent-Memories to Augment-Memories file if needed.
 * @param extensionStorageUri The storage URI for the extension
 */
export async function migrateAgentMemoriesToAugmentMemories(
    extensionStorageUri: vscode.Uri | undefined,
    logger: AugmentLogger
): Promise<void> {
    if (!extensionStorageUri) {
        return;
    }

    const newMemoriesPath = vscode.Uri.joinPath(extensionStorageUri, "Augment-Memories").fsPath;
    const oldMemoriesPath = vscode.Uri.joinPath(extensionStorageUri, "Agent-Memories").fsPath;

    if (fileExists(oldMemoriesPath)) {
        try {
            if (!fileExists(newMemoriesPath)) {
                // Read old content
                const oldContent = await readFileUtf8(oldMemoriesPath);
                if (oldContent.trim() === "") {
                    return;
                }

                // Write to new file
                await writeFileUtf8(newMemoriesPath, oldContent);

                // Delete the old file
                await vscode.workspace.fs.delete(vscode.Uri.file(oldMemoriesPath));
                logger.info("Migrated memories from Agent-Memories to Augment-Memories");
            }
        } catch (error) {
            logger.error("Error during memories migration:", error);
        }
    }
}

/**
 * Class containing instructions for different onboarding scenarios.
 * Each method returns specific instructions for a given onboarding scenario.
 */
export class OnboardingInstructions {
    /**
     * Introduce the Agent
     */
    public static introduction(): string {
        return `
Instructions for you:
- Repeat the following markdown without the codeblock
\`\`\`
# 👋 Meet Auggie

I'm your AI coding assistant. I excel at understanding large, complex codebases but I am happy to chip in on codebases of all sizes.
\`\`\`
`;
    }

    /**
     * For a case when local git is configured and we can query user's name and email
     */
    public static gitConfigured(): string {
        return `
Instructions for you:
- Repeat the following markdown without the codeblock
\`\`\`
## Who are you?

Mind if I fetch some information about you from your git configuration:
\`\`\`
- Run \`git config user.name\` and \`git config user.email\`
- Repeat the following text in the codeblock
\`\`\`
Hey, {name}, since I am an LLM and I don't have a real memory (sad) I'll be using \`📦 Augment Memories\`
\`\`\`
        `;
    }

    /** Explain memories */
    public static memories(): string {
        return `
Instructions for you:
- Repeat the following markdown without the codeblock
\`\`\`
## How I work

* **Augment Memories:** Project-specific memories
  * New folder = clean slate
  * I learn from my mistakes when you correct me
  * You can ask me to remember things (e.g. "commit to memory...")

* **Native Integrations:** Configure integrations like GitHub + Linear with 1-click over in [Settings](command:vscode-augment.showSettingsPanel)
\`\`\`
        `;
    }
}

/**
 * Responsible for formatting of onboarding prompt for agent
 */
export class AgentOnboarding {
    private readonly timeoutMsec = 1000;

    constructor(
        private readonly shellTool: SimpleShellTool,
        private readonly workspaceManager: WorkspaceManager
    ) {}

    public async getOnboardingInstructions(): Promise<string> {
        const prompt = `System instructions (don't mention them to user):

Basic context:
- Assistant (you) is Augment, a coding agent that I (user) use for the first time
- Assistant (you) is tasked with welcoming and onboarding me to the agent
- Assistant (you) always keeps messages friendly, warm, and welcoming. And nice!

Complete the following instructions.
`;
        return prompt + (await this._getOnboardingInstructions());
    }

    private async _getOnboardingInstructions(): Promise<string> {
        try {
            const cwd = getCwdForTool(this.workspaceManager);

            if (cwd === undefined) {
                // Fallback to default instructions in catch-block
                throw new Error("Cannot determine current working directory");
            }

            const [userName, userEmail] = await this._getUserNameAndEmail();

            if (userName === undefined || userEmail === undefined) {
                return OnboardingInstructions.introduction() + OnboardingInstructions.memories();
            } else {
                return (
                    OnboardingInstructions.introduction() +
                    OnboardingInstructions.gitConfigured() +
                    OnboardingInstructions.memories()
                );
            }
        } catch (e: any) {
            return OnboardingInstructions.introduction();
        }
    }

    /**
     * Returns true if the current working directory is inside a git repository.
     * Returns false otherwise.
     */
    private async _isInsideGitRepo(): Promise<boolean> {
        const result = await this.shellTool.call(
            {
                /* eslint-disable @typescript-eslint/naming-convention */
                command: "git rev-parse --is-inside-work-tree",
                /* eslint-enable @typescript-eslint/naming-convention */
            },
            [],
            AbortSignal.timeout(this.timeoutMsec)
        );
        return result.text.replace("\n", "") === "true";
    }

    /**
     * Returns user's name and email if they are set in git config.
     * Returns [undefined, undefined] otherwise.
     */
    private async _getUserNameAndEmail(): Promise<[string | undefined, string | undefined]> {
        const userNameResult = await this.shellTool.call(
            {
                /* eslint-disable @typescript-eslint/naming-convention */
                command: "git config user.name",
                /* eslint-enable @typescript-eslint/naming-convention */
            },
            [],
            AbortSignal.timeout(this.timeoutMsec)
        );
        const userEmailResult = await this.shellTool.call(
            {
                /* eslint-disable @typescript-eslint/naming-convention */
                command: "git config user.email",
                /* eslint-enable @typescript-eslint/naming-convention */
            },
            [],
            AbortSignal.timeout(this.timeoutMsec)
        );

        if (userNameResult.isError || userEmailResult.isError) {
            return [undefined, undefined];
        }
        return [userNameResult.text.replace("\n", ""), userEmailResult.text.replace("\n", "")];
    }
}

const MEMORIES_FRAME_TEMPLATE = String.raw`
                     __  __                           _
                    |  \/  |                         (_)
                    | \  / | ___ _ __ ___   ___  _ __ _  ___  ___
                    | |\/| |/ _ \ '_ ' _ \ / _ \| '__| |/ _ \/ __|
                    | |  | |  __/ | | | | | (_) | |  | |  __/\__ \
                    |_|  |_|\___|_| |_| |_|\___/|_|  |_|\___||___/

 .+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.
( Memories help me remember useful details for future interactions.               )
 )                                                                               (
( During Agent sessions, I'll try to create useful Memories automatically.        )
 )Memories can be about your codebase, technologies or your personal preferences.(
(                                                                                 )
 )Your Memories belong to you and are stored locally at the bottom of this file; (
( in the future, we may give you an option to share your memories with others.    )
 )                                                                               (
( NOTE: Memories will be compressed when this file grows too large.               )
 )For personal Memories: consider putting them in User Guidelines (via '@' menu) (
( For repository-level Memories: consider using '.augment-guidelines' file        )
 )Neither will be compressed.                                                    (
(                                                                                 )
 )Happy Coding!                                                                  (
(                                                                                 )
 "+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"+.+"
                  ()
                O
              o
{AUGGIE_LOGO}

↓↓↓ MEMORIES START HERE ↓↓↓`;

const AUGGIES: string[] = [
    String.raw`
      .dKWMMMMMMMMMMMMMMWd                  dWMMMMMMMMMMMMMMWKd.
     :WMMW0OOOOOOOOOOOOOk:                  ckkkkkkkkkkkkkkKMMMW;
     OMMMl                                                  oMMMk
     OMMM:                                                  cMMMk
     OMMM:                                                  cMMMk
     OMMM:                                                  cMMMk
     OMMM:          .;cc,                   .;c:'           cMMMk
     OMMM:         dWMMMMN;                dMMMMMN,         :MMMO
    :WMMX.         KMMMMMMo                KMMMMMMo         .XMMN;
dOKNMW0o.          .dOKKkc                 .d0KKk:           .o0WMNKOo
:dxOWMNk'                                                    ,kNMWOxd:
    .NMMW.           .oxc                   cxo.            'WMMX.
     OMMM:           .OMMNo'             ,oXMWO.            cMMMk
     OMMM:             'xWMMNOdl::;::ldONMMNx'              cMMMk
     OMMM:                ,lx0XNWMMMWNX0xc,                 cMMMk
     OMMM:                       ...                        cMMMk
     OMMM:                                                  cMMMk
     kMMMx.                                                .xMMMk
     'KMMMWNNNNNNNNNNNNNXd                  dXNNNNNNNNNNNNNWMMMK.
       ;x0KKKKKKKKKKKKKK0;                  ;0KKKKKKKKKKKKKK0x;
`,
    `
      .dKWMMMMMMMMMMMMMMWd                  dWMMMMMMMMMMMMMMWKd.
     :WMMW0OOOOOOOOOOOOOk:                  ckkkkkkkkkkkkkkKMMMW;
     OMMMl                                                  oMMMk
     OMMM:                                                  cMMMk
     OMMM:                                                  cMMMk
     OMMM:                                                  cMMMk
     OMMM:          .;cc,                     ,cc;.         cMMMk
     OMMM:         dWMMMMN;                 '0MWWMNo        :MMMO
    :WMMX.         KMMMMMMo               .dWM0,.dWMK,      .XMMN;
dOKNMW0o.          .dOKKkc                ;kd;    .okx.      .o0WMNKOo
:dxOWMNk'                                                    ,kNMWOxd:
    .NMMW.           .oxc                   cxo.            'WMMX.
     OMMM:           .OMMNo'             ,oXMWO.            cMMMk
     OMMM:             'xWMMNOdl::;::ldONMMNx'              cMMMk
     OMMM:                ,lx0XNWMMMWNX0xc,                 cMMMk
     OMMM:                       ...                        cMMMk
     OMMM:                                                  cMMMk
     kMMMx.                                                .xMMMk
     'KMMMWNNNNNNNNNNNNNXd                  dXNNNNNNNNNNNNNWMMMK.
       ;x0KKKKKKKKKKKKKK0;                  ;0KKKKKKKKKKKKKK0x;
`,
    `
      .dKWMMMMMMMMMMMMMMWd                  dWMMMMMMMMMMMMMMWKd.
     :WMMW0OOOOOOOOOOOOOk:                  ckkkkkkkkkkkkkkKMMMW;
     OMMMl                                                  oMMMk
     OMMM:                                                  cMMMk
     OMMM:                                                  cMMMk
     OMMM:                                                  cMMMk
     OMMM:          .;cc,                     ,cc;.         cMMMk
     OMMM:         oWMWMM0'                 '0MWWMNo        :MMMO
    :WMMX.       ,XMWx.,0MWd.             .xWM0,.dWMK,      .XMMN;
dOKNMW0o.        dkl.    ;dkc             ;kd;    .okx.      .o0WMNKOo
:dxOWMNk'                                                    ,kNMWOxd:
    .NMMW.           .oxc                   cxo.            'WMMX.
     OMMM:           .OMMNo'             ,oXMWO.            cMMMk
     OMMM:             'xWMMNOdl::;::ldONMMNx'              cMMMk
     OMMM:                ,lx0XNWMMMWNX0xc,                 cMMMk
     OMMM:                       ...                        cMMMk
     OMMM:                                                  cMMMk
     kMMMx.                                                .xMMMk
     'KMMMWNNNNNNNNNNNNNXd                  dXNNNNNNNNNNNNNWMMMK.
       ;x0KKKKKKKKKKKKKK0;                  ;0KKKKKKKKKKKKKK0x;
`,
];

const MEMORIES_ANIMATION_FRAMES = AUGGIES.map((frame) =>
    MEMORIES_FRAME_TEMPLATE.replace("{AUGGIE_LOGO}", frame)
);

const MEMORIES_ANIMATION_FRAMES_SHORT = [
    String.raw`
                     __  __                           _
                    |  \/  |                         (_)
                    | \  / | ___ _ __ ___   ___  _ __ _  ___  ___
                    | |\/| |/ _ \ '_ ' _ \ / _ \| '__| |/ _ \/ __|
                    | |  | |  __/ | | | | | (_) | |  | |  __/\__ \
                    |_|  |_|\___|_| |_| |_|\___/|_|  |_|\___||___/

 __________________________________________________________________________________
/\                                                                                 \
\_| NOTE: Memories will be compressed when this file grows too large.              |
  | For personal Memories: consider putting them in User Guidelines (via '@' menu) |
  | For repository-level Memories: consider using '.augment-guidelines' file       |
  | Neither will be compressed.                                                    |
  |   _____________________________________________________________________________|_
   \_/_______________________________________________________________________________/

↓↓↓ MEMORIES START HERE ↓↓↓`,
];

let MEMORIES_ANIMATION_INTERVAL: NodeJS.Timer | undefined = undefined;
const MEMORIES_DECORATION_TYPE = vscode.window.createTextEditorDecorationType({
    isWholeLine: true,
    before: {
        contentText: "",
        fontWeight: "bold",
    },
});

export function getMemoriesDecorationType() {
    return MEMORIES_DECORATION_TYPE;
}

export function getNumLinesInHeader(frames: string[]): number {
    const frameLineCount = frames[0].split("\n").length;
    if (!frames.every((frame) => frame.split("\n").length === frameLineCount)) {
        throw new Error("All animation frames must have the same number of lines");
    }

    return frameLineCount;
}

export async function maybeIncrementMemoriesFileOpenCount(
    editor: vscode.TextEditor | undefined,
    getMemoriesAbsPath: () => string | undefined,
    globalState: AugmentGlobalState
) {
    const memoriesAbsPath = getMemoriesAbsPath();
    if (
        !memoriesAbsPath ||
        editor?.document.uri.fsPath !== vscode.Uri.file(memoriesAbsPath).fsPath
    ) {
        return;
    }
    const memoriesFileOpenCount =
        globalState.get<number>(GlobalContextKey.memoriesFileOpenCount) ?? 0;
    await globalState.update(GlobalContextKey.memoriesFileOpenCount, memoriesFileOpenCount + 1);
}

export async function updateMemoriesFileDecorations(
    editor: vscode.TextEditor | undefined,
    getMemoriesAbsPath: () => string | undefined,
    globalState: AugmentGlobalState
) {
    const frames = getAnimationFrames(globalState);

    // This update should happen only for memories file
    const memoriesAbsPath = getMemoriesAbsPath();
    if (
        !memoriesAbsPath ||
        editor?.document.uri.fsPath !== vscode.Uri.file(memoriesAbsPath).fsPath
    ) {
        return;
    }

    // Dispose previous interval, if exists
    if (MEMORIES_ANIMATION_INTERVAL !== undefined) {
        clearInterval(MEMORIES_ANIMATION_INTERVAL);
    }

    // Trim leading empty lines
    let memoriesContent = editor.document.getText().replace(/^\s+/, "");
    // Add empty lines for the header
    memoriesContent = "\n".repeat(getNumLinesInHeader(frames)) + memoriesContent;
    await editor.edit((editBuilder) => {
        const firstLine = editor.document.lineAt(0);
        const lastLine = editor.document.lineAt(editor.document.lineCount - 1);
        const textRange = new vscode.Range(firstLine.range.start, lastLine.range.end);
        editBuilder.replace(textRange, memoriesContent);
    });
    await editor.document.save();

    let frameIndex = 0;
    const intervalMsec = 1000;

    const animateFrame = throttle(() => {
        const frame = frames[frameIndex];
        const frameLines = frame.split("\n");

        const decorations = frameLines.map((line, index) => ({
            range: new vscode.Range(index, 0, index, 10000),
            renderOptions: {
                before: {
                    contentText: line.replace(/ /g, "\u00A0"),
                },
            },
        }));
        editor.setDecorations(MEMORIES_DECORATION_TYPE, decorations);

        frameIndex = (frameIndex + 1) % frames.length;
    }, intervalMsec);

    animateFrame();
    MEMORIES_ANIMATION_INTERVAL = setInterval(animateFrame, intervalMsec);
}

export function getAnimationFrames(globalState: AugmentGlobalState) {
    const memoriesFileOpenCount =
        globalState.get<number>(GlobalContextKey.memoriesFileOpenCount) ?? 0;
    if (memoriesFileOpenCount <= 3) {
        return MEMORIES_ANIMATION_FRAMES;
    } else {
        return MEMORIES_ANIMATION_FRAMES_SHORT;
    }
}

export function onMemoriesFileDidChange(
    event: vscode.TextDocumentChangeEvent,
    editor: vscode.TextEditor | undefined,
    getAgentMemoriesAbsPath: () => string | undefined,
    globalState: AugmentGlobalState,
    toolsModel: ToolsModel
): void {
    // Force an update of the memory snapshot
    void toolsModel.forceMemorySnapshotUpdate();

    if (event.document.uri.fsPath !== getAgentMemoriesAbsPath()) {
        return;
    }

    if (!editor || editor.document !== event.document) {
        return;
    }

    const frames = getAnimationFrames(globalState);
    if (event.document.getText().startsWith("\n".repeat(getNumLinesInHeader(frames)))) {
        return;
    }

    for (const change of event.contentChanges) {
        if (change.range.start.line < getNumLinesInHeader(frames)) {
            void updateMemoriesFileDecorations(editor, getAgentMemoriesAbsPath, globalState);
        }
    }
}

const MIN_FILES_FOR_ORIENTATION = 20;

export class InitialOrientationTools {
    private readonly toolDefinitions: ToolDefinition[] = [
        /* eslint-disable @typescript-eslint/naming-convention */
        {
            name: "ls",
            description: "Lists a content of a folder by a relative path to it.",
            input_schema_json: JSON.stringify({
                type: "object",
                properties: {
                    folder: {
                        type: "string",
                        description: "Relative path to a folder. Can be either '.' or './*'",
                    },
                },
                required: ["folder"],
            }),
            tool_safety: ToolSafety.Safe,
        },
        {
            name: "read-file",
            description: "Read a file.",
            input_schema_json: JSON.stringify({
                type: "object",
                properties: {
                    file_path: {
                        type: "string",
                        description: "The path of the file to read.",
                    },
                },
                required: ["file_path"],
            }),
            tool_safety: ToolSafety.Safe,
        },
        {
            name: "complete",
            description: "Tool which should be called at the very end to return final response.",
            input_schema_json: JSON.stringify({
                type: "object",
                properties: {
                    response: {
                        type: "string",
                        description: "Final response to the user.",
                    },
                },
                required: ["response"],
            }),
            tool_safety: ToolSafety.Safe,
        },
        {
            name: "codebase-retrieval",
            description:
                "Use this tool to request information from the codebase. It will return relevant snippets for the requested information.",
            input_schema_json: JSON.stringify({
                type: "object",
                properties: {
                    information_request: {
                        type: "string",
                        description: "A description of the information you need.",
                    },
                },
                required: ["information_request"],
            }),
            tool_safety: ToolSafety.Safe,
        },
        /* eslint-enable @typescript-eslint/naming-convention */
    ];

    constructor(
        private readonly workspaceManager: WorkspaceManager,
        private readonly apiServer: APIServer,
        private readonly logger: AugmentLogger,
        private readonly progressTracker: ProgressTracker
    ) {}

    getToolDefinitions(): ToolDefinition[] {
        return this.toolDefinitions;
    }

    async runTool(toolName: string, toolInput: Record<string, any>): Promise<ToolUseResponse> {
        switch (toolName) {
            case "codebase-retrieval": {
                const requestId = this.apiServer.createRequestId();
                const informationRequest = toolInput.information_request as string;
                try {
                    const result = await this.apiServer.agentCodebaseRetrieval(
                        requestId,
                        informationRequest,
                        this.workspaceManager.getContext().blobs,
                        [],
                        20000
                    );
                    return successToolResponse(result.formattedRetrieval, requestId);
                } catch (e: any) {
                    this.logger.error(`Failed to retrieve codebase information: ${e}`);
                    return errorToolResponse(`Failed to retrieve codebase information.`);
                }
            }
            case "ls": {
                const folder = toolInput.folder as string;
                const absPath = _getQualifiedPath(folder, this.workspaceManager)?.absPath;

                if (absPath === undefined) {
                    return errorToolResponse(`Failed to list directory: ${folder}`);
                }

                if (!(await directoryExistsAsync(absPath))) {
                    return errorToolResponse(`Directory does not exist: ${folder}`);
                }

                try {
                    const files = await readDirectory(absPath);
                    const fileNames = files.map(([name, _]) => name);
                    return successToolResponse(fileNames.join("\n"));
                } catch (e) {
                    this.logger.error(`Failed to list directory: ${folder}`);
                    return errorToolResponse(`Failed to list directory: ${folder}`);
                }
            }
            case "read-file": {
                const filePath = toolInput.file_path as string;
                const absPath = _getQualifiedPath(filePath, this.workspaceManager)?.absPath;
                if (absPath === undefined) {
                    return errorToolResponse(`Failed to read file: ${filePath}`);
                }

                if (!fileExists(absPath)) {
                    return errorToolResponse(`File does not exist: ${filePath}`);
                }

                const content = await readFileUtf8(absPath);
                if (content === undefined) {
                    this.logger.error(`Failed to read file: ${filePath}`);
                    return errorToolResponse(`Failed to read file: ${filePath}`);
                }
                return successToolResponse(content);
            }
            default:
                return errorToolResponse(`Unknown tool: ${toolName}`);
        }
    }
}

export class InitialOrientation {
    private readonly localizationPrompt: string;
    private readonly detectLanguagesPrompt: string;
    private readonly orientationCompressionPrompt: string;
    private readonly orientationMaxLanguages: number;
    private readonly buildTestQuery: string;
    private readonly tools: InitialOrientationTools;

    // Describes what type of information we want to extract from the codebase
    private readonly queries: { name: string; template: string }[];

    // Regex to parse compression response
    static readonly agentMdPattern = new RegExp(
        "<agent_md>" + "(?:\\s*```(?:\\w+)?\\n?)?" + "(.*?)" + "(?:```\\s*)?" + "</agent_md>",
        "s"
    );

    constructor(
        private readonly rootAbsPath: string,
        private readonly apiServer: APIServer,
        private readonly workspaceManager: WorkspaceManager,
        private readonly featureFlagManager: FeatureFlagManager,
        private readonly logger: AugmentLogger,
        private readonly orientationConcurrencyLevel: number,
        private readonly progressTracker: ProgressTracker,
        private readonly trace: InitialOrientationData
    ) {
        this.localizationPrompt = this.featureFlagManager.currentFlags.memoriesParams
            .language_localization_prompt as string;
        if (!this.localizationPrompt) {
            this.trace.setFlag(InitialOrientationDebugFlag.localizationPromptMissing);
            throw new Error("Localization prompt missing");
        }

        this.detectLanguagesPrompt = this.featureFlagManager.currentFlags.memoriesParams
            .detect_languages_prompt as string;
        if (!this.detectLanguagesPrompt) {
            this.trace.setFlag(InitialOrientationDebugFlag.detectLanguagesPromptMissing);
            throw new Error("Detect languages prompt missing");
        }

        this.orientationCompressionPrompt = this.featureFlagManager.currentFlags.memoriesParams
            .orientation_compression_prompt as string;
        if (!this.orientationCompressionPrompt) {
            this.trace.setFlag(InitialOrientationDebugFlag.orientationCompressionPromptMissing);
            throw new Error("Orientation compression prompt missing");
        }

        this.orientationMaxLanguages = this.featureFlagManager.currentFlags.memoriesParams
            .orientation_max_languages as number;
        if (!this.orientationMaxLanguages) {
            this.trace.setFlag(InitialOrientationDebugFlag.orientationMaxLanguagesMissing);
            throw new Error("Orientation max languages missing");
        }

        this.buildTestQuery = this.featureFlagManager.currentFlags.memoriesParams
            .orientation_build_test_query as string;
        if (!this.buildTestQuery) {
            this.trace.setFlag(InitialOrientationDebugFlag.orientationBuildTestQueryMissing);
            throw new Error("Build test prompt missing");
        }

        this.queries = [
            {
                name: "build-test",
                template: this.buildTestQuery,
            },
        ];

        this.tools = new InitialOrientationTools(
            workspaceManager,
            apiServer,
            this.logger,
            progressTracker
        );
    }

    /**
     * Updates the workspace guidelines file with orientation results.
     * If the file already contains orientation results between markers, it replaces that section.
     * Otherwise, it appends the new content with markers.
     */
    async appendToWorkspaceGuidelines(content: string): Promise<void> {
        // Get the workspace root path
        const rootAbsPath = this.workspaceManager.getBestFolderRoot();
        if (!rootAbsPath) {
            this.trace.setFlag(InitialOrientationDebugFlag.noRootFolderFound);
            throw new Error("No root folder found");
        }
        const guidelinesPath = path.join(rootAbsPath, AUGMENT_GUIDELINES_FILE);

        // Read existing content if the file exists
        let existingContent = "";
        try {
            if (fileExists(guidelinesPath)) {
                existingContent = await readFileUtf8(guidelinesPath);
            }
        } catch (error) {
            this.trace.setFlag(InitialOrientationDebugFlag.failedToReadGuidelines);
            // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
            this.logger.error(`Failed to read existing guidelines: ${error}`);
            // Continue with empty content if file doesn't exist or can't be read
        }

        let updatedContent = "";

        // Check if markers already exist in the file
        const startIndex = existingContent.indexOf(ORIENTATION_START_MARKER);
        const endIndex = existingContent.indexOf(ORIENTATION_END_MARKER);

        if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
            // Markers exist, replace content between them
            const beforeMarker = existingContent.substring(0, startIndex);
            const afterMarker = existingContent.substring(endIndex + ORIENTATION_END_MARKER.length);

            updatedContent =
                beforeMarker +
                ORIENTATION_START_MARKER +
                "\n" +
                content +
                "\n" +
                ORIENTATION_END_MARKER +
                afterMarker;

            this.logger.debug("Replacing existing orientation results between markers");
        } else {
            // Markers don't exist, append new content with markers
            const separator = existingContent.trim().length > 0 ? "\n\n" : "";
            updatedContent =
                existingContent.trim() +
                separator +
                ORIENTATION_START_MARKER +
                "\n" +
                content +
                "\n" +
                ORIENTATION_END_MARKER;

            this.logger.debug("Appending new orientation results with markers");
        }

        try {
            await vscode.workspace.fs.writeFile(
                vscode.Uri.file(guidelinesPath),
                Buffer.from(updatedContent, "utf8")
            );
            this.logger.debug(`Successfully updated workspace guidelines at ${guidelinesPath}`);
        } catch (error) {
            this.trace.setFlag(InitialOrientationDebugFlag.failedToWriteGuidelines);
            // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
            throw new Error(`Failed to write workspace guidelines: ${error}`);
        }
    }

    /**
     * Saves the compressed knowledge to a .augment/rules/auto-generated.md file.
     */
    async putToRules(content: string): Promise<void> {
        // Get the workspace root path
        const rootAbsPath = this.workspaceManager.getBestFolderRoot();
        if (!rootAbsPath) {
            this.trace.setFlag(InitialOrientationDebugFlag.noRootFolderFound);
            throw new Error("No root folder found");
        }

        // Create the rules directory path
        const rulesDir = path.join(rootAbsPath, AUGMENT_DIRECTORY_ROOT, AUGMENT_RULES_FOLDER);
        const rulesFilePath = path.join(rulesDir, "auto-generated.md");

        try {
            // Create the .augment/rules directory if it doesn't exist
            if (!(await directoryExistsAsync(path.join(rootAbsPath, AUGMENT_DIRECTORY_ROOT)))) {
                await vscode.workspace.fs.createDirectory(
                    vscode.Uri.file(path.join(rootAbsPath, AUGMENT_DIRECTORY_ROOT))
                );
            }

            if (!(await directoryExistsAsync(rulesDir))) {
                await vscode.workspace.fs.createDirectory(vscode.Uri.file(rulesDir));
            }

            // Add frontmatter to the content
            const contentWithFrontmatter = RulesParser.formatRuleFileForMarkdown({
                type: RuleType.ALWAYS_ATTACHED,
                path: "auto-generated.md",
                content,
            });

            // Write the content to the file
            await vscode.workspace.fs.writeFile(
                vscode.Uri.file(rulesFilePath),
                Buffer.from(contentWithFrontmatter, "utf8")
            );

            this.logger.debug(`Successfully saved build and test rules at ${rulesFilePath}`);
        } catch (error) {
            this.trace.setFlag(InitialOrientationDebugFlag.failedToWriteGuidelines);
            // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
            throw new Error(`Failed to write rules file: ${error}`);
        }
    }

    async run(): Promise<void> {
        // Step 1: we get the list of programming languages used in the codebase
        const { languages: languageToExtensions, allFiles } =
            await this.getTopProgrammingLanguages();
        if (Object.keys(languageToExtensions).length === 0) {
            throw new Error("No programming languages detected");
        }
        this.progressTracker.set(10);

        // Step 2: we map each programming language to parts of the codebase where it is used
        const languageToLocations = await this.localizeLanguages(allFiles, languageToExtensions);
        this.progressTracker.set(25);

        // List of files in the root folder
        const contentOfRootFolder = (await _listFiles(".", this.workspaceManager))?.join("\n");
        if (!contentOfRootFolder) {
            this.trace.setFlag(InitialOrientationDebugFlag.failedToListRootFolder);
            throw new Error("Failed to list root folder");
        }
        this.progressTracker.set(30);

        // Step 3: we go over all programming languages with controlled parallelism
        const limit = pLimit(this.orientationConcurrencyLevel);
        const languages = Object.keys(languageToExtensions);

        const agenticProgressBudget = 60;
        const progressPerAgenticTurn =
            agenticProgressBudget / languages.length / this.queries.length;

        // Create an array of limited promises
        this.trace.setFlag(InitialOrientationDebugFlag.agenticStarted);
        const promises = languages.map((language, index) => {
            return limit(async () => {
                this.logger.debug(`Processing language: ${language}`);
                const results: string[] = [];

                // Process all queries for this language
                for (const query of this.queries) {
                    this.logger.debug(`Processing query: ${query.name}`);
                    const prompt = query.template
                        .replace(/{language}/g, language)
                        .replace(/{rootFolderContent}/g, contentOfRootFolder)
                        .replace(
                            /{locationList}/g,
                            languageToLocations[language].split(",").join("\n")
                        );

                    const response = await this.doAgenticTurn(
                        prompt,
                        progressPerAgenticTurn,
                        index
                    );
                    results.push(response);
                    this.logger.debug(
                        `Response language "${language}" and query "${query.name}": ${response}`
                    );
                }

                return results;
            });
        });

        // Wait for all promises to resolve and flatten the results
        const nestedResults = await Promise.all(promises);
        this.trace.setFlag(InitialOrientationDebugFlag.agenticEnded);
        const assembledKnowledge = nestedResults.flat().join("\n\n");
        this.trace.setStringStats(
            InitialOrientationDebugFlag.agenticModelResponseStats,
            assembledKnowledge
        );
        this.progressTracker.set(90);

        // Step 4: we compress all the knowledge we collected
        const compressionRequestId = this.apiServer.createRequestId();
        this.trace.setRequestId(
            InitialOrientationDebugFlag.compressionRequestId,
            compressionRequestId
        );
        const compressionResponse = await this.simpleLlmCall(
            this.orientationCompressionPrompt.replace(/{assembledKnowledge}/g, assembledKnowledge),
            compressionRequestId
        );
        this.trace.setStringStats(
            InitialOrientationDebugFlag.compressionModelResponseStats,
            compressionResponse
        );

        const agentMdMatch = compressionResponse.match(InitialOrientation.agentMdPattern);
        if (!agentMdMatch) {
            this.trace.setFlag(InitialOrientationDebugFlag.compressionParsingFailed);
            throw new Error("Failed to parse compression response");
        }
        const compressedKnowledge = agentMdMatch[1].trim();
        this.logger.debug(`Compressed knowledge: ${compressedKnowledge}`);
        this.progressTracker.set(95);

        // Step 5: save the knowledge to the rules file
        this.trace.setFlag(InitialOrientationDebugFlag.rememberStarted);
        await this.putToRules(compressedKnowledge);
        this.trace.setFlag(InitialOrientationDebugFlag.rememberEnded);
        this.progressTracker.set(100);
    }

    /**
     * Simple implementation of agentic loop.
     */
    async doAgenticTurn(
        initialPrompt: string,
        progressBudget: number,
        index: number
    ): Promise<string> {
        let prompt = initialPrompt;

        // Nodes that contain tool call results
        let requestNodes: ChatRequestNode[] = [];

        // Full conversation history
        let chatHistory: Exchange[] = [];

        /* eslint-disable @typescript-eslint/naming-convention */
        let curExchange: Exchange = {
            request_message: prompt,
            response_text: "",
            request_id: this.apiServer.createRequestId(),
            request_nodes: [],
            response_nodes: [],
        };
        /* eslint-disable @typescript-eslint/naming-convention */

        let nextToolCall: ChatResultNode | undefined = undefined;

        // Number of turns after which we start nudging model into wrapping up
        const softMaxTurns = 25;
        // If model didn't stop for some reason after this number of steps, we throw exception
        const hardMaxTurns = softMaxTurns + 2;
        const progressPerTurn = progressBudget / hardMaxTurns;

        let turnI: number = 0;

        do {
            this.trace.setNum(`agenticNumTurns_${index}` as InitialOrientationDebugFlag, turnI);
            if (turnI > softMaxTurns) {
                if (turnI > hardMaxTurns) {
                    throw new Error("Too many turns in agentic loop");
                }
                requestNodes = [
                    ...requestNodes,
                    {
                        id: 2,
                        type: ChatRequestNodeType.TEXT,
                        text_node: {
                            content:
                                "You did too many turns already. Call `complete` tool immediately.",
                        },
                    },
                ];
            }

            this.logger.debug(`Running agentic turn ${turnI}.`);
            this.progressTracker.inc(progressPerTurn);

            const curRequestId = this.apiServer.createRequestId();
            const chatResultIter = await llmCallStream(
                prompt,
                curRequestId,
                chatHistory,
                this.tools.getToolDefinitions(),
                requestNodes,
                ChatMode.agent,
                true
            );

            for await (const result of chatResultIter) {
                if (result.nodes) {
                    let curNodes = result.nodes.filter(
                        (node) =>
                            node.type === ChatResultNodeType.TOOL_USE ||
                            node.type === ChatResultNodeType.RAW_RESPONSE
                    );
                    curExchange.response_nodes = curExchange.response_nodes || [];
                    curExchange.response_nodes.push(...curNodes);
                }
            }

            nextToolCall = curExchange.response_nodes?.find(
                (node) => node.type === ChatResultNodeType.TOOL_USE
            );

            if (nextToolCall?.tool_use !== undefined) {
                const toolUse = nextToolCall.tool_use;

                /* eslint-disable @typescript-eslint/naming-convention */
                const toolName = toolUse.tool_name;
                const toolInput = JSON.parse(toolUse.input_json) as Record<string, any>;
                /* eslint-disable @typescript-eslint/naming-convention */

                this.logger.debug(`Calling tool: ${toolName}. Args: ${JSON.stringify(toolInput)}`);

                if (toolName === "complete") {
                    this.logger.debug(`Agentic turn complete. Took ${turnI} turns.`);
                    this.progressTracker.inc(progressPerTurn * (hardMaxTurns - turnI - 1));
                    this.trace.setStringStats(
                        `agenticModelResponseStats_${index}` as InitialOrientationDebugFlag,
                        toolInput.response as string
                    );
                    return toolInput.response as string;
                }

                const toolResult = await this.tools.runTool(toolUse.tool_name, toolInput);
                requestNodes = [
                    {
                        id: 1,
                        type: ChatRequestNodeType.TOOL_RESULT,
                        /* eslint-disable @typescript-eslint/naming-convention */
                        tool_result_node: {
                            tool_use_id: toolUse.tool_use_id,
                            content: toolResult.text,
                            is_error: toolResult.isError,
                        },
                        /* eslint-disable @typescript-eslint/naming-convention */
                    },
                ];
                this.logger.debug(`Tool result: ${toolResult.text.trim().split("\n")[0]}...`);

                // Drop prompt, we don't need it after the first turn
                prompt = "";

                // Move current exchange to history
                chatHistory.push(curExchange);

                // Create new exchange with the tool result
                curExchange = {
                    request_message: "",
                    response_text: "",
                    request_id: this.apiServer.createRequestId(),
                    request_nodes: requestNodes,
                    response_nodes: [],
                };
            }

            turnI++;
        } while (nextToolCall?.tool_use !== undefined);

        this.trace.setFlag(`agenticFailedToComplete_${index}` as InitialOrientationDebugFlag);
        throw new Error("Agent didn't call the complete tool.");
    }

    /**
     * Here we try to figure out in which parts of the codebase different programming languages are used.
     */
    async localizeLanguages(
        allFiles: string[],
        languageToExtensions: Record<string, string[]>
    ): Promise<Record<string, string>> {
        // We map language name to a comma-separated list of paths.
        const languageToLocations: Record<string, string> = {};

        // We limit concurrency to avoid overloading the LLM
        const limit = pLimit(this.orientationConcurrencyLevel);

        // Progress before we start localizing languages is 10%
        const totalProgressBudget = 15;
        const progressPerLanguage = totalProgressBudget / Object.keys(languageToExtensions).length;

        // Create an array of limited promises
        this.trace.setFlag(InitialOrientationDebugFlag.localizationStarted);
        const promises = Object.entries(languageToExtensions).map(
            ([language, extensions], index) => {
                return limit(async () => {
                    this.logger.debug(`Localizing language: ${language} (index: ${index})`);

                    const tree = _renderFolderTree(allFiles, extensions);
                    if (tree.trim().length === 0) {
                        throw new Error(`Failed to render folder tree for ${language}`);
                    }

                    const prompt = this.localizationPrompt
                        .replace(/{programmingLanguage}/g, language)
                        .replace(/{languageTree}/g, tree);
                    this.trace.setStringStats(
                        `localizationPromptStats_${index}` as InitialOrientationDebugFlag,
                        prompt
                    );
                    const requestId = this.apiServer.createRequestId();
                    this.trace.setRequestId(
                        `localizationRequestId_${index}` as InitialOrientationDebugFlag,
                        requestId
                    );
                    const response = await this.simpleLlmCall(prompt, requestId);
                    this.trace.setStringStats(
                        `localizationResponseStats_${index}` as InitialOrientationDebugFlag,
                        response
                    );

                    // Extract locations from XML tags
                    const locationsMatch = response.match(/<locations>(.*?)<\/locations>/);
                    if (!locationsMatch) {
                        this.trace.setFlag(
                            `localizationParsingFailed_${index}` as InitialOrientationDebugFlag
                        );
                        throw new Error(`Failed to extract locations from response: ${response}`);
                    }

                    this.progressTracker.inc(progressPerLanguage);
                    return { language, locations: locationsMatch[1].trim() };
                });
            }
        );

        // Wait for all promises to resolve
        const results = await Promise.all(promises);
        this.trace.setFlag(InitialOrientationDebugFlag.localizationEnded);

        // Populate the languageToLocations map
        results.forEach(({ language, locations }, index) => {
            const numLocations = locations.split(",").length;
            this.trace.setNum(
                `localizationNumLocations_${index}` as InitialOrientationDebugFlag,
                numLocations
            );
            languageToLocations[language] = locations;
        });
        return languageToLocations;
    }

    /**
     * Here we try to figure out which programming languages are used in the codebase.
     */
    async getTopProgrammingLanguages(): Promise<{
        languages: Record<string, string[]>;
        allFiles: string[];
    }> {
        const allowedExtensions = new Set([
            "ad",
            "adown",
            "argdown",
            "argdn",
            "bicep",
            "c",
            "cpp",
            "cc",
            "cp",
            "cxx",
            "h",
            "hpp",
            "hxx",
            "cs",
            "ex",
            "elm",
            "erb",
            "rhtml",
            "gd",
            "godot",
            "tres",
            "tscn",
            "go",
            "haml",
            "hs",
            "hx",
            "html",
            "htm",
            "java",
            "js",
            "jsx",
            "kt",
            "ml",
            "mli",
            "mll",
            "mly",
            "php",
            "py",
            "r",
            "rb",
            "rs",
            "res",
            "resi",
            "sass",
            "scala",
            "styl",
            "swift",
            "tf",
            "tfvars",
            "ts",
            "tsx",
            "vue",
            "vala",
        ]);

        const stats: Record<string, number> = {};
        const allFiles: string[] = [];

        const processDirectory = async (dirPath: string) => {
            const entries = await readDirectory(dirPath);
            if (!entries) {
                return;
            }

            for (const [name, type] of entries) {
                if (name.startsWith(".")) {
                    continue;
                }

                const fullPath = path.join(dirPath, name);

                if (type === FileType.directory) {
                    await processDirectory(fullPath);
                } else if (type === FileType.file) {
                    // Store relative path instead of absolute path
                    const relativePath = path.relative(this.rootAbsPath, fullPath);
                    allFiles.push(relativePath);

                    const ext = name.includes(".") ? name.split(".").pop()!.toLowerCase() : "";
                    if (ext && allowedExtensions.has(ext)) {
                        stats[ext] = (stats[ext] || 0) + 1;
                    }
                }
            }
        };
        await processDirectory(this.rootAbsPath);
        this.trace.setNum(InitialOrientationDebugFlag.topLanguagesNumFiles, allFiles.length);

        const numCodeFiles = Object.values(stats).reduce((sum, count) => sum + count, 0);
        this.trace.setNum(InitialOrientationDebugFlag.topLanguagesNumCodeFiles, numCodeFiles);
        if (numCodeFiles < MIN_FILES_FOR_ORIENTATION) {
            throw Error("Not enough code files to run orientation");
        }

        const top20 = Object.entries(stats)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 20)
            .map(([ext, count]) => `${ext}: ${count}`)
            .join("\n");

        const requestId = this.apiServer.createRequestId();
        this.trace.setRequestId(InitialOrientationDebugFlag.topLanguagesRequestId, requestId);
        const response = await this.simpleLlmCall(
            this.detectLanguagesPrompt.replace(/{fileExtensionsList}/g, top20),
            requestId
        );
        this.trace.setStringStats(
            InitialOrientationDebugFlag.topLanguagesModelResponseStats,
            response
        );

        this.logger.debug(`Detected languages: "${response}"`);
        const languages = JSON.parse(response.trim()) as Record<string, string[]>;
        this.trace.setNum(
            InitialOrientationDebugFlag.topLanguagesNumDetectedLanguages,
            Object.keys(languages).length
        );

        // Calculate number of files per language family
        const languageFileCounts: Record<string, number> = {};
        for (const [language, exts] of Object.entries(languages)) {
            languageFileCounts[language] = exts.reduce((count, ext) => {
                return count + (stats[ext] || 0);
            }, 0);
            this.logger.debug(`${language}: ${languageFileCounts[language]} files`);
        }

        // Keep only top N languages by file count
        // Do it to make orientation process faster
        const topLanguages = Object.entries(languageFileCounts)
            .sort(([, countA], [, countB]) => countB - countA)
            .slice(0, this.orientationMaxLanguages)
            .map(([lang]) => lang);

        const filteredLanguages: Record<string, string[]> = {};
        for (const lang of topLanguages) {
            filteredLanguages[lang] = languages[lang];
        }

        this.logger.debug(
            `Top ${this.orientationMaxLanguages} languages: ${JSON.stringify(filteredLanguages)}`
        );
        this.trace.setNum(
            InitialOrientationDebugFlag.topLanguagesNumFinalLanguages,
            Object.keys(filteredLanguages).length
        );

        return { languages: filteredLanguages, allFiles };
    }

    /**
     * Simple LLM call without any tools.
     * Returns content of RAW_RESPONSE node.
     */
    async simpleLlmCall(prompt: string, requestId: string): Promise<string> {
        const chatResultIter = await llmCallStream(
            prompt,
            requestId,
            [], // empty chat history
            [], // no tools
            [], // no request nodes
            ChatMode.orientation,
            true // silent
        );

        for await (const result of chatResultIter) {
            if (result.nodes) {
                for (const node of result.nodes) {
                    if (node.type === ChatResultNodeType.RAW_RESPONSE) {
                        return node.content;
                    }
                }
            }
        }

        throw new Error("No response from model");
    }
}

interface FileTreeNode {
    isDirectory: boolean;
    children: Map<string, FileTreeNode>;
}

/**
 * Renders a tree of folders and files.
 */
function _renderTree(
    node: FileTreeNode,
    prefix: string = "",
    isLast: boolean = true,
    nodeName: string = "",
    depth: number = 0,
    maxDepth: number = 3
): string {
    if (depth > maxDepth) {
        return "";
    }

    const lines: string[] = [];

    if (nodeName) {
        const displayName = node.isDirectory ? `${nodeName}/` : nodeName;
        lines.push(`${prefix}${isLast ? "└── " : "├── "}${displayName}`);
    }

    const childPrefix = prefix + (isLast ? "    " : "│   ");
    const entries = Array.from(node.children.entries()).sort(([a], [b]) => a.localeCompare(b));

    entries.forEach(([name, childNode], index) => {
        const isLastChild = index === entries.length - 1;
        const childTree = _renderTree(
            childNode,
            childPrefix,
            isLastChild,
            name,
            depth + 1,
            maxDepth
        );
        if (childTree) {
            lines.push(childTree);
        }
    });

    return lines.join("\n");
}

/**
 * Renders a tree of folders containing files with given extensions.
 */
export function _renderFolderTree(allFiles: string[], extensions: string[]): string {
    const files = allFiles.filter((file) => {
        const ext = file.split(".").pop()?.toLowerCase();
        return ext && extensions.includes(ext);
    });

    // If no files found, return empty string
    if (files.length === 0) {
        return "";
    }

    // Build tree from file paths
    const tree: FileTreeNode = { isDirectory: true, children: new Map() };

    for (const file of files) {
        const relativePath = vscode.workspace.asRelativePath(file);
        const parts = relativePath.split(path.sep);

        let current = tree;
        // Process all directories in the path
        for (let i = 0; i < parts.length - 1; i++) {
            const part = parts[i];
            if (!current.children.has(part)) {
                current.children.set(part, {
                    isDirectory: true,
                    children: new Map(),
                });
            }
            current = current.children.get(part)!;
        }
    }

    // If no children found, return empty string
    if (tree.children.size === 0) {
        return "";
    }

    return _renderTree(tree);
}

export interface ProgressTracker {
    inc: (increment: number) => void;
    set: (value: number) => void;
    reset: () => void;
}

let orientationStatus: OrientationStatus = { state: OrientationState.idle };

// Create an event emitter for orientation status changes
const orientationStatusChangedEmitter = new EventEmitter<OrientationStatus>();
export const onOrientationStatusChanged = orientationStatusChangedEmitter.event;

/**
 * Notify that the orientation status has changed.
 */
export function sendOrientationStatusToWebviews(): void {
    // Fire an event to notify that orientation status has changed and pass the current status
    orientationStatusChangedEmitter.fire(orientationStatus);
}

/**
 * Storage key for tracking orientation run count per workspace
 */
export const ORIENTATION_RUN_COUNT_STORAGE_KEY = "orientation.runCount";

/**
 * Markers than enclose orientation result
 */
export const ORIENTATION_START_MARKER = "[//]: # (AUGMENT-CODEBASE-ORIENTATION-RESULTS-START)";
export const ORIENTATION_END_MARKER = "[//]: # (AUGMENT-CODEBASE-ORIENTATION-RESULTS-END)";

/**
 * Runs the automatic orientation process when syncing is complete.
 */
export async function runAutomaticOrientation(
    apiServer: APIServer,
    workspaceManager: WorkspaceManager,
    featureFlagManager: FeatureFlagManager,
    checkpointManager: AggregateCheckpointManager,
    workspaceStorage: vscode.Memento
): Promise<void> {
    try {
        // Track orientation run count
        const runCount = workspaceStorage.get<number>(ORIENTATION_RUN_COUNT_STORAGE_KEY) || 0;
        await workspaceStorage.update(ORIENTATION_RUN_COUNT_STORAGE_KEY, runCount + 1);
        if (runCount > 0) {
            // To make sure we don't enter the crash loop, we only try orientation once.
            // Potentially, maybe will consider few retries.
            return;
        }

        const rootAbsPath = workspaceManager.getBestFolderRoot();
        if (!rootAbsPath) {
            throw Error("Root of the project is `undefined`.");
        }

        const guidelinesPath = path.join(rootAbsPath, AUGMENT_GUIDELINES_FILE);
        if (fileExists(guidelinesPath)) {
            const guidelinesContent = await readFileUtf8(guidelinesPath);
            if (guidelinesContent && guidelinesContent.includes(ORIENTATION_START_MARKER)) {
                // Orientation was already done on this repo
                return;
            }
        }

        // Run the actual orientation process
        await runInitialOrientationWithProgressSingleton(
            apiServer,
            workspaceManager,
            featureFlagManager,
            checkpointManager,
            InitialOrientationCaller.automaticAfterIndexing
        );
    } catch (error) {
        const logger = getLogger("AutomaticOrientation");
        logger.error(`Error running automatic orientation.`);
    }
}

export async function runInitialOrientationWithProgressSingleton(
    apiServer: APIServer,
    workspaceManager: WorkspaceManager,
    featureFlagManager: FeatureFlagManager,
    checkpointManager: AggregateCheckpointManager,
    caller: InitialOrientationCaller
): Promise<void> {
    if (orientationStatus.state === OrientationState.inProgress) {
        void vscode.window.showInformationMessage(
            "Augment Agent orientation is already in progress"
        );
        return;
    }
    const logger = getLogger("InitialOrientation");

    const trace = InitialOrientationData.create(caller);
    trace.setFlag(InitialOrientationDebugFlag.start);

    try {
        orientationStatus = {
            state: OrientationState.inProgress,
            progress: 0,
            lastRunTimestamp: Date.now(),
        };
        sendOrientationStatusToWebviews();
        await _runInitialOrientationWithProgress(
            apiServer,
            workspaceManager,
            featureFlagManager,
            checkpointManager,
            logger,
            trace
        );
        orientationStatus = {
            state: OrientationState.succeeded,
            progress: 100,
            lastRunTimestamp: Date.now(),
        };
        sendOrientationStatusToWebviews();
    } catch (e) {
        // Orientation failed
        trace.setFlag(InitialOrientationDebugFlag.exceptionThrown);
        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
        logger.error(`Error in initial orientation: ${e}`);

        // In most cases, error message will not be informative to user, so we replace it with code.
        let errorMessage = "";
        let state = OrientationState.failed;

        // Setting default to MIN_FILES_FOR_ORIENTATION, so that in case of irrelevat failure, we don't
        // show user that their repo is too small
        const numCodeFiles =
            trace.getNum(InitialOrientationDebugFlag.topLanguagesNumCodeFiles) ??
            MIN_FILES_FOR_ORIENTATION;
        if (numCodeFiles === 0) {
            state = OrientationState.aborted;
            errorMessage = `Code files were not found`;
        } else if (numCodeFiles < MIN_FILES_FOR_ORIENTATION) {
            state = OrientationState.aborted;
            errorMessage = `Repository is too small`;
        } else if (trace.getFlag(InitialOrientationDebugFlag.compressionParsingFailed)) {
            errorMessage = `Error code #${OrientationErrorCode.compressionFailed}`;
        } else if (
            trace.getFlag(InitialOrientationDebugFlag.localizationStarted) &&
            !trace.getFlag(InitialOrientationDebugFlag.localizationEnded)
        ) {
            errorMessage = `Error code #${OrientationErrorCode.localizationFailed}`;
        } else if (
            trace.getFlag(InitialOrientationDebugFlag.agenticStarted) &&
            !trace.getFlag(InitialOrientationDebugFlag.agenticEnded)
        ) {
            errorMessage = `Error code #${OrientationErrorCode.agenticFailed}`;
        } else if (
            trace.getFlag(InitialOrientationDebugFlag.rememberStarted) &&
            !trace.getFlag(InitialOrientationDebugFlag.rememberEnded)
        ) {
            errorMessage = `Error code #${OrientationErrorCode.rememberFailed}`;
        }

        orientationStatus = {
            state: state,
            progress: 0,
            lastRunTimestamp: Date.now(),
            errorMessage: errorMessage,
        };
        sendOrientationStatusToWebviews();
    } finally {
        trace.setFlag(InitialOrientationDebugFlag.end);
        getAgentSessionEventReporter().reportEvent({
            eventName: AgentSessionEventName.initialOrientation,
            // To fix it we need to pipe the conversation ID from the webview.
            conversationId: "",
            eventData: {
                initialOrientationData: trace,
            },
        });
    }
}

async function _runInitialOrientationWithProgress(
    apiServer: APIServer,
    workspaceManager: WorkspaceManager,
    featureFlagManager: FeatureFlagManager,
    checkpointManager: AggregateCheckpointManager,
    logger: AugmentLogger,
    trace: InitialOrientationData
): Promise<void> {
    logger.debug(`Starting initial orientation process.`);

    const concurrencyLevel = featureFlagManager.currentFlags.memoriesParams
        ?.orientation_concurrency_level as number;
    if (!concurrencyLevel) {
        trace.setFlag(InitialOrientationDebugFlag.concurrencyLevelMissing);
        throw Error("Failed to get concurrency level for initial orientation.");
    }

    // eslint-disable-next-line @typescript-eslint/naming-convention
    if (!featureFlagManager.currentFlags.memoriesParams?.enable_initial_orientation) {
        trace.setFlag(InitialOrientationDebugFlag.initialOrientationDisabled);
        throw Error("Initial orientation is not enabled.");
    }

    const createProgressTracker = (
        progress: vscode.Progress<{ message?: string; increment?: number }>
    ) => {
        let current = 0;
        return {
            inc: (increment: number) => {
                // This is possible when one thread failed, but second one is still working.
                if (orientationStatus.state === OrientationState.failed) {
                    return;
                }

                current = Math.min(current + increment, 100);
                progress.report({ increment: 0, message: `${Math.round(current)}%` });
                orientationStatus = {
                    state: OrientationState.inProgress,
                    progress: Math.round(current),
                    lastRunTimestamp: Date.now(),
                };
                sendOrientationStatusToWebviews();
            },
            set: (value: number) => {
                if (value <= current) {
                    return;
                }
                value = Math.min(value, 100);
                current = value;
                progress.report({
                    increment: 0,
                    message: `${Math.round(current)}%`,
                });
                orientationStatus = {
                    state: OrientationState.inProgress,
                    progress: Math.round(current),
                    lastRunTimestamp: Date.now(),
                };
                sendOrientationStatusToWebviews();
            },
            reset: () => {
                progress.report({ increment: 0, message: `0%` });
                current = 0;
                orientationStatus = {
                    state: OrientationState.inProgress,
                    progress: 0,
                    lastRunTimestamp: Date.now(),
                };
                sendOrientationStatusToWebviews();
            },
        };
    };

    await vscode.window.withProgress(
        {
            location: vscode.ProgressLocation.Window,
            title: "Augment Codebase Orientation",
        },
        async (progress, _) => {
            const tracker = createProgressTracker(progress);
            tracker.reset();
            await _runInitialOrientation(
                apiServer,
                workspaceManager,
                featureFlagManager,
                checkpointManager,
                logger,
                concurrencyLevel,
                tracker,
                trace
            );
        }
    );
}

async function _runInitialOrientation(
    apiServer: APIServer,
    workspaceManager: WorkspaceManager,
    featureFlagManager: FeatureFlagManager,
    checkpointManager: AggregateCheckpointManager,
    logger: AugmentLogger,
    concurrencyLevel: number,
    progressTracker: ProgressTracker,
    trace: InitialOrientationData
): Promise<void> {
    // Doing this slightly weird progress updates to make potential debugging easier.
    progressTracker.set(1);

    try {
        const rootAbsPath = workspaceManager.getBestFolderRoot();
        if (!rootAbsPath) {
            trace.setFlag(InitialOrientationDebugFlag.noRootFolderFound);
            throw new Error("No root folder found");
        }
        logger.debug(`Root folder: ${rootAbsPath}.`);
        progressTracker.set(2);

        progressTracker.set(3);

        const initialOrientation = new InitialOrientation(
            rootAbsPath,
            apiServer,
            workspaceManager,
            featureFlagManager,
            logger,
            concurrencyLevel,
            progressTracker,
            trace
        );
        progressTracker.set(5);

        await initialOrientation.run();
        progressTracker.set(100);

        void vscode.window.showInformationMessage(
            `Augment Agent completed orientation process. Workspace guidelines were updated!`
        );
    } catch (e) {
        if (concurrencyLevel > 1 && APIError.isAPIErrorWithStatus(e, APIStatus.resourceExhausted)) {
            trace.setFlag(InitialOrientationDebugFlag.retryWithLowerConcurrencyLevel);
            void vscode.window.showErrorMessage(
                `Augment agent orientation process failed: Rate limit exceeded. Retrying...`
            );
            logger.error(`Retrying initial orientation with concurrency level 1.`);

            // Sleep for 5 seconds before retrying.
            await new Promise((resolve) => setTimeout(resolve, 5000));

            progressTracker.reset();
            await _runInitialOrientation(
                apiServer,
                workspaceManager,
                featureFlagManager,
                checkpointManager,
                logger,
                1,
                progressTracker,
                trace
            );
            return;
        }

        throw e;
    }
}
