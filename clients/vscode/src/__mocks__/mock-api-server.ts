import { Blobs } from "@augment-internal/sidecar-libs/src/api/types";
import {
    ChatMode,
    ChatRequestNode,
    ChatResultNode,
    Exchange,
    PersonaType,
    ReplacementText,
    Rule,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
    AgentCodebaseRetrievalResult,
    AgentRequestEvent,
    AgentSessionEvent,
    ChatResult,
    RemoteAgentSessionEvent,
    ToolUseRequestEvent,
} from "@augment-internal/sidecar-libs/src/client-interfaces/api-client-types";
import { RunRemoteToolResult } from "@augment-internal/sidecar-libs/src/tools/remote-tools/remote-tool-host";
import {
    ExtraToolInput,
    RemoteToolId,
    ToolDefinition,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { VCSChange } from "@augment-internal/sidecar-libs/src/vcs/watcher/types";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import * as vscode from "vscode";

import { APITiming } from "../api/api-timing";
import {
    AgentEditFileResult,
    APIServer,
    AutofixCheckResponse,
    AutofixPlanResponse,
    BatchUploadResult,
    BlobMetadata,
    ChatInstructionStreamResult,
    CheckCommandResult,
    CheckpointBlobsResult,
    ClientCompletionTimline,
    ClientMetric,
    CodeEditResolution,
    CodeEditResult,
    CompletionLocation,
    CompletionResolution,
    CompletionResult,
    ContainErrorsResult,
    Diagnostic,
    ExtensionSessionEvent,
    FilePatch,
    FindMissingResult,
    FixPlanResult,
    GenerateCommitMessageResult,
    GetSubscriptionInfoResponse,
    InstructionResolution,
    Language,
    ListRemoteToolsResult,
    MemorizeResult,
    Model,
    ModelConfig,
    NextEditGenerationResult,
    NextEditLocationResult,
    NextEditResolution,
    NextEditSessionEvent,
    NextEditStreamRequest,
    OnboardingSessionEvent,
    PreferenceSample,
    RecencyInfo,
    type RevokeToolAccessResult,
    SaveChatResult,
    SearchExternalSourcesResponse,
    SmartPasteResolution,
    UploadBlob,
} from "../augment-api";
import { ChatFeedback } from "../chat/chat-types";
import { CompletionFeedback } from "../completions/completion-types";
import { UnknownModelError } from "../exceptions";
import { defaultFeatureFlags } from "../feature-flags";
import { FeatureVector } from "../feature-vector/feature-vector-collector";
import { FileEditEvent } from "../next-edit/file-edit-events";
import { NextEditFeedback } from "../next-edit/next-edit-types";
import {
    CreateRemoteAgentResponse,
    GetRemoteAgentChatHistoryResponse,
    GetRemoteAgentHistoryStreamResponse,
    GithubRepo,
    IsUserGithubConfiguredResponse,
    ListGithubRepoBranchesResponse,
    ListGithubReposForAuthenticatedUserResponse,
    ListRemoteAgentsResponse,
    RemoteAgentAddSSHKeyResponse,
    RemoteAgentChatRequestDetails,
    RemoteAgentStatus,
    RemoteAgentWorkspaceLogsResponse,
    RemoteAgentWorkspaceSetup,
} from "../remote-agent-manager/types";
import { makePromise } from "../utils/promise-utils";
import { KeyValuePair } from "../utils/types";
import { CommitMessagePromptData } from "../utils/types";
import { mockGetModelsResult } from "./mock-modelinfo";

// OnMemorizeAction is a function that defines an action to be taken when a blob with
// a given path name is memorized. The return value indicates whether the action should
// be kept (if true) or removed (if false) for future memorize calls with the path.
type OnMemorizeAction = (pathName: string, memoryName: string) => boolean;

type CompletionRequestArgs = {
    requestId: string;
    prefix: string;
    suffix: string;
    pathName: string;
    blobName: string | undefined;
    completionLocation: CompletionLocation | undefined;
    language: string;
    blobs: Blobs;
    recencyInfo?: RecencyInfo;
    completementTimeoutMs?: number;
};

type CodeEditRequestArgs = {
    requestId: string;
    instruction: string;
    selectedCode: string;
    prefix: string;
    suffix: string;
    pathName: string;
    blobName: string;
    prefixBegin: number;
    suffixEnd: number;
    language: string;
    blobs: Blobs;
};

// Note(rich): copying this status breaks a dependency chain where
// the test is unable to find the uuid library.
enum RevokeToolAccessStatus {
    /* eslint-disable @typescript-eslint/naming-convention */
    Unknown = 0,
    Unimplemented = 1,
    NotFound = 2,
    Success = 3,
    NotActive = 4,
    Failed = 5,
    /* eslint-enable @typescript-eslint/naming-convention */
}

/**
 * MockApiServer is a class that implements a mock of the ApiServer class. It supports
 * the ability to view its set of memory names, to clear its set of memory names, and
 * to delay the `memorize` api.
 */
export class MockAPIServer implements APIServer {
    private nextRequestId = 123;
    private textEncoder = new TextEncoder();
    private _blobNameCalculator: BlobNameCalculator;
    static readonly cannedCompletion = "canned completion";
    static readonly sentinelCompletion = "sentinel completion";
    static readonly sentinelKeyword = `sentinel keyword`;

    // store the last completion request parameters for tests
    private _lastCompletionRequestArgs: CompletionRequestArgs | undefined;
    public get lastCompletionRequestArgs(): CompletionRequestArgs | undefined {
        return this._lastCompletionRequestArgs;
    }

    // store the last completion request parameters for tests
    private _lastCodeEditRequestArgs: CodeEditRequestArgs | undefined;
    public get lastCodeEditRequestArgs(): CodeEditRequestArgs | undefined {
        return this._lastCodeEditRequestArgs;
    }

    static readonly defaultCompletionResponseParams = {
        suggestedPrefixCharCount: 644,
        suggestedSuffixCharCount: 147,
    };

    // Map of memory name -> path name
    private memories = new Map<string, string>();

    // Map of path name -> memory name. If a path name is present in this map then the
    // corresponding memory name overrides the true memory name in calls to memorize()
    // for that path.
    private overrideMemoryNames = new Map<string, string>();

    // A promise that memorize requests will wait on before completing.
    private memorizeDelay: Promise<void> | undefined;

    // Map of path name -> action. The corresponding action is performed when there
    // is a memorization request for a given path name.
    private onMemorizeActions = new Map<string, OnMemorizeAction>();

    // A promise that findMissing requests will wait on before completing.
    private _findMissingDelay: Promise<void> | undefined;

    // An optional generator for responding to checkpoint requests
    private _checkpointResponse: Generator<CheckpointBlobsResult, void, unknown> | undefined;
    private _checkpointDelay: Promise<void> | undefined;

    // A list of blob names for findMissing to report as not indexed
    public readonly nonindexedBlobNames = new Set<string>();

    // A history of the batch sizes of findMissing requests.
    public findMissingHistory = new Array<number>();

    public expectedCompletion: string | undefined = undefined;
    public mockModifiedCode: string = "console.log('hello world')";

    public featureFlags = defaultFeatureFlags;

    constructor(
        private readonly _maxBlobSize?: number,
        private readonly _maxFindMissingBatchSize?: number,
        readonly sessionId: string = "placeholder-session-id",
        readonly completionResponseParams: {
            suggestedPrefixCharCount: number | undefined;
            suggestedSuffixCharCount: number | undefined;
        } = MockAPIServer.defaultCompletionResponseParams
    ) {
        this._blobNameCalculator = new BlobNameCalculator(_maxBlobSize || 1000);
    }

    reportClientCompletionTimelines(_timelines: ClientCompletionTimline[]): Promise<void> {
        return Promise.resolve();
    }

    public getSessionId(): string {
        return `${this.sessionId}`;
    }

    public createRequestId(): string {
        const requestId = `request.${this.nextRequestId}`;
        this.nextRequestId += 1;
        return requestId;
    }

    public memorize = jest.fn(
        async (
            pathName: string,
            text: string,
            _blobName: string,
            _metadata: BlobMetadata,
            _timeoutMs?: number
        ): Promise<MemorizeResult> => {
            if (this.memorizeDelay) {
                const localDelay = this.memorizeDelay;
                this.memorizeDelay = undefined;
                await localDelay;
            }
            const memoryName = this.calculateMemoryName(pathName, text);

            const action = this.onMemorizeActions.get(pathName);
            if (action) {
                const keepAction = action(pathName, memoryName);
                if (!keepAction) {
                    this.onMemorizeActions.delete(pathName);
                }
            }
            this.memories.set(memoryName, pathName);
            return { blobName: memoryName };
        }
    );

    public batchUpload = jest.fn(async (blobs: Array<UploadBlob>): Promise<BatchUploadResult> => {
        if (this.memorizeDelay) {
            const localDelay = this.memorizeDelay;
            this.memorizeDelay = undefined;
            await localDelay;
        }

        const blobNames = [];
        for (const blob of blobs) {
            const memoryName = this.calculateMemoryName(blob.pathName, blob.text);

            const action = this.onMemorizeActions.get(blob.pathName);
            if (action) {
                const keepAction = action(blob.pathName, memoryName);
                if (!keepAction) {
                    this.onMemorizeActions.delete(blob.pathName);
                }
            }
            this.memories.set(memoryName, blob.pathName);
            blobNames.push(memoryName);
        }
        return {
            blobNames,
        };
    });

    public findMissing = jest.fn(async (memoryNames: string[]): Promise<FindMissingResult> => {
        if (this._findMissingDelay) {
            await this._findMissingDelay;
        }
        return this._findMissing(memoryNames);
    });

    private _findMissing(memoryNames: string[]): FindMissingResult {
        if (this._maxFindMissingBatchSize !== undefined) {
            expect(memoryNames.length).toBeLessThanOrEqual(this._maxFindMissingBatchSize);
        }
        this.findMissingHistory.push(memoryNames.length);

        const unknownBlobNames = new Set<string>();
        memoryNames.forEach((memoryName: string): void => {
            if (!this.memories.has(memoryName)) {
                unknownBlobNames.add(memoryName);
            }
        });

        // Don't report any blob names as both unknown and nonindexed
        const nonindexedBlobNames = new Set<string>();
        for (const nonindexedBlobName of this.nonindexedBlobNames) {
            if (!unknownBlobNames.has(nonindexedBlobName)) {
                nonindexedBlobNames.add(nonindexedBlobName);
            }
        }

        return {
            unknownBlobNames: Array.from(unknownBlobNames),
            nonindexedBlobNames: Array.from(nonindexedBlobNames),
        };
    }

    public complete = jest.fn(
        async (
            requestId: string,
            prefix: string,
            suffix: string,
            path: string,
            blobName: string | undefined,
            completionLocation: CompletionLocation | undefined,
            language: string,
            blobs: Blobs,
            _recenctChanges: ReplacementText[],
            _fileEditEvents?: FileEditEvent[],
            _completionTimeoutMs?: number,
            _probeOnly?: boolean,
            apiTiming?: APITiming
        ): Promise<CompletionResult> => {
            if (apiTiming) {
                apiTiming.rpcStart = Date.now();
            }
            this._lastCompletionRequestArgs = {
                requestId,
                prefix,
                suffix,
                pathName: path,
                blobName,
                completionLocation,
                language,
                blobs,
            };
            const findMissingResult = this._findMissing(blobs.addedBlobs);
            let completion = this.expectedCompletion;
            if (!completion) {
                completion = suffix.startsWith(MockAPIServer.sentinelKeyword)
                    ? MockAPIServer.sentinelCompletion
                    : MockAPIServer.cannedCompletion;
            }
            if (apiTiming) {
                apiTiming.rpcEnd = Date.now();
            }
            return {
                completionItems: [
                    {
                        text: completion,
                        suffixReplacementText: "",
                        skippedSuffix: "",
                    },
                ],
                unknownBlobNames: findMissingResult.unknownBlobNames,
                checkpointNotFound: false,
                ...this.completionResponseParams,
            };
        }
    );

    public reportError(
        _originalRequestId: string | null,
        _sanitizedMessage: string,
        _stackTrace: string,
        _diagnostics: KeyValuePair[]
    ): Promise<void> {
        return Promise.resolve();
    }

    public checkpointBlobs(_blobs: Blobs): Promise<CheckpointBlobsResult> {
        let response = this._checkpointResponse?.next().value;
        if (!response) {
            // If there is no generated response, use a default
            response = { newCheckpointId: "0x1234" };
        }
        if (!response.newCheckpointId) {
            // Special case: if the generator produced an empty version, treat
            // that as an error from the backend.
            throw new Error(`HTTP error`);
        }
        return Promise.resolve(response);
    }

    public editCode = jest.fn(
        async (
            requestId: string,
            instruction: string,
            selectedCode: string,
            prefix: string,
            suffix: string,
            pathName: string,
            blobName: string,
            prefixBegin: number,
            suffixEnd: number,
            language: string,
            blobs: Blobs
        ): Promise<CodeEditResult> => {
            this._lastCodeEditRequestArgs = {
                requestId,
                instruction,
                selectedCode,
                prefix,
                suffix,
                pathName,
                blobName,
                prefixBegin,
                suffixEnd,
                language,
                blobs,
            };
            return {
                modifiedCode: this.mockModifiedCode,
                unknownBlobNames: [],
                checkpointNotFound: false,
            };
        }
    );

    public chat(
        _requestId: string,
        _message: string,
        _chatHistory: Exchange[],
        _blobs: Blobs,
        _userGuidedBlobs: string[],
        _externalSourceIds: string[],
        _model: string | undefined,
        _vcsChange: VCSChange,
        _recentChanges: ReplacementText[],
        _selectedCode?: string,
        _prefix?: string,
        _suffix?: string,
        _pathName?: string,
        _language?: string
    ): Promise<ChatResult> {
        return Promise.resolve({
            text: "hello world",
        });
    }

    public chatStream(
        _requestId: string,
        _message: string,
        _chatHistory: Exchange[],
        _blobs: Blobs,
        _userGuidedBlobs: string[],
        _externalSourceIds: string[],
        _model: string | undefined,
        _vcsChange: VCSChange,
        _recentChanges: ReplacementText[],
        _contextCodeExchangeRequestId?: string,
        _selectedCode?: string,
        _prefix?: string,
        _suffix?: string,
        _pathName?: string,
        _language?: string,
        _sessionId?: string,
        _disableAutoExternalSources?: boolean,
        _userGuidelines?: string,
        _workspaceGuidelines?: string,
        _toolDefinitions?: ToolDefinition[],
        _nodes?: ChatRequestNode[],
        _mode?: ChatMode,
        _agentMemories?: string,
        _personaType?: PersonaType,
        _rules?: Rule[],
        _silent?: boolean,
        _enableSupportToolUseStart?: boolean
    ): Promise<AsyncIterable<ChatResult>> {
        async function* helloGenerator(): AsyncGenerator<ChatResult, void, undefined> {
            yield { text: "hello" };
            yield { text: " world" };
        }
        return Promise.resolve(helloGenerator());
    }

    public chatInstructionStream(
        _requestId: string,
        _instruction: string,
        _blobs: Blobs,
        _chatHistory: Exchange[],
        _selectedText?: string,
        _prefix?: string,
        _suffix?: string,
        _pathName?: string,
        _blobName?: string,
        _prefixBegin?: number,
        _suffixEnd?: number,
        _language?: string,
        _userGuidelines?: string,
        _workspaceGuidelines?: string
    ): Promise<AsyncIterable<ChatInstructionStreamResult>> {
        async function* helloGenerator(): AsyncGenerator<
            ChatInstructionStreamResult,
            void,
            undefined
        > {
            yield { text: "hello" };
            yield { text: " world" };
        }
        return Promise.resolve(helloGenerator());
    }

    public smartPasteStream(
        _requestId: string,
        _instruction: string,
        _blobs: Blobs,
        _chatHistory: Exchange[],
        _selectedText?: string,
        _prefix?: string,
        _suffix?: string,
        _pathName?: string,
        _blobName?: string,
        _prefixBegin?: number,
        _suffixEnd?: number,
        _language?: string,
        _codeBlock?: string,
        _targetFilePath?: string,
        _targetFileContent?: string
    ): Promise<AsyncIterable<ChatInstructionStreamResult>> {
        async function* helloGenerator(): AsyncGenerator<
            ChatInstructionStreamResult,
            void,
            undefined
        > {
            yield { text: "hello" };
            yield { text: " world" };
        }
        return Promise.resolve(helloGenerator());
    }

    public generateCommitMessageStream(
        _requestId: string,
        _commitMessagePromptData: CommitMessagePromptData
    ): Promise<AsyncIterable<GenerateCommitMessageResult>> {
        async function* helloGenerator(): AsyncGenerator<
            GenerateCommitMessageResult,
            void,
            undefined
        > {
            yield { text: "hello" };
            yield { text: " world" };
        }
        return Promise.resolve(helloGenerator());
    }

    public nextEditLocation(
        _requestId: string,
        _instruction: string,
        _pathName: string,
        _vcsChange: VCSChange,
        _fileEditEvents: FileEditEvent[],
        _blobs: Blobs,
        _recentChanges: ReplacementText[],
        _diagnostics: Diagnostic[],
        _numResults: number,
        _isSingleFile: boolean
    ): Promise<NextEditLocationResult> {
        return Promise.resolve({
            candidateLocations: [],
            unknownBlobNames: [],
            checkpointNotFound: false,
            criticalErrors: [],
        });
    }

    public nextEditStream(
        _request: NextEditStreamRequest
    ): Promise<AsyncIterable<NextEditGenerationResult>> {
        async function* helloGenerator(): AsyncGenerator<
            NextEditGenerationResult,
            void,
            undefined
        > {
            yield {
                result: {
                    suggestionId: "",
                    path: "",
                    blobName: "",
                    charStart: 0,
                    charEnd: 0,
                    existingCode: "",
                    suggestedCode: "hello",
                    truncationChar: undefined,
                    changeDescription: "",
                    diffSpans: [],
                    editingScore: 0,
                    localizationScore: 0,
                    editingScoreThreshold: 1.0,
                },
                unknownBlobNames: [],
                checkpointNotFound: false,
            };
        }
        return Promise.resolve(helloGenerator());
    }

    public resolveCompletions(_resolutions: CompletionResolution[]): Promise<void> {
        return Promise.resolve();
    }

    public logCodeEditResolution(_resolution: CodeEditResolution): Promise<void> {
        return Promise.resolve();
    }

    public logInstructionResolution(_resolution: InstructionResolution): Promise<void> {
        return Promise.resolve();
    }

    public logSmartPasteResolution(_resolution: SmartPasteResolution): Promise<void> {
        return Promise.resolve();
    }

    public resolveNextEdits(_resolutions: NextEditResolution[]): Promise<void> {
        return Promise.resolve();
    }

    public logNextEditSessionEvent(_events: NextEditSessionEvent[]): Promise<void> {
        return Promise.resolve();
    }

    public logFeatureVector(_events: FeatureVector): Promise<void> {
        return Promise.resolve();
    }

    public logOnboardingSessionEvent(_events: OnboardingSessionEvent[]): Promise<void> {
        return Promise.resolve();
    }

    public logAgentRequestEvent(_events: AgentRequestEvent[]): Promise<void> {
        return Promise.resolve();
    }

    public logAgentSessionEvent(_events: AgentSessionEvent[]): Promise<void> {
        return Promise.resolve();
    }

    public logRemoteAgentSessionEvent(_events: RemoteAgentSessionEvent[]): Promise<void> {
        return Promise.resolve();
    }

    public logExtensionSessionEvent(_event: ExtensionSessionEvent[]): Promise<void> {
        return Promise.resolve();
    }

    public logToolUseRequestEvent(_events: ToolUseRequestEvent[]): Promise<void> {
        return Promise.resolve();
    }

    public recordPreferenceSample(_sample: PreferenceSample): Promise<void> {
        return Promise.resolve();
    }

    public async checkCommand(): Promise<CheckCommandResult> {
        return { result: false, desc: "" };
    }

    public async containErrors(): Promise<ContainErrorsResult> {
        return { result: false, desc: "" };
    }

    public async createFixPlan(): Promise<FixPlanResult> {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        return { fix_desc: "", changes: [] };
    }

    public async applyFileFix(): Promise<FilePatch> {
        return { path: "", sourceContent: "", targetContent: "" };
    }

    public async autofixCheck(): Promise<AutofixCheckResponse> {
        return {
            containsFailure: false,
            isCodeRelated: false,
        };
    }

    public async autofixPlan(): Promise<AutofixPlanResponse> {
        return {
            unknownBlobNames: [],
            checkpointNotFound: false,
            summary: "",
            replacements: [],
        };
    }

    public static readonly modelName = "default-model";
    public static readonly suggestedPrefixCharCount = 100;
    public static readonly suggestedSuffixCharCount = 100;
    public static readonly maxMemorizeSizeBytes = 100;

    public async getModelConfig(): Promise<ModelConfig> {
        const modelInfo: Model[] = [];
        for (const backModelInfo of mockGetModelsResult.models) {
            modelInfo.push({
                name: backModelInfo.name,
                suggestedPrefixCharCount: backModelInfo.suggested_prefix_char_count,
                suggestedSuffixCharCount: backModelInfo.suggested_suffix_char_count,
            });
        }

        const languages: Language[] = [];
        for (const languageInfo of mockGetModelsResult.languages) {
            languages.push({
                name: languageInfo.name,
                vscodeName: languageInfo.vscode_name,
                extensions: languageInfo.extensions,
            });
        }

        return {
            defaultModel: mockGetModelsResult.default_model,
            models: modelInfo,
            languages,
            featureFlags: this.featureFlags,
            userTier: "unknown",
        };
    }

    public getModelInfo = jest.fn(async (modelName: string): Promise<Model> => {
        const models = (await this.getModelConfig()).models;
        const modelInfo = models.find((model) => model.name === modelName);
        if (modelInfo) {
            return modelInfo;
        }
        throw new UnknownModelError(modelName);
    });

    public async completionFeedback(_feedback: CompletionFeedback): Promise<void> {}

    public async chatFeedback(_feedback: ChatFeedback): Promise<void> {}

    public async nextEditFeedback(_feedback: NextEditFeedback): Promise<void> {}

    public uploadUserEvents(_events: any[]): Promise<void> {
        return Promise.resolve();
    }

    public saveChat(_conversationId: string, _conversation: Exchange[]): Promise<SaveChatResult> {
        return Promise.resolve({
            uuid: "test-uuid",
            url: "https://app.augmentcode.com/share/test-uuid",
        });
    }

    /**
     * The methods below are not part of APIServer.
     */

    // verifyContext verifies that the contents of the given context match our set of
    // memory names.
    public verifyContext(context: string[]) {
        expect(context.length).toBe(this.memories.size);
        for (const memoryName of context) {
            expect(this.memories.keys()).toContain(memoryName);
        }
    }

    // `forget` clears the set of uploaded memory names
    public forget(): void {
        this.memories.clear();
    }

    // calculateMemoryName calculates the memory name for the given pathName and
    // memory contents. If an override has been registered for the pathName, return
    // the override instead of the real name.
    private calculateMemoryName(pathName: string, text: string): string {
        if (this._maxBlobSize !== undefined && text.length > this._maxBlobSize) {
            throw new Error(
                `pathName ${pathName}, size ${text.length} ` +
                    `exceeds maxBlobSize of ${this._maxBlobSize}`
            );
        }
        const overrideName = this.overrideMemoryNames.get(pathName);
        if (overrideName) {
            return overrideName;
        }
        return this._blobNameCalculator.calculate(pathName, this.textEncoder.encode(text))!;
    }

    // overrideMemoryName registers an override for the memory name of the given
    // pathName. Or, if memoryName is undefined, it removes the override for the
    // path if there is one.
    public overrideMemoryName(pathName: string, memoryName?: string): void {
        if (memoryName) {
            this.overrideMemoryNames.set(pathName, memoryName);
        } else {
            this.overrideMemoryNames.delete(pathName);
        }
    }

    // delayMemorize causes memorize operations to wait until the returned disposable
    // is disposed. If `error` is defined, any delayed memorize operations will fail with
    // the given error.
    public delayMemorize(error?: Error): vscode.Disposable {
        const [promise, resolve, reject] = makePromise();
        const resume = new vscode.Disposable(() => {
            this.memorizeDelay = undefined;
            if (error) {
                reject(error);
            } else {
                resolve();
            }
        });
        this.memorizeDelay = promise;
        return resume;
    }

    // delayFindUnknown causes findMissing operations to wait until the returned disposable
    // is disposed.
    public delayFindMissing(): vscode.Disposable {
        const [promise, resolve, _] = makePromise();
        const resume = new vscode.Disposable(() => {
            this._findMissingDelay = undefined;
            resolve();
        });
        this._findMissingDelay = promise;
        return resume;
    }

    public delayCheckpoint(): vscode.Disposable {
        const [promise, resolve, _] = makePromise();
        const resume = new vscode.Disposable(() => {
            this._checkpointDelay = undefined;
            resolve();
        });
        this._checkpointDelay = promise;
        return resume;
    }

    // onMemorize registers an action to be taken when the given pathName is memorized.
    // Or, if `action` is undefined, removes the action for pathName if there is one.
    // If memorization is delayed via delayMemorize, the action run after the delay has
    // been lifted.
    public onMemorize(pathName: string, action?: OnMemorizeAction) {
        if (action) {
            this.onMemorizeActions.set(pathName, action);
        } else {
            this.onMemorizeActions.delete(pathName);
        }
    }

    // getMemoryNames returns all known memory names.
    public getMemoryNames(): string[] {
        return Array.from(this.memories.keys());
    }

    // getMemoryNamesForPath returns all known memory names for the given path name.
    public getMemoryNamesForPath(pathName: string): string[] {
        const memoryNames: string[] = [];
        for (const [memoryName, memoryPathName] of this.memories) {
            if (memoryPathName === pathName) {
                memoryNames.push(memoryName);
            }
        }
        return memoryNames;
    }

    public getBlobName(pathName: string): string | undefined {
        const blobNames = this.getMemoryNamesForPath(pathName);
        expect(blobNames.length).toBeLessThanOrEqual(1);
        return blobNames.at(0);
    }

    set checkpointResponse(
        response: Generator<CheckpointBlobsResult, void, undefined> | undefined
    ) {
        this._checkpointResponse = response;
    }
    get checkpointResponse(): Generator<CheckpointBlobsResult, void, undefined> | undefined {
        return this._checkpointResponse;
    }

    public async getAccessToken(
        _authURI: string,
        _tenantURL: string,
        _codeVerifier: string,
        _code: string
    ): Promise<string> {
        return "fake-access-token";
    }

    public async clientMetrics(_metrics: Array<ClientMetric>): Promise<void> {
        return Promise.resolve();
    }

    public async searchExternalSources(
        _query: string,
        _sourceTypes: string[]
    ): Promise<SearchExternalSourcesResponse> {
        return {
            sources: [
                {
                    id: "123",
                    name: "python",
                    title: "test-python",
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    source_type: 1,
                },
            ],
        };
    }

    public async getImplicitExternalSources(
        _message: string
    ): Promise<SearchExternalSourcesResponse> {
        return {
            sources: [
                {
                    id: "123",
                    name: "python",
                    title: "test-python",
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    source_type: 1,
                },
            ],
        };
    }

    public async agentCodebaseRetrieval(
        _requestId: string,
        _informationRequest: string,
        _blobs: Blobs,
        _chatHistory: Exchange[],
        _maxOutputLength: number,
        _signal: AbortSignal
    ): Promise<AgentCodebaseRetrievalResult> {
        return {
            formattedRetrieval: "hello world",
        };
    }

    public async agentEditFile(
        _requestId: string,
        _filePath: string,
        _editSummary: string,
        _detailedEditDescription: string,
        _fileContents: string,
        _signal: AbortSignal
    ): Promise<AgentEditFileResult> {
        return {
            modifiedFileContents: "hello world",
            isError: false,
        };
    }

    public async listRemoteTools(): Promise<ListRemoteToolsResult> {
        return {
            tools: [],
        };
    }

    public async checkToolSafety(_toolId: RemoteToolId, _toolInputJson: string): Promise<boolean> {
        return true;
    }

    public async runRemoteTool(
        _requestId: string,
        _toolName: string,
        _toolInputJson: string,
        _toolId: RemoteToolId,
        _extraToolInput: ExtraToolInput | undefined,
        _signal: AbortSignal
    ): Promise<RunRemoteToolResult> {
        return {
            toolOutput: "hello world",
            toolResultMessage: "",
            status: 1,
        };
    }

    public async revokeToolAccess(_toolId: RemoteToolId): Promise<RevokeToolAccessResult> {
        return {
            status: RevokeToolAccessStatus.Success,
        };
    }

    public async createRemoteAgent(
        _workspaceSetup: RemoteAgentWorkspaceSetup,
        _initialRequestDetails: RemoteAgentChatRequestDetails,
        _model?: string,
        _setupScript?: string,
        _isSetupScriptAgent?: boolean
    ): Promise<CreateRemoteAgentResponse> {
        return new Promise((resolve) => {
            resolve({
                // eslint-disable-next-line @typescript-eslint/naming-convention
                remote_agent_id: "123",
                status: RemoteAgentStatus.agentStarting,
            });
        });
    }

    public async remoteAgentChat(
        _remoteAgentId: string,
        _chatRequestDetails: RemoteAgentChatRequestDetails,
        _timeoutMs?: number
        // eslint-disable-next-line @typescript-eslint/naming-convention
    ): Promise<{ remoteAgentId: string; nodes: ChatResultNode[] }> {
        return new Promise((resolve) => {
            resolve({
                // eslint-disable-next-line @typescript-eslint/naming-convention
                remoteAgentId: "123",
                nodes: [],
            });
        });
    }

    public async deleteRemoteAgent(_remoteAgentId: string): Promise<void> {
        return new Promise((resolve) => {
            resolve();
        });
    }

    public async interruptRemoteAgent(_remoteAgentId: string): Promise<RemoteAgentStatus> {
        return new Promise((resolve) => {
            resolve(RemoteAgentStatus.agentIdle);
        });
    }

    public async listRemoteAgents(): Promise<ListRemoteAgentsResponse> {
        return new Promise((resolve) => {
            resolve({
                // eslint-disable-next-line @typescript-eslint/naming-convention
                remote_agents: [],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                max_remote_agents: 100,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                max_active_remote_agents: 10,
            });
        });
    }

    public async getRemoteAgentChatHistory(
        _remoteAgentId: string
    ): Promise<GetRemoteAgentChatHistoryResponse> {
        return new Promise((resolve) => {
            resolve({
                // eslint-disable-next-line @typescript-eslint/naming-convention
                chat_history: [],
            });
        });
    }

    public async remoteAgentAddSSHKey(
        _remoteAgentId: string,
        _publicKeys: string[]
    ): Promise<RemoteAgentAddSSHKeyResponse> {
        return new Promise((resolve) => {
            resolve({
                /* eslint-disable @typescript-eslint/naming-convention */
                ssh_config: {
                    public_keys: _publicKeys,
                    hostname: "augment-remote-agent-" + _remoteAgentId,
                    ssh_config_options: [],
                },
                /* eslint-enable @typescript-eslint/naming-convention */
            });
        });
    }

    public async getRemoteAgentWorkspaceLogs(
        _remoteAgentId: string
    ): Promise<RemoteAgentWorkspaceLogsResponse> {
        return new Promise((resolve) => {
            resolve({
                /* eslint-disable @typescript-eslint/naming-convention */
                workspace_setup_status: {
                    steps: [
                        {
                            step_description: "git clone",
                            logs: "Cloning into '/mnt/persist/workspace'...\nUpdating files:  79% (16019/20216)\rUpdating files:  80% (16173/20216)\rUpdating files:  81% (16375/20216)\rUpdating files:  82% (16578/20216)\rUpdating files:  83% (16780/20216)\rUpdating files:  84% (16982/20216)\rUpdating files:  85% (17184/20216)\rUpdating files:  86% (17386/20216)\rUpdating files:  87% (17588/20216)\rUpdating files:  88% (17791/20216)\rUpdating files:  89% (17993/20216)\rUpdating files:  90% (18195/20216)\rUpdating files:  91% (18397/20216)\rUpdating files:  92% (18599/20216)\rUpdating files:  93% (18801/20216)\rUpdating files:  94% (19004/20216)\rUpdating files:  95% (19206/20216)\rUpdating files:  96% (19408/20216)\rUpdating files:  97% (19610/20216)\rUpdating files:  98% (19812/20216)\rUpdating files:  99% (20014/20216)\rUpdating files: 100% (20216/20216)\rUpdating files: 100% (20216/20216), done.\n",
                            status: 2,
                            sequence_id: 0,
                            step_number: 0,
                        },
                        {
                            step_description: "git checkout",
                            logs: "Switched to a new branch 'kyekim/AU-8812'\nbranch 'kyekim/AU-8812' set up to track 'origin/kyekim/AU-8812'.\n",
                            status: 2,
                            sequence_id: 0,
                            step_number: 1,
                        },
                        {
                            step_description: "git apply",
                            logs: "",
                            status: 2,
                            sequence_id: 0,
                            step_number: 1,
                        },
                        {
                            step_description: "exec setup_script",
                            logs: "",
                            status: 0,
                            sequence_id: 1,
                            step_number: 0,
                        },
                    ],
                },
            });
        });
    }

    public async listGithubReposForAuthenticatedUser(): Promise<ListGithubReposForAuthenticatedUserResponse> {
        return new Promise((resolve) => {
            resolve({
                repos: [
                    {
                        owner: "augmentcode",
                        name: "augment",
                        html_url: "https://github.com/augmentcode/augment",
                        created_at: "2021-01-01",
                        updated_at: "2021-01-01",
                    },
                ],
            });
        });
    }

    public async listGithubRepoBranches(
        _repo: GithubRepo
    ): Promise<ListGithubRepoBranchesResponse> {
        return new Promise((resolve) => {
            resolve({
                branches: [
                    {
                        name: "main",
                        commit: {
                            sha: "123",
                            url: "https://github.com/augmentcode/augment/commit/123",
                        },
                        protected: false,
                    },
                ],
                has_next_page: false,
                next_page: 0,
            });
        });
    }

    public async isUserGithubConfigured(): Promise<IsUserGithubConfiguredResponse> {
        return new Promise((resolve) => {
            resolve({
                is_configured: true,
                oauth_url: "",
            });
        });
    }

    public async getGithubRepo(_repo: GithubRepo): Promise<{ repo: GithubRepo; error?: string }> {
        return new Promise((resolve) => {
            resolve({
                repo: {
                    owner: "augmentcode",
                    name: "augment",
                    html_url: "https://github.com/augmentcode/augment",
                    created_at: "2025-01-01",
                    updated_at: "2025-05-10",
                },
            });
        });
    }

    public async *getRemoteAgentChatHistoryStream(
        remoteAgentId: string,
        lastProcessedSequenceId: number,
        signal: AbortSignal
    ): AsyncGenerator<GetRemoteAgentHistoryStreamResponse> {
        if (signal.aborted) {
            throw new Error("Stream aborted");
        }
        yield* (async function* () {
            yield {
                updates: [
                    {
                        type: 1, // AgentHistoryUpdateType.AGENT_HISTORY_EXCHANGE
                        exchange: {
                            exchange: { id: `${remoteAgentId}-${lastProcessedSequenceId}` } as any,
                            changed_files: [],
                            sequence_id: 124,
                        },
                    },
                ],
            };
        })();
    }

    public async getSubscriptionInfo(): Promise<GetSubscriptionInfoResponse> {
        return new Promise((resolve) => {
            resolve({
                subscription: {
                    ActiveSubscription: {
                        usage_balance_depleted: false,
                    },
                },
            });
        });
    }

    public async pauseRemoteAgentWorkspace(_agentId: string): Promise<void> {
        return new Promise((resolve) => {
            resolve();
        });
    }

    public async resumeRemoteAgentWorkspace(_agentId: string): Promise<void> {
        return new Promise((resolve) => {
            resolve();
        });
    }
}
