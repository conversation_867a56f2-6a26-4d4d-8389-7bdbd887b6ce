import * as vscode from "vscode";

import { APIServer } from "./augment-api";
import { AugmentConfigListener } from "./augment-config-listener";
import { AuthSessionStore } from "./auth/auth-session-store";
import { OAuthFlow } from "./auth/oauth-flow";
import { AugmentInstruction } from "./code-edit-types";
import { CommandManager } from "./command-manager";
import { ResetAgentOnboarding, RunAgentInitialOrientationCommand } from "./commands/agent";
import { AuthCommand } from "./commands/auth";
import { AutofixCommand } from "./commands/autofix";
import { ClearFileEdits } from "./commands/clear-recent-editing-history";
import { CopySessionIdPanelCommand } from "./commands/copy-session-id";
import {
    StartExtensionCPUProfileCommand,
    StopExtensionCPUProfileCommand,
} from "./commands/cpu-profile";
import {
    AcceptAllChunksDiffViewCommand,
    AcceptFocusedChunkDiffViewCommand,
    CloseDiffViewCommand,
    FocusNextChunkDiffViewCommand,
    FocusPreviousChunkDiffViewCommand,
    OpenDiffViewCommand,
    RejectFocusedChunkDiffViewCommand,
    StartCodeInstructionCommand,
} from "./commands/diff-view";
import { DisableSyncing, EnableSyncing } from "./commands/enable-syncing";
import { ExtensionStatusCommand } from "./commands/extension-status";
import { FocusAugmentPanel } from "./commands/focus-augment-panel";
import { CommitMessageGenerator } from "./commands/generate-commit-message";
import { InsertCompletionCommand } from "./commands/insert-completion";
import { InternalWorkspaceContextCommand } from "./commands/internal-workspace-context";
import { ManageAccountCommand } from "./commands/manage-account";
import {
    NextEditBackgroundAcceptAllCommand,
    NextEditBackgroundAcceptCodeActionCommand,
    NextEditBackgroundAcceptCommand,
    NextEditBackgroundDismissCommand,
    NextEditBackgroundGotoNextSmartCommand,
    NextEditBackgroundNextCommand,
    NextEditBackgroundNextDisabledCommand,
    NextEditBackgroundOpenCommand,
    NextEditBackgroundPreviousCommand,
    NextEditBackgroundPreviousDisabledCommand,
    NextEditBackgroundRejectAllCommand,
    NextEditBackgroundRejectCommand,
    NextEditDisableBackgroundSuggestions,
    NextEditEnableBackgroundSuggestions,
    NextEditForceGenerationCommand,
    NextEditLearnMoreCommand,
    NextEditOpenPanelCommand,
    ResetNextEditOnboarding as NextEditResetOnboarding,
    NextEditSettingsCommand,
    NextEditToggleBackgroundSuggestions,
    NextEditToggleHoverDiffCommand,
    NextEditTogglePanelHorizontalSplitCommand,
    NextEditToggleShowAllHighlightsCommand,
    NextEditUndoAcceptSuggestionCommand,
    NextEditUpdateCommand,
    NextEditUpdateDisabledCachedCommand,
    NextEditUpdateDisabledNoChangesCommand,
    NextEditUpdateLoadingCommand,
} from "./commands/next-edit";
import { OpenSSHConfigCommand } from "./commands/open-ssh-config";
import { KeyboardShortcutsCommand, SettingsCommand } from "./commands/settings";
import { ShowAccountPage } from "./commands/show-account-page";
import { ShowDocs } from "./commands/show-docs";
import { ShowHistoryPanelCommand } from "./commands/show-history-panel";
import { ShowAugmentCommands } from "./commands/show-menu";
import { ShowRemoteAgentsPanelCommand } from "./commands/show-remote-agents-panel";
import { ShowSettingsPanelCommand } from "./commands/show-settings-panel";
import { ShowSidebarChatCommand } from "./commands/show-sidebar-chat";
import {
    SlashDocumentCommand,
    SlashExplainCommand,
    SlashFixCommand,
    SlashTestCommand,
} from "./commands/slash-command";
import { StartNewChat } from "./commands/start-new-chat";
import { StatusBarClick } from "./commands/status-bar-click";
import { ToggleCompletionsCommand } from "./commands/toggle-completions";
import { RecentCompletions } from "./completions/recent-completions";
import { AugmentExtension } from "./extension";
import { ChatExtensionMessage } from "./main-panel/apps/chat-webview-app";
import { NextEditResultInfo } from "./next-edit/next-edit-types";
import { AugmentGlobalState } from "./utils/context";
import { RecentItems } from "./utils/recent-items";
import { MainPanelApp } from "./webview-providers/webview-messages";
import { SyncingEnabledTracker } from "./workspace/syncing-enabled-tracker";

// This method makes it easy to write tests for the commands in our extension.
export function initCommandManager(
    context: vscode.ExtensionContext,
    extension: AugmentExtension,
    configListener: AugmentConfigListener,
    auth: AuthSessionStore,
    oauthFlow: OAuthFlow,
    apiServer: APIServer,
    recentCompletions: RecentCompletions,
    recentInstructions: RecentItems<AugmentInstruction>,
    recentNextEditResults: RecentItems<NextEditResultInfo>,
    changeWebviewAppEvent: vscode.EventEmitter<MainPanelApp>,
    chatExtensionEvent: vscode.EventEmitter<ChatExtensionMessage>,
    syncingEnabledTracker: SyncingEnabledTracker,
    globalState: AugmentGlobalState,
    workspaceStorage: vscode.Memento
): CommandManager {
    const commandManager = new CommandManager(configListener);
    commandManager.registerGroup("", [
        new AuthCommand(auth, oauthFlow, AuthCommand.signInCommandID, "$(sign-in) Sign In"),
        new FocusAugmentPanel(),
        new EnableSyncing(syncingEnabledTracker, configListener, extension),
    ]);

    commandManager.registerGroup("Completions", [
        new InsertCompletionCommand(extension, syncingEnabledTracker),
        new ToggleCompletionsCommand(configListener, syncingEnabledTracker),
    ]);

    // Code Instruction Commands
    commandManager.registerGroup("Code Instruction", [
        new StartCodeInstructionCommand(
            extension,
            context.extensionUri,
            apiServer,
            extension.guidelinesWatcher,
            syncingEnabledTracker
        ),
    ]);

    // Chat Commands
    commandManager.registerGroup("Chat", [
        new SlashFixCommand(extension, configListener, chatExtensionEvent, syncingEnabledTracker),
        new SlashExplainCommand(
            extension,
            configListener,
            chatExtensionEvent,
            syncingEnabledTracker
        ),
        new StartNewChat(chatExtensionEvent),
        new SlashTestCommand(extension, configListener, chatExtensionEvent, syncingEnabledTracker),
        new SlashDocumentCommand(
            extension,
            configListener,
            chatExtensionEvent,
            syncingEnabledTracker
        ),
        new ResetAgentOnboarding(chatExtensionEvent, globalState, workspaceStorage),
        new RunAgentInitialOrientationCommand(extension, apiServer, syncingEnabledTracker),
        new ShowSettingsPanelCommand(
            context.extensionUri,
            extension,
            apiServer,
            configListener,
            extension.guidelinesWatcher,
            auth
        ),
    ]);

    // Next Edit Commands
    commandManager.registerGroup("Next Edit Suggestions", [
        // The commands below are shown in action panel in order.
        new NextEditBackgroundAcceptCommand(extension, configListener, syncingEnabledTracker),
        new NextEditBackgroundRejectCommand(extension, configListener, syncingEnabledTracker),
        new NextEditBackgroundAcceptAllCommand(extension, configListener, syncingEnabledTracker),
        new NextEditBackgroundRejectAllCommand(extension, configListener, syncingEnabledTracker),
        new NextEditBackgroundDismissCommand(extension, configListener, syncingEnabledTracker),
        new NextEditBackgroundPreviousCommand(extension, configListener, syncingEnabledTracker),
        new NextEditBackgroundNextCommand(extension, configListener, syncingEnabledTracker),
        new NextEditBackgroundGotoNextSmartCommand(
            extension,
            configListener,
            syncingEnabledTracker
        ),
        new NextEditForceGenerationCommand(extension, configListener, syncingEnabledTracker),
        new NextEditTogglePanelHorizontalSplitCommand(
            extension,
            configListener,
            syncingEnabledTracker
        ),
        new NextEditUpdateCommand(extension, configListener, syncingEnabledTracker),
        new NextEditLearnMoreCommand(extension, configListener, syncingEnabledTracker),
        new NextEditEnableBackgroundSuggestions(extension, configListener, syncingEnabledTracker),
        new NextEditDisableBackgroundSuggestions(extension, configListener, syncingEnabledTracker),
        new NextEditToggleShowAllHighlightsCommand(
            extension,
            configListener,
            syncingEnabledTracker
        ),
        new NextEditResetOnboarding(extension, configListener, syncingEnabledTracker, globalState),
        new ClearFileEdits(extension, configListener),
        // The commands below aren't shown in action panel.
        new NextEditUpdateLoadingCommand(extension, configListener, syncingEnabledTracker),
        new NextEditUpdateDisabledNoChangesCommand(
            extension,
            configListener,
            syncingEnabledTracker
        ),
        new NextEditUpdateDisabledCachedCommand(extension, configListener, syncingEnabledTracker),
        new NextEditBackgroundAcceptCodeActionCommand(
            extension,
            configListener,
            syncingEnabledTracker
        ),
        new NextEditBackgroundOpenCommand(extension, configListener, syncingEnabledTracker),
        new NextEditOpenPanelCommand(extension, configListener, syncingEnabledTracker),
        new NextEditBackgroundPreviousDisabledCommand(
            extension,
            configListener,
            syncingEnabledTracker
        ),
        new NextEditBackgroundNextDisabledCommand(extension, configListener, syncingEnabledTracker),
        new NextEditUndoAcceptSuggestionCommand(extension, configListener, syncingEnabledTracker),
        new NextEditToggleHoverDiffCommand(extension, configListener, syncingEnabledTracker),
    ]);

    // For general commands used for debugging
    commandManager.registerGroup("Debug", [
        new ExtensionStatusCommand(extension, configListener),
        new InternalWorkspaceContextCommand(extension, configListener),
        new StartExtensionCPUProfileCommand(extension.featureFlagManager),
        new StopExtensionCPUProfileCommand(extension.featureFlagManager),
    ]);

    commandManager.registerGroup("Remote Agents", [
        new OpenSSHConfigCommand(extension.featureFlagManager, globalState),
        new ShowRemoteAgentsPanelCommand(
            context.extensionUri,
            apiServer,
            extension.workTimer,
            globalState,
            extension.featureFlagManager,
            extension,
            configListener,
            extension.guidelinesWatcher,
            extension.workspaceManager!,
            context
        ),
    ]);

    // This group is a list of higher level commands that aren't tied
    // to a specific feature.
    commandManager.registerGroup("", [
        new SettingsCommand(),
        new KeyboardShortcutsCommand(),
        new DisableSyncing(syncingEnabledTracker, configListener, extension),
        new ShowDocs(),
        new ShowAccountPage(),
        new ShowHistoryPanelCommand(
            context.extensionUri,
            configListener,
            apiServer,
            recentCompletions,
            recentInstructions,
            recentNextEditResults,
            extension.workTimer
        ),
        new CopySessionIdPanelCommand(apiServer),
        new ManageAccountCommand(extension, ManageAccountCommand.commandIDCommunity),
        new ManageAccountCommand(extension, ManageAccountCommand.commandIDProfessional),
        new ManageAccountCommand(extension, ManageAccountCommand.commandIDEnterprise),
        new AuthCommand(auth, oauthFlow, AuthCommand.signOutCommandID, "$(sign-out) Sign Out"),
        new NextEditSettingsCommand(extension, configListener, syncingEnabledTracker),
    ]);
    if (configListener.config.autofix.enabled && configListener.config.autofix.autofixUrl) {
        commandManager.register([new AutofixCommand(extension, apiServer)]);
    }

    // These commands are not grouped or shown to the user.
    // There are commands that the extension triggers itself.
    commandManager.register([
        new ShowAugmentCommands(extension, context, commandManager),
        new NextEditToggleBackgroundSuggestions(extension, configListener, syncingEnabledTracker),
        new ShowSidebarChatCommand(extension.featureFlagManager, changeWebviewAppEvent),
        new CommitMessageGenerator(extension, apiServer),
        new OpenDiffViewCommand(extension, context.extensionUri, apiServer),
        new AcceptAllChunksDiffViewCommand(),
        new AcceptFocusedChunkDiffViewCommand(),
        new RejectFocusedChunkDiffViewCommand(),
        new FocusPreviousChunkDiffViewCommand(),
        new FocusNextChunkDiffViewCommand(),
        new CloseDiffViewCommand(),
        new StatusBarClick(),
    ]);

    return commandManager;
}
