import { sendMessageToSidecar } from "@augment-internal/sidecar-libs/src/client-interfaces/webview-messaging";
import {
    AUGMENT_DIRECTORY_ROOT,
    AUGMENT_RULES_FOLDER,
} from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import * as fs from "fs";
import * as path from "path";
import * as vscode from "vscode";

import { AugmentConfigListener } from "./augment-config-listener";
import { GuidelinesWatcher } from "./chat/guidelines-watcher";
import { RulesLoader } from "./chat/rules-loader";
import { AugmentExtension } from "./extension";
import { getLogger } from "./logging";
import { PanelWebviewBase } from "./utils/panel-webview-base";
import { createAsyncMsgHandlerFromWebview } from "./utils/webviews/messaging-helper";
import { openFileFromMessage } from "./utils/webviews/open-file";
import {
    ChatInitialize,
    WebViewMessage,
    WebViewMessageType,
} from "./webview-providers/webview-messages";

/**
 * Provider for Augment Memories editor.
 *
 * This custom editor is used to display and edit Augment Memories files.
 *
 * This provider implements:
 *
 * - Setting up the initial webview for a custom editor.
 * - Loading scripts and styles in a custom editor.
 * - Synchronizing changes between a text document and a custom editor.
 */
export class AugmentMemoriesEditorProvider implements vscode.CustomTextEditorProvider {
    public static register(
        context: vscode.ExtensionContext,
        configListener: AugmentConfigListener,
        extension: AugmentExtension
    ): vscode.Disposable {
        const provider = new AugmentMemoriesEditorProvider(context, configListener, extension);
        const providerRegistration = vscode.window.registerCustomEditorProvider(
            AugmentMemoriesEditorProvider.viewType,
            provider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true,
                },
                supportsMultipleEditorsPerDocument: false,
            }
        );
        return providerRegistration;
    }

    private static readonly viewType = "memories.augment";
    private readonly logger = getLogger("AugmentMemoriesEditorProvider");
    private webviewLoader: MemoriesWebviewLoader | undefined;

    constructor(
        public readonly context: vscode.ExtensionContext,
        private readonly configListener: AugmentConfigListener,
        private readonly extension: AugmentExtension
    ) {}

    /**
     * Called when our custom editor is opened.
     */
    public async resolveCustomTextEditor(
        document: vscode.TextDocument,
        webviewPanel: vscode.WebviewPanel,
        _token: vscode.CancellationToken
    ): Promise<void> {
        // If loading spinner , provide a fallback HTML
        webviewPanel.webview.options = {
            enableScripts: true,
        };
        webviewPanel.webview.html = `
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Augment Memories</title>
                </head>
                <body>
                    <div id="app">
                        <p>Loading Augment Memories...</p>
                    </div>
                </body>
                </html>
        `;

        // Create a webview loader to handle loading the HTML content
        this.webviewLoader = new MemoriesWebviewLoader("memories.html", webviewPanel.webview);

        const asyncMsgHandler = createAsyncMsgHandlerFromWebview(webviewPanel.webview);
        // Register a sidecar handler for messages handled by sidecar/libs
        asyncMsgHandler.registerSidecarHandler((msg, postMessage) => {
            sendMessageToSidecar(msg, postMessage);
        });

        // Register handler for chat initialization (to provide flags)
        asyncMsgHandler.registerHandler(
            WebViewMessageType.chatLoaded,
            (_): Promise<ChatInitialize> => {
                return Promise.resolve({
                    type: WebViewMessageType.chatInitialize,
                    data: {
                        enableRules: this.extension.featureFlagManager.currentFlags.enableRules,
                        enableDebugFeatures: this.configListener.config.enableDebugFeatures,
                        fullFeatured: true,
                        // Add other flags as needed for memories
                    },
                });
            }
        );

        try {
            await this.webviewLoader.loadHTML(this.context.extensionUri);
        } catch (error) {
            this.logger.error(`Failed to load memories.html webview: ${String(error)}`);

            void vscode.window.showErrorMessage(`Failed to load memories editor: ${String(error)}`);
            return;
        }

        // Function to send content to the webview
        const updateWebview = () => {
            this.logger.debug("Sending content to memories webview");
            void webviewPanel.webview.postMessage({
                type: WebViewMessageType.loadFile,
                data: {
                    repoRoot: undefined,
                    content: document.getText(),
                    pathName: document.uri.fsPath,
                },
            });
        };

        // Listen for webview ready message
        webviewPanel.webview.onDidReceiveMessage(async (message: WebViewMessage) => {
            // Handle standard WebViewMessage types
            switch (message.type) {
                case WebViewMessageType.saveFile: {
                    // Handle file writing from the webview
                    const writeFileMsg = message;
                    try {
                        const edit = new vscode.WorkspaceEdit();
                        edit.replace(
                            document.uri,
                            new vscode.Range(0, 0, document.lineCount, 0),
                            writeFileMsg.data.content
                        );
                        void vscode.workspace.applyEdit(edit).then(() => {
                            void document.save();
                            this.logger.debug(
                                `Successfully saved content to ${document.uri.fsPath}`
                            );
                        });
                    } catch (error) {
                        this.logger.error(`Error saving content to ${document.uri.fsPath}:`, error);
                    }
                    break;
                }
                case WebViewMessageType.memoriesLoaded: {
                    this.logger.debug("Memories webview is ready, sending initial content");
                    updateWebview();
                    break;
                }
                case WebViewMessageType.openGuidelines: {
                    const workspacePath =
                        vscode.workspace.workspaceFolders?.at(0)?.uri.fsPath ?? "";
                    void openFileFromMessage({
                        repoRoot: workspacePath,
                        pathName: ".augment-guidelines",
                    });
                    break;
                }
                case WebViewMessageType.updateWorkspaceGuidelines: {
                    const workspacePath =
                        vscode.workspace.workspaceFolders?.at(0)?.uri.fsPath ?? "";

                    if (workspacePath) {
                        const guidelinesPath = path.join(workspacePath, ".augment-guidelines");

                        try {
                            // Read existing content if the file exists
                            let existingContent = "";
                            try {
                                existingContent = fs.readFileSync(guidelinesPath, "utf8");
                            } catch (error) {
                                // File doesn't exist yet, which is fine
                                this.logger.debug(
                                    `Guidelines file doesn't exist yet, will create it: ${guidelinesPath}`
                                );
                            }

                            // Append the new content to the existing content with a newline separator if needed
                            const newContent = existingContent
                                ? (existingContent.endsWith("\n")
                                      ? existingContent + "\n"
                                      : existingContent + "\n\n") + message.data
                                : message.data;

                            // Write the updated content back to the file
                            fs.writeFileSync(guidelinesPath, newContent);
                            this.logger.debug(
                                `Successfully updated workspace guidelines at ${guidelinesPath}`
                            );

                            // Open the file in the editor
                            void openFileFromMessage({
                                repoRoot: workspacePath,
                                pathName: ".augment-guidelines",
                                differentTab: true,
                            });
                        } catch (error) {
                            this.logger.error(
                                `Failed to update workspace guidelines: ${String(error)}`
                            );
                            void vscode.window.showErrorMessage(
                                `Failed to update workspace guidelines: ${String(error)}`
                            );
                        }
                    } else {
                        this.logger.error("No workspace folder found");
                        void vscode.window.showErrorMessage("No workspace folder found");
                    }
                    break;
                }
                case WebViewMessageType.openFile: {
                    void openFileFromMessage({
                        repoRoot: message.data.repoRoot,
                        pathName: message.data.pathName,
                        openTextDocument: true,
                    });
                    break;
                }
                case WebViewMessageType.updateUserGuidelines: {
                    // Get current user guidelines content
                    const config = vscode.workspace.getConfiguration("augment.chat");
                    const currentContent = config.get<string>("userGuidelines") || "";

                    // Append the new content to the existing content with a newline separator if needed
                    const newContent = currentContent
                        ? (currentContent.endsWith("\n") ? currentContent : currentContent + "\n") +
                          message.data
                        : message.data;

                    try {
                        GuidelinesWatcher.updateUserGuidelines(newContent);
                        this.logger.debug("Successfully updated user guidelines");
                        void vscode.window
                            .showInformationMessage("User guidelines successfully updated", {
                                title: "View in Settings",
                            })
                            .then((selection) => {
                                if (selection) {
                                    void vscode.commands.executeCommand(
                                        "vscode-augment.showSettingsPanel",
                                        "guidelines"
                                    );
                                }
                            });
                    } catch (error) {
                        this.logger.error(`Failed to update user guidelines: ${String(error)}`);
                        void vscode.window.showErrorMessage(
                            `Failed to update user guidelines: ${String(error)}`
                        );
                    }
                    break;
                }
                case WebViewMessageType.openSettingsPage: {
                    // If a section is specified, pass it as a parameter to the command
                    const section = message.data;

                    // Open settings in a different tab
                    // Use the standard command if differentTab is not requested
                    if (section) {
                        void vscode.commands.executeCommand(
                            "vscode-augment.showSettingsPanel",
                            section
                        );
                    } else {
                        void vscode.commands.executeCommand("vscode-augment.showSettingsPanel");
                    }
                    break;
                }
                case WebViewMessageType.getRulesListRequest: {
                    try {
                        // Check if workspaceManager is available
                        if (!this.extension.workspaceManager) {
                            void webviewPanel.webview.postMessage({
                                type: WebViewMessageType.getRulesListResponse,
                                data: [],
                            });
                            break;
                        }

                        const rules = await RulesLoader.loadRules(this.extension.workspaceManager);
                        this.logger.debug(
                            `Loaded ${rules.length} rules:`,
                            rules.map((r) => r.path)
                        );
                        void webviewPanel.webview.postMessage({
                            type: WebViewMessageType.getRulesListResponse,
                            data: rules,
                        });
                    } catch (error) {
                        this.logger.error(`Failed to get rules list: ${String(error)}`);
                        void webviewPanel.webview.postMessage({
                            type: WebViewMessageType.getRulesListResponse,
                            data: [],
                        });
                    }
                    break;
                }
                case WebViewMessageType.updateRuleFile: {
                    const workspacePath =
                        vscode.workspace.workspaceFolders?.at(0)?.uri.fsPath ?? "";

                    if (!workspacePath) {
                        this.logger.error("No workspace folder found");
                        void vscode.window.showErrorMessage("No workspace folder found");
                        break;
                    }
                    const rulePath = path.join(
                        workspacePath,
                        AUGMENT_DIRECTORY_ROOT,
                        AUGMENT_RULES_FOLDER,
                        message.data.rulePath
                    );

                    try {
                        // Ensure the directory exists
                        const ruleDir = path.dirname(rulePath);
                        if (!fs.existsSync(ruleDir)) {
                            fs.mkdirSync(ruleDir, { recursive: true });
                        }

                        // Read existing content if the file exists
                        let existingContent = "";
                        try {
                            existingContent = fs.readFileSync(rulePath, "utf8");
                        } catch (error) {
                            // File doesn't exist yet, which is fine
                            this.logger.debug(
                                `Rule file doesn't exist yet, will create it: ${rulePath}`
                            );
                        }

                        // Append the new content to the existing content with a newline separator if needed
                        const newContent = existingContent
                            ? (existingContent.endsWith("\n")
                                  ? existingContent + "\n"
                                  : existingContent + "\n\n") + message.data.content
                            : message.data.content;

                        // Write the updated content back to the file
                        fs.writeFileSync(rulePath, newContent);
                        this.logger.debug(`Successfully updated rule file at ${rulePath}`);

                        // Open the file in the editor when the user clicks the alert
                        void vscode.window
                            .showInformationMessage(
                                `Moved select to rule file ${message.data.rulePath}`,
                                { title: "View Rule File" }
                            )
                            .then(() => {
                                void openFileFromMessage({
                                    repoRoot: workspacePath,
                                    pathName: path.relative(workspacePath, rulePath),
                                });
                            });
                    } catch (error) {
                        this.logger.error(`Failed to update rule file: ${String(error)}`);
                        void vscode.window.showErrorMessage(
                            `Failed to update rule file: ${String(error)}`
                        );
                    }
                }
            }
        });

        // Hook up event handlers for document changes
        const changeDocumentSubscription = vscode.workspace.onDidChangeTextDocument((e) => {
            if (e.document.uri.toString() === document.uri.toString()) {
                updateWebview();
            }
        });

        // Make sure we get rid of the listener when our editor is closed.
        webviewPanel.onDidDispose(() => {
            changeDocumentSubscription.dispose();
            if (this.webviewLoader) {
                this.webviewLoader.dispose();
                this.webviewLoader = undefined;
            }
        });

        // Send the initial document content to the webview
        // We'll also try to send it immediately, but the webview might not be ready yet
        updateWebview();
    }
}

/**
 * Helper class to load the memories.html webview
 */
class MemoriesWebviewLoader extends PanelWebviewBase {
    constructor(filename: string, webview: vscode.Webview) {
        super(filename, webview);
    }
}
