import {
    Chat<PERSON><PERSON>,
    ChatRequestNode,
    Exchange,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { IAPIClient } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client";
import {
    AgentCodebaseRetrie<PERSON><PERSON><PERSON><PERSON>,
    AgentRequestEvent,
    AgentSessionEvent,
    ChatResult,
    RemoteAgentSessionEvent,
    ToolUseRequestEvent,
} from "@augment-internal/sidecar-libs/src/client-interfaces/api-client-types";
import { RemoteToolId, ToolDefinition } from "@augment-internal/sidecar-libs/src/tools/tool-types";

import { APIServer } from "../augment-api";
import { WorkspaceManager } from "../workspace/workspace-manager";

/**
 * This class is a light wrapper around our APIServer to meet the IAPIClient
 * interface.
 */
export class SidecarAPIClient implements IAPIClient {
    constructor(
        private _apiServer: APIServer,
        private _workspaceManager: WorkspaceManager
    ) {}

    async chatStream(
        message: string,
        requestId: string,
        chatHistory: Exchange[],
        toolDefinitions: ToolDefinition[],
        requestNodes: ChatRequestNode[],
        mode: ChatMode,
        modelId: string | undefined,
        silent: boolean
    ): Promise<AsyncIterable<ChatResult>> {
        const chatResultIter = await this._apiServer.chatStream(
            requestId, // requestId
            message, // message
            chatHistory, // chatHistory
            // NOTE: If we update this to use actual blobs, you probably want to update
            //       IntelliJ sidecar as well.
            { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] }, // blobs
            [], // userGuidedBlobs
            [], // externalSourceIds
            modelId ?? undefined, // model (if `undefined`, backend picks model based on `mode`)
            { workingDirectory: [], commits: [] }, // vcsChange
            [], // recentChanges
            undefined, // contextCodeExchangeRequestId
            undefined, // selectedCode
            undefined, // prefix
            undefined, // suffix
            undefined, // pathName
            undefined, // language
            undefined, // sessionId
            undefined, // disableAutoExternalSources
            undefined, // userGuidelines
            undefined, // workspaceGuidelines
            toolDefinitions, // toolDefinitions
            requestNodes, // nodes
            mode, // mode
            undefined, // agentMemories
            undefined, // personaType
            undefined, // rules
            silent // silent
        );

        return chatResultIter;
    }

    public agentCodebaseRetrieval(
        toolRequestId: string,
        informationRequest: string,
        chatHistory: Exchange[],
        maxOutputLength: number,
        signal?: AbortSignal
    ): Promise<AgentCodebaseRetrievalResult> {
        return this._apiServer.agentCodebaseRetrieval(
            toolRequestId,
            informationRequest,
            this._workspaceManager.getContext().blobs,
            chatHistory,
            maxOutputLength,
            signal
        );
    }

    // delegate tool use event reporter to apiserver
    logToolUseRequestEvent(events: ToolUseRequestEvent[]): Promise<void> {
        return this._apiServer.logToolUseRequestEvent(events);
    }

    logAgentRequestEvent(events: AgentRequestEvent[]): Promise<void> {
        return this._apiServer.logAgentRequestEvent(events);
    }

    logAgentSessionEvent(events: AgentSessionEvent[]): Promise<void> {
        return this._apiServer.logAgentSessionEvent(events);
    }

    logRemoteAgentSessionEvent(events: RemoteAgentSessionEvent[]): Promise<void> {
        return this._apiServer.logRemoteAgentSessionEvent(events);
    }

    checkToolSafety(toolId: RemoteToolId, toolInputJson: string): Promise<boolean> {
        return this._apiServer.checkToolSafety(toolId, toolInputJson);
    }
}
