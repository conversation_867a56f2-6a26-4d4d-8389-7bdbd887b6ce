import { IPluginFileStore } from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import * as vscode from "vscode";

import {
    deleteFile,
    directoryExistsAsync,
    makeDirs,
    readFileRaw,
    writeFileRaw,
} from "../utils/fs-utils";
import { dirName } from "../utils/path-utils";

/**
 * Asset Manager
 *
 * Stores any asset in the VS Code extension context
 * Supports nested directory structures for assets
 *
 * Unique per workspace
 */
export class AssetManager implements IPluginFileStore {
    private static assetSubdirectory = "augment-user-assets";

    constructor(private readonly _extensionContext: vscode.ExtensionContext) {}

    /**
     * Save an asset
     *
     * @param name the name of the asset (can include nested paths like "images/logo.png")
     * @param content the content of the asset
     * @returns the uri of the asset
     */
    async saveAsset(name: string, content: Uint8Array) {
        const uri = await this._getUri(name);
        await writeFileRaw(uri.fsPath, content);
    }

    /**
     * Load an asset
     *
     * @param name the name of the asset (can include nested paths like "images/logo.png")
     * @returns the content of the asset or undefined if it does not exist
     */
    async loadAsset(name: string): Promise<Uint8Array | undefined> {
        const uri = await this._getUri(name);
        try {
            // We have 2 reasons for manually casting this to a Uint8Array:
            // 1. readFileRaw returns a Buffer which is serialized to a JSON when logged
            //    when the downstream consumer from _asyncMessageSender reads the Buffer from chatLoadImageResponse,
            //    it converts it to a JSON object and makes the typing incorrect / confusing
            // 2. We want this to mirror the saveAsset typing, so we convert it back to a Uint8Array to maintain the contract
            const buffer = await readFileRaw(uri.fsPath);
            return new Uint8Array(buffer);
        } catch {
            return undefined;
        }
    }

    /**
     * Delete an asset
     *
     * @param name the name of the asset (can include nested paths like "images/logo.png")
     * @returns void
     */
    async deleteAsset(name: string): Promise<void> {
        const uri = await this._getUri(name);
        return await deleteFile(uri.fsPath);
    }

    /**
     * Get the URI of an asset
     *
     * Creates the asset directory and any necessary subdirectories if they do not exist
     *
     * @param name the name of the asset (can include nested paths like "images/logo.png")
     * @returns the uri of the asset
     */
    private async _getUri(name: string): Promise<vscode.Uri> {
        const storageUri =
            this._extensionContext.storageUri ?? this._extensionContext.globalStorageUri;
        const assetBasePath = joinPath(storageUri.fsPath, AssetManager.assetSubdirectory);

        // Create the base asset directory if it doesn't exist
        if (!(await directoryExistsAsync(assetBasePath))) {
            await makeDirs(assetBasePath);
        }

        // Handle nested paths
        // Get the directory part and filename from the asset name
        const assetDirPart = dirName(name);

        // If we have a directory part, create the nested directory structure
        if (assetDirPart) {
            const assetDirPath = joinPath(assetBasePath, assetDirPart);

            // Create the directory if it doesn't exist
            if (!(await directoryExistsAsync(assetDirPath))) {
                await makeDirs(assetDirPath);
            }
        }

        return vscode.Uri.file(joinPath(assetBasePath, name));
    }
}
