import { RuleType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { sendMessageToSidecar } from "@augment-internal/sidecar-libs/src/client-interfaces/webview-messaging";
import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import {
    ToolDefinitionWithSettings,
    ToolHostName,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { type TerminalSettings } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { RulesParser } from "@augment-internal/sidecar-libs/src/utils/rules-parser";
import { partition } from "lodash";
import path from "path";
import * as vscode from "vscode";

import { APIServer, type RevokeToolAccessResult, RevokeToolAccessStatus } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { AuthSessionStore } from "../auth/auth-session-store";
import {
    onOrientationStatusChanged,
    sendOrientationStatusToWebviews,
} from "../chat/agent-onboarding-orientation";
import { GuidelinesWatcher } from "../chat/guidelines-watcher";
import { MARKDOWN_FILE_ENDINGS, RulesLoader } from "../chat/rules-loader";
import { RulesWatcher } from "../chat/rules-watcher";
import { RunAgentInitialOrientationCommand } from "../commands/agent";
import { AugmentExtension } from "../extension";
import { FeatureFlagManager } from "../feature-flags";
import { getLogger } from "../logging";
import { isExtensionVersionGte } from "../utils/environment";
import { directoryExists, fileExists, readFileUtf8, writeFileUtf8 } from "../utils/fs-utils";
import { PanelWebviewBase } from "../utils/panel-webview-base";
import { type AsyncMsgHandler } from "../utils/webviews/messaging";
import { createAsyncMsgHandlerFromWebview } from "../utils/webviews/messaging-helper";
import {
    type ConfirmationModalResponse,
    type ImportDirectoryRequestData,
    type ImportFileRequestData,
    type MCPServer,
    type OpenConfirmationModal,
    ToolConfigGetDefinitions,
    type ToolConfigRevokeAccessRequest,
    type ToolConfigStartOAuthRequest,
    WebViewMessage,
    WebViewMessageType,
} from "../webview-providers/webview-messages";
import { WorkspaceUIModel } from "../workspace/workspace-ui-model";
import {
    ToolConfigError,
    ToolConfigParseError,
    ToolConfigSaveRequest,
    ToolSettings,
} from "./settings-panel-types";
import { ToolConfigStore } from "./stores/tool-config-store";

// TODO: support importing memories

type AutoDetectRulesConfig = {
    directory?: string;
    file?: string;
    name: string;
};
const AUTO_DETECT_RULES_CONFIG: AutoDetectRulesConfig[] = [
    { directory: ".cursor/rules", file: ".cursorrules", name: "Cursor" },
    { directory: ".windsurf/rules", file: ".windsurfrules", name: "Windsurf" },
    { file: ".github/copilot-instructions.md", name: "GitHub Copilot" },
    { directory: ".clinerules", file: ".clinerules", name: "Cline" },
    { directory: ".roo/rules", file: ".roorules", name: "Roo Code" },
    { directory: ".trae/rules", name: "Trae" },
] as const;

export class SettingsWebviewPanel extends PanelWebviewBase {
    private static readonly viewType: string = "augmentSettingsPanel";
    private _settingsLogger = getLogger("SettingsWebviewPanel");
    private _asyncMsgHandler: AsyncMsgHandler | undefined;
    public static currentPanel: SettingsWebviewPanel | undefined;

    private _workspaceUiModel: WorkspaceUIModel | null = null;
    private _store: ToolConfigStore;
    private _featureFlagManager: FeatureFlagManager;
    private _rulesWatcher: RulesWatcher;

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private readonly _extension: AugmentExtension,
        private readonly _apiServer: APIServer,
        private readonly _config: AugmentConfigListener,
        private readonly _guidelinesWatcher: GuidelinesWatcher,
        private readonly _authSessionStore: AuthSessionStore,
        private readonly _navigateTo?: string,
        private readonly _panel = vscode.window.createWebviewPanel(
            SettingsWebviewPanel.viewType,
            "Augment Settings",
            vscode.ViewColumn.Active,
            {
                retainContextWhenHidden: true,
                enableScripts: true,
            }
        )
    ) {
        super("settings.html", _panel.webview);
        this._store = this._extension.toolConfigStore!;
        this._featureFlagManager = this._extension.featureFlagManager;
        this._rulesWatcher = new RulesWatcher(this._extension.workspaceManager!);

        this._panel.iconPath = {
            light: vscode.Uri.joinPath(this._extensionUri, "media", "panel-icon-light.svg"),
            dark: vscode.Uri.joinPath(this._extensionUri, "media", "panel-icon-dark.svg"),
        };
        this._panel.onDidDispose(() => {
            this.dispose();
        });
        this._asyncMsgHandler = createAsyncMsgHandlerFromWebview(this._panel.webview);

        // Register a sidecar handler for messages handled by sidecar/libs
        this._asyncMsgHandler.registerSidecarHandler((msg, postMessage) => {
            sendMessageToSidecar(msg, postMessage);
        });

        // Register async message handlers
        this._asyncMsgHandler.registerHandler<OpenConfirmationModal, ConfirmationModalResponse>(
            WebViewMessageType.openConfirmationModal,
            this._handleOpenConfirmationModal.bind(this)
        );

        this.addDisposable(this._asyncMsgHandler);
        this.addDisposables(
            this._panel,
            new vscode.Disposable(() => {
                SettingsWebviewPanel.currentPanel = undefined;
            }),
            onOrientationStatusChanged((status) => {
                // When orientation status changes, send the status to the webview
                void this._postMessage({
                    type: WebViewMessageType.orientationStatusUpdate,
                    data: status,
                });
            }),
            this._rulesWatcher.onDidChange(() => {
                // When rules change, send the updated list to the webview
                void this._handleGetRulesList();
            })
        );

        this.addDisposable(
            this._panel.webview.onDidReceiveMessage(async (message: WebViewMessage) => {
                try {
                    await this._extension.workTimer.runTimed(message.type, () =>
                        this._handleMessage(message)
                    );
                } catch (error) {
                    this._handleError(error);
                }
            })
        );

        // Initialize WorkspaceUIModel for handling workspace context requests
        if (this._extension.workspaceManager) {
            this._workspaceUiModel = new WorkspaceUIModel(
                this._extension.workspaceManager,
                this._panel.webview,
                this._extension.featureFlagManager,
                this._extension.workTimer
            );
            this.addDisposable(this._workspaceUiModel);
        }

        void this.loadHTML(_extensionUri);
    }

    public dispose = () => {
        this._panel.dispose();
        this._rulesWatcher.dispose();
        SettingsWebviewPanel.currentPanel = undefined;
        super.dispose();
    };

    /**
     * Navigates to a specific section in the settings panel
     * @param section The section to navigate to (e.g., 'userGuidelines')
     */
    public navigateToSection(section: string): void {
        void this._postMessage({
            type: WebViewMessageType.navigateToSettingsSection,
            data: section,
        });
    }

    protected _handleError(error: unknown) {
        if (error instanceof ToolConfigError) {
            void vscode.window.showErrorMessage(error.message);
        } else {
            void vscode.window.showErrorMessage(`Unexpected error: ${getErrmsg(error)}`);
        }
    }

    private async _handleMessage(message: WebViewMessage): Promise<void> {
        const messageType = message.type;
        switch (messageType) {
            case WebViewMessageType.settingsPanelLoaded:
                this._handleSettingsPanelLoaded();
                break;
            case WebViewMessageType.toolConfigLoaded:
                await this._handleConfigLoaded();
                break;
            case WebViewMessageType.toolConfigSave:
                await this._handleConfigSave(message.data);
                break;
            case WebViewMessageType.toolConfigGetDefinitions:
                await this._handleGetDefinitions(message);
                break;
            case WebViewMessageType.toolConfigStartOAuth:
                await this._handleStartOAuth(message.data);
                break;
            case WebViewMessageType.toolConfigRevokeAccess:
                await this._handleRevokeAccess(message.data);
                break;
            case WebViewMessageType.getStoredMCPServers:
                await this._handleGetStorageValue();
                break;
            case WebViewMessageType.setStoredMCPServers:
                await this._handleSetStorageValue(message.data);
                break;
            case WebViewMessageType.executeInitialOrientation:
                await this._handleExecuteInitialOrientation();
                break;
            case WebViewMessageType.getOrientationStatus:
                this._handleGetOrientationStatus();
                break;
            case WebViewMessageType.updateUserGuidelines:
                GuidelinesWatcher.updateUserGuidelines(message.data);
                break;
            case WebViewMessageType.getTerminalSettings:
                await this._handleGetTerminalSettings();
                break;
            case WebViewMessageType.updateTerminalSettings:
                await this._handleUpdateTerminalSettings(message.data);
                break;

            case WebViewMessageType.signOut:
                await this._authSessionStore.removeSession();
                this.dispose();
                break;
            case WebViewMessageType.saveFile: {
                const workspaceRoot = this._extension.workspaceManager?.getBestFolderRoot();
                if (!workspaceRoot) {
                    this._settingsLogger.warn("No workspace root found");
                    break;
                }
                // Handle file writing from the webview
                const writeFileMsg = message;
                const writeFilePath = path.join(workspaceRoot, writeFileMsg.data.pathName);
                await writeFileUtf8(writeFilePath, writeFileMsg.data.content);
                break;
            }
            case WebViewMessageType.getRulesListRequest:
                await this._handleGetRulesList();
                break;
            case WebViewMessageType.createRule:
                await this._handleCreateRule();
                break;
            case WebViewMessageType.openRule:
                await this._handleOpenRule(message.data);
                break;
            case WebViewMessageType.deleteRule:
                await this._handleDeleteRule(message.data);
                break;
            case WebViewMessageType.importFileRequest: {
                const count = await this._handleImportFileRequest(message.data);
                await this._postMessage({
                    type: WebViewMessageType.triggerImportDialogResponse,
                    data: { importedRulesCount: count, directoryOrFile: "file" },
                });
                break;
            }
            case WebViewMessageType.importDirectoryRequest: {
                const count = await this._handleImportDirectoryRequest(message.data);
                await this._postMessage({
                    type: WebViewMessageType.triggerImportDialogResponse,
                    data: { importedRulesCount: count, directoryOrFile: "directory" },
                });
                break;
            }
            case WebViewMessageType.autoImportRules:
                await this._handleAutoImportRules();
                break;
            case WebViewMessageType.triggerImportDialog:
                await this._handleTriggerImportDialog();
                break;
        }
    }

    private _handleSettingsPanelLoaded() {
        setTimeout(() => {
            if (this._navigateTo) {
                // Wait for the panel to be fully loaded before sending the message to navigate
                this.navigateToSection(this._navigateTo);
            }
        }, 500);
    }

    private async _handleConfigLoaded(): Promise<void> {
        try {
            const state = await this._store.get();

            const enableAgentMode = this._getEnableAgentMode();
            const enableInitialOrientation =
                !!this._featureFlagManager.currentFlags.memoriesParams.enable_initial_orientation;

            let hostTools: ToolDefinitionWithSettings[] = [];
            let toolConfigs: ToolSettings[] = [];

            if (enableAgentMode) {
                hostTools = await (this._extension.toolsModel?.getToolStatusForSettingsPanel() ??
                    Promise.resolve([]));

                toolConfigs = state.tools.map((tool) => ({
                    config: JSON.stringify(tool.config, null, 2),
                    isConfigured: tool.isConfigured,
                    name: tool.name,
                }));
            }

            const guidelines = this._guidelinesWatcher.getGuidelinesStates();

            await this._postMessage({
                type: WebViewMessageType.toolConfigInitialize,
                data: {
                    toolConfigs,
                    hostTools,
                    enableDebugFeatures: this._config.config.enableDebugFeatures,
                    settingsComponentSupported: {
                        workspaceContext: true,
                        mcpServerList: true,
                        mcpServerImport: true,
                        orientation: true,
                        remoteTools: true,
                        userGuidelines: this._featureFlagManager.currentFlags.enableGuidelines,
                        terminal: true,
                        rules: this._featureFlagManager.currentFlags.enableRules,
                    },
                    enableAgentMode: enableAgentMode,
                    enableInitialOrientation: enableInitialOrientation,
                    userTier: this._extension.userTier,
                    guidelines,
                },
            });

            // Also load MCP servers when the panel is initialized
            await this._handleGetStorageValue();
        } catch (error) {
            this._handleError(error);
        }
    }

    private async _handleConfigSave(data: ToolConfigSaveRequest): Promise<void> {
        try {
            const state = await this._store.get();

            // Parse the JSON configuration
            let parsedConfig: Record<string, unknown>;
            try {
                parsedConfig = JSON.parse(data.toolConfig) as Record<string, unknown>;
            } catch (parseError) {
                this._settingsLogger.error(`Failed to parse tool config: ${getErrmsg(parseError)}`);
                throw new ToolConfigParseError(getErrmsg(parseError));
            }

            const setting = {
                config: parsedConfig,
                isConfigured: data.isConfigured,
                name: data.toolName,
            };

            // Find existing tool or add new one
            const toolIndex = state.tools.findIndex((t) => t.name === data.toolName);
            if (toolIndex >= 0) {
                state.tools[toolIndex] = setting;
            } else {
                state.tools.push(setting);
            }

            // Save to local storage
            await this._store.save(state);
        } catch (error) {
            this._settingsLogger.error(`Error saving tool configuration: ${getErrmsg(error)}`);
            throw new ToolConfigParseError(getErrmsg(error));
        }
    }

    private async _handleGetDefinitions(message?: ToolConfigGetDefinitions): Promise<void> {
        // Default to not using cache if not specified (for backward compatibility)
        const useCache = !!message?.data?.useCache;

        let hostTools: ToolDefinitionWithSettings[] = [];
        if (this._getEnableAgentMode()) {
            hostTools = await (this._extension.toolsModel?.getToolStatusForSettingsPanel(
                useCache
            ) ?? Promise.resolve([]));
        }

        await this._postMessage({
            type: WebViewMessageType.toolConfigDefinitionsResponse,
            data: { hostTools },
        });
    }

    /**
     * Open the URL in the browser for the user to authenticate.
     */
    private async _handleStartOAuth(request: ToolConfigStartOAuthRequest): Promise<void> {
        let success = false;
        try {
            const url = request.authUrl;
            const uri = vscode.Uri.parse(url);
            success = await vscode.env.openExternal(uri);
            if (!success) {
                this._settingsLogger.warn(`Failed to open URL: ${url}`);
            }
        } catch (error) {
            this._settingsLogger.error(`Error opening URL: ${getErrmsg(error)}`);
        } finally {
            await this._postMessage({
                type: WebViewMessageType.toolConfigStartOAuthResponse,
                data: {
                    ok: success,
                },
            });
        }
    }

    /**
     * Revoke access for a tool.
     */
    private async _handleRevokeAccess(request: ToolConfigRevokeAccessRequest): Promise<void> {
        try {
            // Get the tool definitions to find the tool by its RemoteToolId
            const hostTools = await (this._extension.toolsModel?.getToolStatusForSettingsPanel() ??
                Promise.resolve([]));
            const tool = hostTools.find(
                (t) =>
                    t.identifier.hostName === request.toolId.hostName &&
                    t.identifier.toolId === request.toolId.toolId
            );

            if (tool && tool.identifier.hostName === ToolHostName.remoteToolHost) {
                const toolId = tool.identifier.toolId;
                this._settingsLogger.info(
                    `Revoking access for remote tool: ${tool.definition.name} (${toolId})`
                );

                let result: RevokeToolAccessResult;
                try {
                    result = await this._apiServer.revokeToolAccess(toolId);
                } catch (apiError) {
                    this._settingsLogger.error(`API error revoking access: ${getErrmsg(apiError)}`);
                    void vscode.window.showErrorMessage(
                        `Error revoking access: ${getErrmsg(apiError)}`
                    );
                    return;
                }

                // Show a message based on the result status
                switch (result.status) {
                    case RevokeToolAccessStatus.Success:
                        this._settingsLogger.info(
                            `Successfully revoked access for ${tool.definition.name} (${toolId}).`
                        );
                        await this._handleGetDefinitions();
                        break;
                    case RevokeToolAccessStatus.NotActive:
                        this._settingsLogger.info(
                            `Tool ${tool.definition.name} (${toolId}) has no access to revoke.`
                        );
                        await this._handleGetDefinitions();
                        break;
                    case RevokeToolAccessStatus.Unimplemented:
                        this._settingsLogger.warn(
                            `Revoking access is not implemented for ${tool.definition.name} (${toolId}).`
                        );
                        void vscode.window.showWarningMessage(
                            `Revoking access is not implemented for ${tool.definition.name} (${toolId}).`
                        );
                        break;
                    case RevokeToolAccessStatus.NotFound:
                        throw new ToolConfigError(
                            `Tool not found: ${tool.definition.name} (${toolId}).`
                        );
                    case RevokeToolAccessStatus.Failed:
                        throw new ToolConfigError(
                            `Failed to revoke access for ${tool.definition.name} (${toolId}).`
                        );
                    default:
                        throw new ToolConfigError(
                            `Unknown status (${result.status}) when revoking access for ${tool.definition.name} (${toolId}).`
                        );
                }
            } else {
                throw new ToolConfigError(
                    `Tool not found: ${request.toolId.hostName} ${request.toolId.toolId}`
                );
            }
        } catch (error) {
            this._settingsLogger.error(`Error revoking access: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    /**
     * Handle opening a confirmation modal.
     */
    private async _handleOpenConfirmationModal(
        message: OpenConfirmationModal
    ): Promise<ConfirmationModalResponse> {
        this._settingsLogger.info("Confirmation modal requested: ", message.data.title);
        try {
            const result = await vscode.window.showInformationMessage(
                message.data.message,
                { modal: true }, // Options
                { title: message.data.cancelButtonText, isCloseAffordance: true },
                { title: message.data.confirmButtonText }
            );

            return {
                type: WebViewMessageType.confirmationModalResponse,
                data: {
                    ok: result?.title === message.data.confirmButtonText,
                },
            };
        } catch (error) {
            this._settingsLogger.error(`Error handling confirmation modal: ${getErrmsg(error)}`);

            // Return error response
            return {
                type: WebViewMessageType.confirmationModalResponse,
                data: {
                    ok: false,
                    error: getErrmsg(error),
                },
            };
        }
    }

    /**
     * Get a value from storage
     */
    private async _handleGetStorageValue(): Promise<void> {
        try {
            // For MCP servers, use the ToolConfigStore
            const value = await this._store.getMCPServers();
            await this._postMessage({
                type: WebViewMessageType.getStoredMCPServersResponse,
                data: value,
            });
        } catch (error) {
            this._settingsLogger.error(`Error getting storage value: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    /**
     * Set a value in storage
     */
    private async _handleSetStorageValue(data: MCPServer[]): Promise<void> {
        try {
            // For MCP servers, use the ToolConfigStore
            await this._store.saveMCPServers(data);
        } catch (error) {
            this._settingsLogger.error(`Error setting storage value: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    private async _postMessage(msg: WebViewMessage): Promise<boolean> {
        try {
            await this._panel.webview.postMessage(msg);
            return true;
        } catch (error) {
            this._settingsLogger.error(`Failed to post message to webview: ${getErrmsg(error)}`);
            return false;
        }
    }

    private async _handleExecuteInitialOrientation(): Promise<void> {
        try {
            await vscode.commands.executeCommand(RunAgentInitialOrientationCommand.commandID);
        } catch (error) {
            this._settingsLogger.error(
                `Failed to execute initial orientation: ${getErrmsg(error)}`
            );
            this._handleError(error);
        }
    }

    private _getEnableAgentMode(): boolean {
        return isExtensionVersionGte(
            this._featureFlagManager.currentFlags.vscodeAgentModeMinVersion ?? ""
        );
    }

    /**
     * Handle a request for the current orientation status
     */
    private _handleGetOrientationStatus(): void {
        try {
            // Call the function to send orientation status to webviews
            // This will fire an event with the current status
            sendOrientationStatusToWebviews();
        } catch (error) {
            this._settingsLogger.error(`Failed to get orientation status: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    /**
     * Handle a request for terminal settings
     */
    private async _handleGetTerminalSettings(): Promise<void> {
        try {
            // Get terminal settings from the store
            const settings = await this._store.getTerminalSettings();
            await this._postMessage({
                type: WebViewMessageType.terminalSettingsResponse,
                data: settings,
            });
        } catch (error) {
            this._settingsLogger.error(`Failed to get terminal settings: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    /**
     * Handle a request to update terminal settings
     */
    private async _handleUpdateTerminalSettings(data: Partial<TerminalSettings>): Promise<void> {
        try {
            // Update the selected shell if provided
            if (data.selectedShell) {
                await this._store.updateSelectedShell(data.selectedShell);
            }

            // Update the startup script if provided
            if (data.startupScript !== undefined) {
                await this._store.updateStartupScript(data.startupScript);
            }

            // Get the updated settings and send them back
            const settings = await this._store.getTerminalSettings();
            await this._postMessage({
                type: WebViewMessageType.terminalSettingsResponse,
                data: settings,
            });
        } catch (error) {
            this._settingsLogger.error(`Failed to update terminal settings: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    /**
     * Handle a request for the list of rules
     */
    private async _handleGetRulesList(): Promise<void> {
        try {
            const rules = await RulesLoader.loadRules(this._extension.workspaceManager!);
            await this._postMessage({
                type: WebViewMessageType.getRulesListResponse,
                data: rules,
            });
        } catch (error) {
            this._settingsLogger.error(`Failed to get rules list: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    /**
     * Handle a request to create a new rule
     */
    private async _handleCreateRule(): Promise<void> {
        try {
            const ruleName = await vscode.window.showInputBox({
                prompt: "Enter a name for the new rule",
                placeHolder: "e.g. architecture.md",
                validateInput: (value) => {
                    return value.trim() ? null : "Rule name cannot be empty";
                },
            });

            if (!ruleName) {
                return;
            }

            const rule = await this._rulesWatcher.createRule({
                path: ruleName,
                content: "",
                type: RuleType.MANUAL,
            });
            if (rule?.path) {
                await this._rulesWatcher.openRule(rule.path);
            }
            await this._postMessage({
                type: WebViewMessageType.createRuleResponse,
                data: { importedRulesCount: 1 },
            });
        } catch (error) {
            this._settingsLogger.error(`Failed to create rule: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    /**
     * Handle a request to open a rule in the editor
     */
    private async _handleOpenRule(data: { path: string }): Promise<void> {
        try {
            await this._rulesWatcher.openRule(data.path);
        } catch (error) {
            this._settingsLogger.error(`Failed to open rule: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    private async _handleDeleteRule(data: { path: string }): Promise<void> {
        try {
            const ruleName = path.basename(data.path);
            const confirmation = await vscode.window.showWarningMessage(
                `Are you sure you want to delete the rule "${ruleName}"? This action cannot be undone.`,
                { modal: true },
                "Delete"
            );

            if (confirmation === "Delete") {
                this._rulesWatcher.deleteRule(data.path);
                this._settingsLogger.debug(`Rule "${ruleName}" deleted successfully.`);
            } else {
                this._settingsLogger.debug(`Deletion of rule "${ruleName}" cancelled by user.`);
            }
        } catch (error) {
            this._settingsLogger.error(`Failed to delete rule: ${getErrmsg(error)}`);
            this._handleError(error);
        }
    }

    private async _handleImportFileRequest(data: ImportFileRequestData): Promise<number> {
        try {
            // Path to the directory, expected to be relative to workspace root
            const workspaceRoot = this._extension.workspaceManager?.getBestFolderRoot();
            if (!workspaceRoot) {
                this._settingsLogger.warn("No workspace root found");
                return 0;
            }

            let filePath = path.join(workspaceRoot, data.filename);

            // Try absolute path if relative path doesn't exist
            if (!fileExists(filePath)) {
                filePath = data.filename;
                // Return early and log if file doesn't exist
                if (!fileExists(filePath)) {
                    this._settingsLogger.warn(`File not found: ${filePath}`);
                    return 0;
                }
            }

            try {
                const content = await readFileUtf8(filePath);

                const parsedRule = RulesParser.parseRuleFile(content, data.filename);
                const newFilename = `${path.parse(data.filename).name.replace(".", "")}`;
                this._rulesWatcher
                    .createRule(
                        {
                            path: newFilename,
                            content: parsedRule.content,
                            type: parsedRule.type,
                        },
                        data.autoImport
                    )
                    .then(async () => {
                        await vscode.window.showInformationMessage(
                            `Successfully created rule from ${newFilename}.`
                        );
                    })
                    .catch(async () => {
                        await vscode.window.showWarningMessage(
                            `Existing rule found for ${newFilename}.`
                        );
                    });
                return 1;
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                this._settingsLogger.error(
                    `Error reading directory ${data.filename}: ${errorMessage}`
                );
                return 0;
            }
        } catch (error) {
            const errorMsg = getErrmsg(error);
            this._settingsLogger.error(`Error importing file: ${errorMsg}`);
            return 0;
        }
    }

    private async _handleImportDirectoryRequest(data: ImportDirectoryRequestData): Promise<number> {
        try {
            // Path to the directory, expected to be relative to workspace root
            const directoryPath = data.directoryPath;
            const existingRules = await RulesLoader.loadDirectory(
                this._extension.workspaceManager!,
                directoryPath
            );
            if (existingRules.length === 0) {
                return 0;
            }
            this._settingsLogger.debug(
                `Loaded ${existingRules.length} existing rules from ${directoryPath}`
            );

            const createRulePromises = existingRules.map((existingRule) => {
                return this._rulesWatcher.createRule(
                    {
                        path: path.join(
                            data.directoryPath,
                            path.dirname(existingRule.path),
                            path.parse(existingRule.path).name
                        ),
                        content: existingRule.content,
                        type: existingRule.type,
                    },
                    data.autoImport
                );
            });
            const ruleCreationResults = await Promise.allSettled(createRulePromises);
            // COunt the number of successfully created promises
            const [createdRules, duplicateRules] = partition(
                ruleCreationResults,
                (result) => result.status === "fulfilled"
            );
            const createdCount = createdRules.length;
            const duplicateCount = duplicateRules.length;
            if (createdCount !== 0 && duplicateCount !== 0) {
                await vscode.window.showInformationMessage(
                    `Successfully created ${createdCount} rules from ${data.directoryPath}, but found ${duplicateCount} duplicate rules.`
                );
            } else if (createdCount !== 0 && duplicateCount === 0) {
                await vscode.window.showInformationMessage(
                    `Successfully created ${createdCount} rules from ${data.directoryPath}.`
                );
            } else if (createdCount === 0 && duplicateCount !== 0) {
                await vscode.window.showWarningMessage(
                    `Found ${duplicateCount} duplicate rules in ${data.directoryPath}, not creating any new rules.`
                );
            }
            return createRulePromises.length;
        } catch (error) {
            const errorMsg = getErrmsg(error);
            this._settingsLogger.error(`Error importing directory: ${errorMsg}`);
            return 0;
        }
    }

    private async _handleAutoImportRules(): Promise<void> {
        this._settingsLogger.debug("Handling auto-import rules");
        const workspaceRoot = this._extension.workspaceManager?.getBestFolderRoot();
        if (!workspaceRoot) {
            void vscode.window.showWarningMessage("No workspace folder open to import from.");
            return;
        }

        // Create QuickPick items for each directory that exists
        const items: vscode.QuickPickItem[] = [];
        for (const { directory, file, name } of AUTO_DETECT_RULES_CONFIG) {
            const ruleDirectoryExists =
                directory && directoryExists(path.join(workspaceRoot, directory));
            const ruleFileExists = file && fileExists(path.join(workspaceRoot, file));
            if (ruleDirectoryExists && ruleFileExists) {
                items.push({
                    label: name,
                    description: `Import existing rules from ${directory} and ${file}`,
                });
            } else if (ruleDirectoryExists) {
                items.push({
                    label: name,
                    description: `Import existing rules from ${directory}`,
                });
            } else if (ruleFileExists) {
                items.push({
                    label: name,
                    description: `Import existing rules from ${file}`,
                });
            }
        }
        if (items.length === 0) {
            void vscode.window.showInformationMessage("No existing rules found in workspace.");
            return;
        }
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: "Select existing rules to import from.",
        });

        if (!selected) {
            return;
        }

        const { directory, file } = AUTO_DETECT_RULES_CONFIG.find(
            (config) => config.name === selected.label
        )!;

        const importPromises = [];
        if (directory) {
            importPromises.push(
                this._handleImportDirectoryRequest({
                    directoryPath: directory,
                    autoImport: true,
                })
            );
        }
        if (file) {
            importPromises.push(
                this._handleImportFileRequest({
                    filename: file,
                    autoImport: true,
                })
            );
        }
        const results = await Promise.all(importPromises);
        const totalImportedCount = results.reduce((sum, count) => sum + count, 0);

        this._settingsLogger.debug(
            `Auto-import rules completed, total imported: ${totalImportedCount}`
        );
        await this._postMessage({
            type: WebViewMessageType.autoImportRulesResponse,
            data: { importedRulesCount: totalImportedCount, source: selected.label },
        });
    }

    private async _handleTriggerImportDialog(): Promise<void> {
        this._settingsLogger.debug("Handling trigger import dialog");
        try {
            const workspaceRootPath = this._extension.workspaceManager?.getBestFolderRoot();
            if (!workspaceRootPath) {
                this._settingsLogger.warn("No workspace root found for import dialog.");
                void vscode.window.showWarningMessage("No workspace folder open to import into.");
                return;
            }
            const workspaceRootUri = vscode.Uri.file(workspaceRootPath);

            const selectedUris = await vscode.window.showOpenDialog({
                canSelectFiles: true,
                canSelectFolders: true,
                canSelectMany: true, // Allows multiple files OR multiple folders
                defaultUri: workspaceRootUri,
                openLabel: "Import",
                filters: {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    Markdown: MARKDOWN_FILE_ENDINGS,
                },
                title: "Select files or a folder to import as rules",
            });
            this._settingsLogger.debug(`Selected URIs: ${selectedUris?.length || 0}`);

            if (!selectedUris || selectedUris.length === 0) {
                this._settingsLogger.debug("Import dialog cancelled by user.");
                return;
            }

            const files: vscode.Uri[] = [];
            const folders: vscode.Uri[] = [];

            // Stat each URI to determine if it's a file or folder
            const statsPromises = selectedUris.map(async (uri) => {
                try {
                    const stat = await vscode.workspace.fs.stat(uri);
                    return { uri, type: stat.type, error: null };
                } catch (statError) {
                    this._settingsLogger.error(
                        `Error stating URI ${uri.fsPath}: ${getErrmsg(statError)}`
                    );
                    return { uri, type: null, error: statError };
                }
            });

            const settledStats = await Promise.all(statsPromises);

            for (const item of settledStats) {
                if (item.error) {
                    // Optionally inform user about URI that couldn't be stat-ed
                    continue;
                }
                if (item.type === vscode.FileType.File) {
                    files.push(item.uri);
                } else if (item.type === vscode.FileType.Directory) {
                    folders.push(item.uri);
                }
            }

            if (folders.length === 1 && files.length === 0) {
                // Single folder selected

                const relativePath = vscode.workspace.asRelativePath(folders[0], false);
                const isInWorkspace =
                    relativePath !== folders[0].path && !relativePath.startsWith("..");
                if (!isInWorkspace) {
                    const warningMessage = `The selected folder is not within the workspace, cannot import rules.`;
                    this._settingsLogger.warn(warningMessage);
                    void vscode.window.showErrorMessage(warningMessage);
                    return;
                }
                this._settingsLogger.info(`Importing rules from directory: ${relativePath}`);

                const importedCount = await this._handleImportDirectoryRequest({
                    directoryPath: relativePath,
                    autoImport: true,
                });
                this._settingsLogger.debug(
                    `Import directory completed, total imported: ${importedCount}`
                );
                await this._postMessage({
                    type: WebViewMessageType.triggerImportDialogResponse,
                    data: { importedRulesCount: importedCount, directoryOrFile: "directory" },
                });
            } else if (files.length > 0 && folders.length === 0) {
                // Multiple files selected
                const importPromises = files.map((fileUri) => {
                    const relativePath = vscode.workspace.asRelativePath(fileUri, false);
                    this._settingsLogger.info(`Importing rules from file ${relativePath}`);
                    return this._handleImportFileRequest({
                        filename: relativePath,
                        autoImport: true,
                    });
                });

                const results = await Promise.all(importPromises);
                const totalImportedCount = results.reduce((sum, count) => sum + count, 0);
                this._settingsLogger.debug(
                    `Import files completed, total imported: ${totalImportedCount}`
                );
                await this._postMessage({
                    type: WebViewMessageType.triggerImportDialogResponse,
                    data: { importedRulesCount: totalImportedCount, directoryOrFile: "file" },
                });
            } else {
                // Invalid selection
                let warningMessage = "Invalid selection.";
                if (folders.length > 1) {
                    warningMessage = "Please select only a single folder, or multiple files.";
                } else if (files.length > 0 && folders.length > 0) {
                    warningMessage = "Please select either files or a folder, not both.";
                } else {
                    warningMessage = "Please select multiple files OR a single folder to import.";
                }
                this._settingsLogger.warn(
                    `Invalid selection for import: ${files.length} files, ${folders.length} folders.`
                );
                void vscode.window.showErrorMessage(warningMessage);
            }
        } catch (error) {
            const errorMsg = getErrmsg(error);
            this._settingsLogger.error(`Error in import dialog: ${errorMsg}`);
            void vscode.window.showErrorMessage(`An error occurred during import: ${errorMsg}`);
        }
    }

    public static createOrShow(
        extensionUri: vscode.Uri,
        extension: AugmentExtension,
        apiServer: APIServer,
        config: AugmentConfigListener,
        guidelinesWatcher: GuidelinesWatcher,
        authSessionStore: AuthSessionStore,
        navigateTo?: string
    ): SettingsWebviewPanel {
        // If we already have a panel, show it
        if (SettingsWebviewPanel.currentPanel) {
            SettingsWebviewPanel.currentPanel._panel.reveal();
            return SettingsWebviewPanel.currentPanel;
        }

        // Otherwise, create a new panel
        SettingsWebviewPanel.currentPanel = new SettingsWebviewPanel(
            extensionUri,
            extension,
            apiServer,
            config,
            guidelinesWatcher,
            authSessionStore,
            navigateTo
        );

        return SettingsWebviewPanel.currentPanel;
    }
}
