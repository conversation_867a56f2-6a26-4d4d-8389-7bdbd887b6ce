import os from "os";
import * as vscode from "vscode";

import { sha256 } from "../sha";

export type FeatureVector = Record<number, string>;

enum FeatureVectorKey {
    vscode = 0,
    machineId = 1,
    os = 2,
    cpu = 3,
    memory = 4,
    numCpus = 5,
    hostname = 6,
    arch = 7,
    username = 8,
    macAddresses = 9,
    osRelease = 10,
    kernelVersion = 11,
    checksum = 12,
}

export class Features {
    private _textEncoder = new TextEncoder();
    constructor(
        public readonly vscode: string,
        public readonly machineId: string,
        public readonly os: string,
        public readonly cpu: string,
        public readonly memory: string,
        public readonly numCpus: string,
        public readonly hostname: string,
        public readonly arch: string,
        public readonly username: string,
        public readonly macAddresses: string[],
        public readonly osRelease: string,
        public readonly kernelVersion: string
    ) {}

    calculateChecksum(vector: FeatureVector) {
        const sortedKeys = Object.keys(vector).sort();
        const values = sortedKeys.map((key) => vector[Number(key)]);
        const combined = values.join("");
        const hash = sha256(this._textEncoder.encode(combined));

        return "v1#" + hash;
    }

    canonicalize(s: string): string {
        return sha256(this._textEncoder.encode(s.toLowerCase().trim()));
    }

    canonicalizeArray(array: string[]): string {
        return this.canonicalize(array.map((s) => s.toLowerCase().trim()).join(","));
    }

    toVector() {
        const result: FeatureVector = {
            [FeatureVectorKey.vscode]: this.canonicalize(this.vscode),
            [FeatureVectorKey.machineId]: this.canonicalize(this.machineId),
            [FeatureVectorKey.os]: this.canonicalize(this.os),
            [FeatureVectorKey.cpu]: this.canonicalize(this.cpu),
            [FeatureVectorKey.memory]: this.canonicalize(this.memory),
            [FeatureVectorKey.numCpus]: this.canonicalize(this.numCpus),
            [FeatureVectorKey.hostname]: this.canonicalize(this.hostname),
            [FeatureVectorKey.arch]: this.canonicalize(this.arch),
            [FeatureVectorKey.username]: this.canonicalize(this.username),
            [FeatureVectorKey.macAddresses]: this.canonicalizeArray(this.macAddresses),
            [FeatureVectorKey.osRelease]: this.canonicalize(this.osRelease),
            [FeatureVectorKey.kernelVersion]: this.canonicalize(this.kernelVersion),
        };
        result[FeatureVectorKey.checksum] = this.calculateChecksum(result);
        return result;
    }
}

function getExternalMacAddresses(): string[] {
    const interfaces = os.networkInterfaces();
    let externalMacs: string[] = [];
    for (const iface in interfaces) {
        const ifaceInfos = interfaces[iface];
        if (!ifaceInfos) {
            continue;
        }
        if (ifaceInfos.length === 0) {
            continue;
        }
        if (ifaceInfos[0].internal) {
            continue;
        }
        for (const info of ifaceInfos) {
            externalMacs.push(info.mac);
        }
    }
    // Sort the external mac addresses to make the fingerprint stable
    externalMacs.sort();
    return externalMacs;
}
export function createFeatures() {
    const cpus = os.cpus();
    let username = "";
    try {
        username = os.userInfo().username;
    } catch (e) {
        // Ignore
    }
    const vscodeVersion = vscode.version;
    return new Features(
        vscodeVersion,
        vscode.env.machineId,
        os.type(),
        cpus[0].model,
        os.totalmem().toString(),
        cpus.length.toString(),
        os.hostname(),
        os.machine(),
        username,
        getExternalMacAddresses(),
        os.release(),
        os.version()
    );
}
