import { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { quote } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-utils";
import {
    errorToolResponse,
    successToolResponse,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import {
    LocalToolType,
    ToolBase,
    ToolSafety,
    ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { exec } from "child_process";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";

import { isDockerImageAvailable, waitForDockerImage } from "../utils/docker-utils";

enum CommandStatus {
    SUCCESS = "SUCCESS",
    SKIPPED = "SKIPPED",
    FAILED = "FAILED",
}

interface CommandResult {
    command: string;
    output: string | null;
    status: CommandStatus;
    exit_code: number | null;
}

interface SetupScriptToolResult {
    script_result: CommandResult;
    test_results: CommandResult[];
}

export class SetupScriptTool extends ToolBase<LocalToolType> {
    private static readonly TEMP_DIR_PREFIX = "augment-setup-";
    private static readonly COMBINED_SCRIPT_FILENAME = "combined-script.sh";
    private static readonly SETUP_SCRIPT_FILENAME = "setup-script.sh";
    private static readonly WORKSPACE_DIR_NAME = "workspace";
    private static readonly MAX_OUTPUT_SIZE = 10000;
    private static readonly TIMEOUT = 3600; // 60 minutes
    private readonly workspaceRoot: string;

    constructor(workspaceRoot: string) {
        super(LocalToolType.setupScript as LocalToolType, ToolSafety.Check);
        this.workspaceRoot = workspaceRoot;
    }

    public get description(): string {
        return `Run and validate a startup script to configure a development environment.
        The setup-script tool creates a fresh sandbox environment for each invocation.
        Any tools, dependencies, or configurations from previous setup-script calls are NOT preserved between calls.
        Each script must include ALL necessary setup steps required for the test commands to run successfully.
        `;
    }

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            script_content: {
                type: "string",
                description: "Content of the bash script to execute (without test commands).",
            },
            test_commands: {
                type: "array",
                items: {
                    type: "string",
                },
                description: `List of unit test commands to run after the script to verify the environment is configured correctly in the sandbox.`,
            },
        },
        required: ["script_content", "test_commands"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, unknown>,
        _chatHistory: Exchange[],
        abortSignal: AbortSignal
    ): Promise<ToolUseResponse> {
        const scriptContent = toolInput.script_content as string;
        const testCommands = (toolInput.test_commands as string[]) || [];

        try {
            return await this.testScript(scriptContent, testCommands, abortSignal);
        } catch (e: unknown) {
            return errorToolResponse(
                `Failed to execute setup script tool: ${e instanceof Error ? e.message : String(e)}`
            );
        }
    }

    /**
     * Tests a setup script with verification commands.
     */
    private async testScript(
        scriptContent: string,
        testCommands: string[],
        abortSignal: AbortSignal
    ): Promise<ToolUseResponse> {
        if (!scriptContent) {
            return errorToolResponse("Script content must be provided for testing.");
        }

        // Create a temporary script file
        const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), SetupScriptTool.TEMP_DIR_PREFIX));
        const combinedScriptPath = path.join(tempDir, SetupScriptTool.COMBINED_SCRIPT_FILENAME);
        const setupScriptPath = path.join(tempDir, SetupScriptTool.SETUP_SCRIPT_FILENAME);

        try {
            const workspaceTempDir = this.copyWorkspaceToTempDir(tempDir);
            await fs.promises.writeFile(setupScriptPath, scriptContent);

            // Create and write the combined script
            const combinedScript = this.createCombinedScript(
                `/scripts/${SetupScriptTool.SETUP_SCRIPT_FILENAME}`,
                testCommands,
                SetupScriptTool.TIMEOUT
            );
            await fs.promises.writeFile(combinedScriptPath, combinedScript, {
                mode: 0o755,
            });

            // Run the script in Docker
            const output = await this.runScriptInDocker(
                combinedScriptPath,
                tempDir,
                workspaceTempDir,
                abortSignal
            );

            const result = this.parseOutput(output, testCommands);
            if (
                result.script_result.status !== CommandStatus.SUCCESS ||
                result.test_results.some((r) => r.status !== CommandStatus.SUCCESS)
            ) {
                return errorToolResponse(JSON.stringify(result, null, 2));
            }
            return successToolResponse(JSON.stringify(result, null, 2));
        } catch (e: unknown) {
            return this.handleError(e);
        } finally {
            try {
                await fs.promises.rm(tempDir, { recursive: true, force: true });
            } catch (e) {
                // Ignore cleanup errors
            }
        }
    }

    /**
     * Creates a combined script with both setup and verification commands.
     */
    private createCombinedScript(
        scriptPath: string,
        testCommands: string[],
        timeout: number
    ): string {
        let combinedScript =
            "#!/bin/bash\n" +
            "set +e  # Don't exit on error\n" +
            'echo "START_OF_SCRIPT"\n' +
            `timeout ${timeout} bash "${scriptPath}" 2>&1\n` +
            'echo "EXIT_CODE=$?"\n' +
            'echo "END_OF_SCRIPT"\n\n';

        testCommands.forEach((cmd) => {
            combinedScript +=
                'echo "START_OF_TEST"\n' +
                `TEST_COMMAND=${quote([cmd])}\n` +
                'echo "TEST_COMMAND=$TEST_COMMAND"\n' +
                'echo "OUTPUT_START"\n' +
                `timeout ${timeout} bash -l -c "$TEST_COMMAND" 2>&1\n` +
                "LAST_EXIT_CODE=$?\n" +
                'echo "OUTPUT_END"\n' +
                'echo "EXIT_CODE=$LAST_EXIT_CODE"\n' +
                'echo "END_OF_TEST"\n';
        });

        return combinedScript;
    }

    /**
     * Runs the script in a Docker container.
     * First checks if the Docker image is available, and waits for it if necessary.
     */
    private async runScriptInDocker(
        _scriptPath: string,
        tempDir: string,
        workspaceTempDir: string,
        abortSignal: AbortSignal
    ): Promise<string> {
        // Run the script inside a Docker container
        // Mount both the scripts directory and the workspace directory
        // Set the working directory to the workspace directory
        const dockerImage = process.env.AUGMENT_AGENT_WORKSPACE_IMAGE;
        if (!dockerImage) {
            throw new Error("AUGMENT_AGENT_WORKSPACE_IMAGE environment variable is not set");
        }
        if (!(await isDockerImageAvailable(dockerImage))) {
            await waitForDockerImage(dockerImage, 300000); // 5 minutes timeout
        }
        const command = `docker run --rm \
        -v "${tempDir}:/scripts" \
        -v "${workspaceTempDir}:/mnt/persist/workspace" \
        -w "/mnt/persist/workspace" \
        --entrypoint=/bin/bash \
        ${dockerImage} \
        /scripts/${SetupScriptTool.COMBINED_SCRIPT_FILENAME}`;

        return new Promise((resolve, reject) => {
            exec(command, { signal: abortSignal }, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(stdout + stderr);
                }
            });
        });
    }

    private parseOutput(output: string, testCommands: string[]): SetupScriptToolResult {
        const scriptRegex = /START_OF_SCRIPT\n([\s\S]*?)\nEXIT_CODE=(\d+)\nEND_OF_SCRIPT/;
        const testRegex =
            /START_OF_TEST\nTEST_COMMAND=(.*?)\nOUTPUT_START\n([\s\S]*?)OUTPUT_END\nEXIT_CODE=(\d+)\nEND_OF_TEST/g;
        const result: SetupScriptToolResult = {
            script_result: {
                command: SetupScriptTool.SETUP_SCRIPT_FILENAME,
                output: null,
                status: CommandStatus.SKIPPED,
                exit_code: null,
            },
            test_results: testCommands.map((cmd) => ({
                command: cmd,
                output: null,
                status: CommandStatus.SKIPPED,
                exit_code: null,
            })),
        };

        let testMatch;
        while ((testMatch = testRegex.exec(output)) !== null) {
            const [, command, testOutput, exitCode] = testMatch;
            const code = parseInt(exitCode, 10);
            if (testCommands.includes(command)) {
                const testResult = result.test_results.find((r) => r.command === command);
                if (testResult) {
                    testResult.output = testOutput.slice(-SetupScriptTool.MAX_OUTPUT_SIZE);
                    testResult.exit_code = code;
                    testResult.status = code === 0 ? CommandStatus.SUCCESS : CommandStatus.FAILED;
                }
            }
        }

        const scriptMatch = output.match(scriptRegex);
        if (scriptMatch) {
            result.script_result.output = scriptMatch[1].slice(-SetupScriptTool.MAX_OUTPUT_SIZE);
            result.script_result.exit_code = parseInt(scriptMatch[2], 10);
            result.script_result.status =
                result.script_result.exit_code === 0 ? CommandStatus.SUCCESS : CommandStatus.FAILED;
        }
        return result;
    }

    /**
     * Handles errors that occur during script execution.
     */
    private handleError(e: unknown): ToolUseResponse {
        if (e instanceof Error) {
            return errorToolResponse(`Script test failed: ${e.message}`);
        }
        return errorToolResponse(`Script test failed with unknown error: ${String(e)}`);
    }

    /**
     * Copies workspace files to the temporary directory.
     */
    private copyWorkspaceToTempDir(tempDir: string): string {
        if (!this.workspaceRoot) {
            throw new Error("Could not determine workspace root directory");
        }

        const workspaceTempDir = path.join(tempDir, SetupScriptTool.WORKSPACE_DIR_NAME);
        fs.mkdirSync(workspaceTempDir, { recursive: true });

        // Copy files from workspace to temp directory
        // Using a simple recursive copy function
        fs.cpSync(this.workspaceRoot, workspaceTempDir, { recursive: true, force: true });
        return workspaceTempDir;
    }
}
