import {
    ChatRequestNode,
    ChatRequestNodeType,
    ChatResultNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ChatResult } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client-types";
import { ToolUseResponse } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import * as fs from "fs";
import * as path from "path";
import { promisify } from "util";
import { v4 as uuidv4 } from "uuid";

import { AgentState } from "../../agent_loop/state";
import { StatePersistence } from "../../agent_loop/state-persistence";
import { FileChangeType, RemoteAgentStatus } from "../../remote-agent-manager/types";

const mkdirAsync = promisify(fs.mkdir);
const rmAsync = promisify(fs.rm);

// Helper function to create a populated AgentState for testing
function createPopulatedAgentState(): AgentState {
    const state = new AgentState();

    // Add a user message
    const message: ChatRequestNode[] = [
        {
            id: 1,
            type: ChatRequestNodeType.TEXT,
            text_node: {
                content: "Hello",
            },
        },
    ];
    state.sendUserChat(message);
    state.beginRequest("req-123");

    // Add response chunks
    const chunk: ChatResult = {
        text: "Hello there",
        nodes: [
            {
                id: 1,
                type: ChatResultNodeType.RAW_RESPONSE,
                content: "Hello there",
            },
            {
                id: 2,
                type: ChatResultNodeType.TOOL_USE,
                tool_use: {
                    tool_use_id: "tool-1",
                    tool_name: "test-tool",
                    input_json: '{"param": "value"}',
                },
                content: "",
            },
        ],
    };
    state.pushResponseChunk(chunk);

    // Finish the response to add to chat history
    state.finishResponse();

    // Add a tool result
    const toolResult: ToolUseResponse = {
        text: "Tool result",
        isError: false,
        requestId: "tool-req-1",
    };
    state.pushToolCallResult(0, toolResult);

    // Set some internal state
    state.setLastProcessedServiceUpdateSequenceId(123);
    state.setLastUploadedSequenceId(456);

    return state;
}

describe("StatePersistence", () => {
    const testDir = path.join(__dirname, "test_states_" + uuidv4());
    let statePersistence: StatePersistence;

    beforeAll(async () => {
        // Create test directory
        await mkdirAsync(testDir, { recursive: true });
    });

    afterAll(async () => {
        // Clean up test directory
        try {
            await rmAsync(testDir, { recursive: true, force: true });
        } catch (error) {
            // Log error but don't fail the test
            const logger = {
                warn: jest.fn(),
            };
            logger.warn("Error cleaning up test directory - manual cleanup may be needed");
            logger.warn(error);
        }
    });

    beforeEach(() => {
        statePersistence = new StatePersistence(testDir);
    });

    it("save and load an empty agent state", async () => {
        const state = new AgentState();

        await statePersistence.save(state);
        const stateExist = statePersistence.checkSavedStateExists();
        expect(stateExist).toBe(true);

        // Load the agent state
        const result = await statePersistence.loadLatestAgentState();

        expect(result.conversationId).toBe(state.conversationId);
        expect(result.status).toBe(RemoteAgentStatus.agentStarting);
        expect(result.requestNodes).toEqual([]);
        expect(result.chatHistoryForDisplay).toEqual([]);
    });

    it("save and load a populated agent state", async () => {
        // Create a state with some data
        const originalState = createPopulatedAgentState();

        // Save and load
        await statePersistence.save(originalState);
        const deserializedState = await statePersistence.loadLatestAgentState();

        // Verify core properties
        expect(deserializedState.conversationId).toBe(originalState.conversationId);
        expect(deserializedState.status).toBe(originalState.status);
        expect(deserializedState.requestId).toBe(originalState.requestId);
        expect(deserializedState.responseText).toBe(originalState.responseText);
        expect(deserializedState.requestNodes).toEqual(originalState.requestNodes);
        expect(deserializedState.responseNodes).toEqual(originalState.responseNodes);
        expect(deserializedState.toolCalls).toEqual(originalState.toolCalls);
        expect(deserializedState.chatHistoryForDisplay).toEqual(
            originalState.chatHistoryForDisplay
        );
        expect(deserializedState.lastProcessedServiceUpdateSequenceId).toBe(
            originalState.lastProcessedServiceUpdateSequenceId
        );
        expect(deserializedState.lastUploadedSequenceId).toBe(originalState.lastUploadedSequenceId);
    });

    it("should return a not_found error when loading agent state for non-existent file", async () => {
        // Delete the state file if it exists
        const filePath = path.join(testDir, "agent_state.json");
        try {
            await rmAsync(filePath, { force: true });
        } catch {
            // Ignore if file doesn't exist
        }

        try {
            await statePersistence.loadLatestAgentState();
        } catch (error) {
            if (error instanceof Error) {
                expect(error.message).toContain("no such file or directory");
            } else {
                throw new Error("Expected error to be an instance of Error");
            }
        }
    });

    it("save multiple states and load the latest one", async () => {
        const state1 = new AgentState();
        const state2 = new AgentState();
        const state3 = new AgentState();

        // Modify the state before writing
        // Access private property directly for testing
        (state1 as any)._status = RemoteAgentStatus.agentStarting;
        (state2 as any)._status = RemoteAgentStatus.agentRunning;
        (state3 as any)._status = RemoteAgentStatus.agentIdle;

        await statePersistence.save(state1);
        await statePersistence.save(state2);
        await statePersistence.save(state3);

        const stateExist = statePersistence.checkSavedStateExists();
        expect(stateExist).toBe(true);

        // Load the agent state
        const result = await statePersistence.loadLatestAgentState();

        expect(result.conversationId).toBe(state3.conversationId);
        expect(result.status).toBe(RemoteAgentStatus.agentIdle);
    });
});

describe("StatePersistence with Split Chat History", () => {
    const testDir = path.join(__dirname, "test_states_split_" + uuidv4());
    let statePersistence: StatePersistence;

    beforeAll(async () => {
        // Create test directory
        await mkdirAsync(testDir, { recursive: true });
    });

    afterAll(async () => {
        // Clean up test directory
        try {
            await rmAsync(testDir, { recursive: true, force: true });
        } catch (error) {
            // Log error but don't fail the test
            const logger = {
                warn: jest.fn(),
            };
            logger.warn("Error cleaning up test directory - manual cleanup may be needed");
            logger.warn(error);
        }
    });

    beforeEach(() => {
        statePersistence = new StatePersistence(testDir);
    });
    afterEach(async () => {
        try {
            await rmAsync(testDir, { recursive: true, force: true });
        } catch (error) {
            const logger = {
                warn: jest.fn(),
            };
            logger.warn("Error cleaning up test directory - manual cleanup may be needed");
            logger.warn(error);
        }
    });

    it("should save and load an empty agent state", async () => {
        const state = new AgentState();

        await statePersistence.save(state);
        const stateExist = statePersistence.checkSavedStateExists();
        expect(stateExist).toBe(true);

        // Load the agent state
        const result = await statePersistence.loadLatestAgentState();

        expect(result.conversationId).toBe(state.conversationId);
        expect(result.status).toBe(RemoteAgentStatus.agentStarting);
        expect(result.requestNodes).toEqual([]);
        expect(result.chatHistoryForDisplay).toEqual([]);
    });

    it("should save and load a populated agent state", async () => {
        // Create a state with some data
        const originalState = createPopulatedAgentState();

        // Save and load
        await statePersistence.save(originalState);
        const deserializedState = await statePersistence.loadLatestAgentState();

        // Verify core properties
        expect(deserializedState.conversationId).toBe(originalState.conversationId);
        expect(deserializedState.status).toBe(originalState.status);
        expect(deserializedState.requestId).toBe(originalState.requestId);
        expect(deserializedState.responseText).toBe(originalState.responseText);
        expect(deserializedState.requestNodes).toEqual(originalState.requestNodes);
        expect(deserializedState.responseNodes).toEqual(originalState.responseNodes);
        expect(deserializedState.toolCalls).toEqual(originalState.toolCalls);
        expect(deserializedState.chatHistoryForDisplay).toEqual(
            originalState.chatHistoryForDisplay
        );
        expect(deserializedState.lastProcessedServiceUpdateSequenceId).toBe(
            originalState.lastProcessedServiceUpdateSequenceId
        );
        expect(deserializedState.lastUploadedSequenceId).toBe(originalState.lastUploadedSequenceId);
    });

    it("should save and load huge chat history", async () => {
        // Create a state with multiple exchanges
        const state = createPopulatedAgentState();

        // Add another exchange
        const numOfExchanges = 100;
        for (let i = 0; i < numOfExchanges; i++) {
            const message: ChatRequestNode[] = [
                {
                    id: 1,
                    type: ChatRequestNodeType.TEXT,
                    text_node: {
                        content: "Hello",
                    },
                },
            ];
            state.sendUserChat(message);
            state.beginRequest("req-123");

            const chunk: ChatResult = {
                text: "Hello there",
                nodes: [
                    {
                        id: 1,
                        type: ChatResultNodeType.RAW_RESPONSE,
                        content: "Hello there".repeat(10_000),
                    },
                ],
            };
            state.pushChangedFiles([
                {
                    old_path: "",
                    new_path: `file${i}.txt`,
                    old_contents: "",
                    // make a 5MB string
                    new_contents: "12345".repeat(1_000_000),
                    change_type: FileChangeType.added,
                },
            ]);
            state.pushResponseChunk(chunk);
            state.finishResponse();
        }
        // large string used to crash the serialization
        await statePersistence.save(state);

        // Load and verify the state
        const loadedState = await statePersistence.loadLatestAgentState();
        expect(loadedState.chatHistoryForDisplay.length).toBe(state.chatHistoryForDisplay.length);
        // confirm the state file size
        const stateFilePath = path.join(testDir, "agent_state_v2.json");
        const stateFileSize = (await fs.promises.stat(stateFilePath)).size;
        expect(stateFileSize).toBeLessThan(100_000);
        // confirm separate files exchange_1.json to exchange_101.json exists and < 10MB each
        const files = await fs.promises.readdir(testDir);
        for (let i = 1; i <= numOfExchanges; i++) {
            expect(files).toContain(`exchange_${i}.json`);
            const exchangeFilePath = path.join(testDir, `exchange_${i}.json`);
            const exchangeFileSize = (await fs.promises.stat(exchangeFilePath)).size;
            expect(exchangeFileSize).toBeLessThan(5_120_000);
        }
    }, 30_000); // 30 seconds timeout it's large file writes
});
