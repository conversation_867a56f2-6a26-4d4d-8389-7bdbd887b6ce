package com.augmentcode.intellij.chat

import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.utils.AugmentDisposable
import com.augmentcode.intellij.webviews.chat.ChatWebviewMessageBus
import com.augmentcode.rpc.*
import com.intellij.ide.DisplayChangeDetector
import com.intellij.ide.ui.UISettings
import com.intellij.ide.ui.UISettingsListener
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.readAction
import com.intellij.openapi.application.smartReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.editor.event.SelectionEvent
import com.intellij.openapi.editor.event.SelectionListener
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.FileEditorManagerEvent
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.project.BaseProjectDirectories.Companion.getBaseDirectories
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.text.LineColumn
import com.intellij.openapi.util.text.StringUtil
import kotlinx.coroutines.*

@Service(Service.Level.PROJECT)
class AugmentChatIdeEventService(
  private val project: Project,
  private val cs: CoroutineScope,
) : SelectionListener,
  FileEditorManagerListener,
  DumbService.DumbModeListener,
  UISettingsListener,
  DisplayChangeDetector.Listener,
  Disposable {
  private val messageBusConnection = project.messageBus.connect(this)

  init {
    registerListeners()
  }

  private fun createSyncStatus(status: SyncingStatus): SourceFoldersSyncStatus =
    SourceFoldersSyncStatus
      .newBuilder()
      .setData(
        SyncingStatusEvent
          .newBuilder()
          .setStatus(status)
          .build(),
      ).build()

  // registerListeners are initialized when the service is created and cleaned up when the service is disposed
  private fun registerListeners() {
    EditorFactory.getInstance().eventMulticaster.addSelectionListener(
      this,
      AugmentDisposable.getInstance(project),
    )

    messageBusConnection.subscribe(
      FileEditorManagerListener.FILE_EDITOR_MANAGER,
      this,
    )

    messageBusConnection.subscribe(
      DumbService.DUMB_MODE,
      this,
    )

    messageBusConnection.subscribe(
      UISettingsListener.TOPIC,
      this,
    )
    DisplayChangeDetector.getInstance().addListener(this)
  }

  suspend fun getSourceFoldersUpdated(): SourceFoldersUpdated {
    val sourceFoldersBuilder = SourceFoldersUpdatedData.newBuilder()
    readAction {
      // Use project.getBaseDirectories() instead of ProjectRootManager.getInstance(project).contentRoots
      // because getBaseDirectories gives us content roots that are not contained within other content roots.
      project.getBaseDirectories()
    }.map {
      sourceFoldersBuilder.addSourceFolders(
        ISourceFolderInfo
          .newBuilder()
          .setFolderRoot(it.path),
      )
    }
    return SourceFoldersUpdated
      .newBuilder()
      .setData(sourceFoldersBuilder.build())
      .build()
  }

  fun sendCurrentlyOpenedFiles() {
    cs.launch(Dispatchers.IO) {
      // We might be waiting a long time, so we kick off a separate coroutine.
      // it's ok if this finishes later after chat-initialize.
      DumbService.getInstance(project).waitForSmartMode()

      if (FileEditorManager.getInstance(project).selectedEditor != null) {
        val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)

        publisher.postMessageToWebview(getCurrentlyOpenedFiles())
      }
    }
  }

  private suspend fun getCurrentlyOpenedFiles(): CurrentlyOpenFiles =
    smartReadAction(project) {
      val currentlyOpenFilesBuilder = CurrentlyOpenFiles.newBuilder()
      val editor = FileEditorManager.getInstance(project).selectedEditor
      if (editor != null) {
        val pathWithRoot = AugmentRoot.findRelativePathWithRoot(project, editor.file)
        if (pathWithRoot?.relativePath != null) {
          currentlyOpenFilesBuilder.addData(
            FileDetails
              .newBuilder()
              .setRepoRoot(pathWithRoot.rootPath)
              .setPathName(pathWithRoot.relativePath),
          )
        }
      }
      currentlyOpenFilesBuilder.build()
    }

  // called when the user switches between files
  // Our VS Code implementation has a `throttle` on these type of listeners
  // we may find we also need that, but it seems to be ok without it for now
  override fun selectionChanged(e: FileEditorManagerEvent) {
    // Move the slow operation off EDT
    cs.launch(Dispatchers.IO) {
      val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)

      // if indexes are not ready then don't bother telling the webview about open files because
      // we may not be able to figure out their blobs.
      if (e.newFile == null || DumbService.getInstance(project).isDumb) {
        publisher.postMessageToWebview(
          CurrentlyOpenFiles.newBuilder().build(),
        )
      } else {
        val path = AugmentRoot.findRelativePathWithRoot(project, e.newFile!!)
        if (path == null) {
          publisher.postMessageToWebview(
            CurrentlyOpenFiles.newBuilder().build(),
          )
        } else {
          publisher.postMessageToWebview(
            CurrentlyOpenFiles
              .newBuilder()
              .addData(
                FileDetails
                  .newBuilder()
                  .setRepoRoot(path.rootPath)
                  .setPathName(path.relativePath),
              ).build(),
          )
        }
      }
    }
  }

  // Called when the user selects a range of text in the editor
  // Our VS Code implementation has a `throttle` on these type of listeners
  // we may find we also need that, but it seems to be ok without it for now
  override fun selectionChanged(e: SelectionEvent) {
    if (e.newRange.isEmpty) {
      ChatWebviewMessageBus.syncPublisher(project).postMessageToWebview(
        FileRangesSelected.newBuilder().build(),
      )
      return
    }

    // Move the slow operation off EDT
    cs.launch(Dispatchers.IO) {
      val doc = e.editor.document
      val startLineCol: LineColumn? = StringUtil.offsetToLineColumn(doc.text, e.newRange.startOffset)
      val endLineCol: LineColumn? = StringUtil.offsetToLineColumn(doc.text, e.newRange.endOffset)

      if (startLineCol == null || endLineCol == null) {
        ChatWebviewMessageBus.syncPublisher(project).postMessageToWebview(
          FileRangesSelected.newBuilder().build(),
        )
        return@launch
      }

      val rootAndPath =
        if (DumbService.getInstance(project).isDumb) {
          null
        } else if (e.editor.virtualFile == null) {
          null
        } else {
          AugmentRoot.findRelativePathWithRoot(project, e.editor.virtualFile)
        }

      val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
      publisher.postMessageToWebview(
        FileRangesSelected
          .newBuilder()
          .addData(
            FileDetails
              .newBuilder()
              .setRepoRoot(rootAndPath?.rootPath ?: "")
              .setPathName(rootAndPath?.relativePath ?: "")
              .setFullRange(
                expandToLineBoundaries(startLineCol, endLineCol, doc),
              ).setOriginalCode(doc.getText(e.newRange)),
          ).build(),
      )
    }
  }

  private fun expandToLineBoundaries(
    startLineCol: LineColumn,
    endLineCol: LineColumn,
    document: Document,
  ): FullRange {
    // This effectively means that line is not selected,
    // but cursor is placed at the beginning of this line
    // We should not include this line into selection, then.
    var endLine = endLineCol.line
    if (endLineCol.column == 0 && endLine > startLineCol.line) {
      endLine -= 1
    }
    val endColumn = document.getLineEndOffset(endLine)

    return FullRange
      .newBuilder()
      .setStartLineNumber(startLineCol.line)
      .setStartColumn(0)
      .setEndLineNumber(endLine)
      .setEndColumn(endColumn)
      .build()
  }

  override fun exitDumbMode() {
    val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
    publisher.postMessageToWebview(
      createSyncStatus(SyncingStatus.done),
    )
    sendCurrentlyOpenedFiles()
  }

  override fun enteredDumbMode() {
    val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
    publisher.postMessageToWebview(
      createSyncStatus(SyncingStatus.running),
    )
  }

  override fun uiSettingsChanged(uiSettings: UISettings) {
    val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
    publisher.reloadIntellijStyles()
  }

  override fun displayChanged() {
    // displayChanged will sometimes be run on the AppKit thread in macOS.
    // Errors on the AppKit thread can crash the IDE so we need invokeLater.
    invokeLater {
      val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)
      publisher.reloadIntellijStyles()
    }
  }

  override fun dispose() {
    // Other listeners are automatically disposed by the project since we used messageBus.connect(this),
    // but we need to manually remove the DisplayChangeDetector listener.
    DisplayChangeDetector.getInstance().removeListener(this)
  }
}
