package com.augmentcode.intellij.metrics

import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.PathManager
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date

@Service(Service.Level.APP)
class EdtFreezeDetector(private val cs: CoroutineScope) : Disposable {
  companion object {
    val instance: EdtFreezeDetector
      get() = service<EdtFreezeDetector>()
  }

  private val logger = thisLogger()

  private var monitoringJob: Job? = null
  private var lastLogTimeMs = 0L

  init {
    PluginStateService.instance.subscribe(
      ApplicationManager.getApplication().messageBus.connect(this),
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext,
          state: PluginState,
        ) {
          if (context.isSignedIn) {
            // Check if EDT freeze detection is enabled via feature flags
            if (context.flags.enableEdtFreezeDetection) {
              logger.info("Starting EDT freeze detection based on feature flag")
              startMonitoring()
              return
            }
          }
          // If we are here, the plugin is not ready/signed-in or the feature flag is not enabled
          logger.info("Stopping EDT freeze detection")
          stopMonitoring()
        }
      },
      // Run the `onStateChange()` callback immediately to start monitoring if the plugin is already enabled
      triggerOnStateChange = true,
    )
  }

  fun startMonitoring() {
    if (monitoringJob != null) return

    logger.info("Starting EDT freeze detection")
    monitoringJob =
      cs.launch {
        while (isActive) {
          checkEdtResponsiveness()
        }
      }
  }

  /**
   * Checks EDT responsiveness and logs thread dumps if necessary.
   * Puts a completable deferred on the EDT and waits for it,
   * checking periodically with exponential backoff.
   */
  private suspend fun checkEdtResponsiveness() {
    val edtDeferred = CompletableDeferred<Long>()

    invokeLater {
      edtDeferred.complete(System.currentTimeMillis())
    }
    val startTime = System.currentTimeMillis()

    // Log at specific intervals: 200ms, 1s, 5s, 20s, 60s
    val logIntervals = listOf(200L, 1000L, 5000L, 20000L, 60000L)

    for (interval in logIntervals) {
      val result = withTimeoutOrNull(interval) { edtDeferred.await() }
      if (result != null) return // EDT became responsive

      logger.warn("EDT has been frozen for ${interval}ms, logging thread dump")
      saveThreadDumpToFile()
    }

    // Wait for EDT to become responsive if still not completed
    if (!edtDeferred.isCompleted) {
      edtDeferred.await()
      thisLogger().warn("EDT is now responsive after ${System.currentTimeMillis() - startTime}ms.")
    }
  }

  private fun captureThreadDump(outputFile: File) {
    val allStackTraces = Thread.getAllStackTraces()

    outputFile.bufferedWriter().use { writer ->
      writer.appendLine("=== Thread Dump ===")
      writer.appendLine("Timestamp: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(Date())}")
      writer.appendLine("Freeze type: EDT freeze")
      writer.appendLine()

      for ((thread, stackTrace) in allStackTraces) {
        writer.appendLine("\"${thread.name}\" #${thread.threadId()} ${thread.state}")

        // Look for EDT thread specifically
        if (thread.name.contains("AWT-EventQueue", ignoreCase = true)) {
          writer.appendLine("*** EDT THREAD ***")
        }

        // Print stack trace
        for (element in stackTrace) {
          writer.appendLine("\tat $element")
        }
        writer.appendLine()
      }
      writer.appendLine("===================")
    }
  }

  private fun saveThreadDumpToFile() {
    lastLogTimeMs = System.currentTimeMillis()
    val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss").format(Date())
    val fileName = "augment-detected-edt-freeze-$timestamp.log"

    try {
      // Get the log directory (same as where idea.log and sidecar logs are stored)
      val logDir = PathManager.getLogPath()
      val file = File(logDir, fileName)
      captureThreadDump(file)

      thisLogger().info("Thread dump written to: ${file.absolutePath}")
    } catch (e: Exception) {
      thisLogger().error("Failed to write thread dump to file: ${e.message}")
    }
  }

  fun stopMonitoring() {
    monitoringJob?.cancel()
    monitoringJob = null
  }

  override fun dispose() {
    stopMonitoring()
  }
}
