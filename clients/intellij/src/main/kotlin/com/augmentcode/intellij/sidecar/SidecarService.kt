package com.augmentcode.intellij.sidecar

import com.augmentcode.api.*
import com.augmentcode.api.AgentInterruptionData
import com.augmentcode.api.AgentRequestEvent
import com.augmentcode.api.AgentSessionEvent
import com.augmentcode.api.AgentTracingData
import com.augmentcode.api.InitialOrientationData
import com.augmentcode.api.MemoriesFileOpenData
import com.augmentcode.api.RememberToolCallData
import com.augmentcode.api.StringStats
import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.api.getUserAgent
import com.augmentcode.intellij.api.toTruncatedApiExchange
import com.augmentcode.intellij.auth.AugmentCredentials
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.auth.CredentialsChangeListener
import com.augmentcode.intellij.auth.CredentialsMessageBusWrapper
import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.memories.MemoriesService
import com.augmentcode.intellij.settings.AugmentIntegrationsConfig
import com.augmentcode.intellij.settings.FeatureFlagManager
import com.augmentcode.intellij.sidecar.clientinterfaces.ClientWorkspaces
import com.augmentcode.intellij.sidecar.clientinterfaces.PluginFileStore
import com.augmentcode.intellij.sidecar.clientinterfaces.PluginStateForSidecar
import com.augmentcode.intellij.sidecar.tools.ToolsManager
import com.augmentcode.intellij.sidecarrpc.Connection
import com.augmentcode.intellij.sidecarrpc.IOConnection
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.sidecar.rpc.*
import com.augmentcode.sidecar.rpc.chat.ChatHistoryItem
import com.augmentcode.sidecar.rpc.chat.ChatStreamRequest
import com.augmentcode.sidecar.rpc.chat.ChatStreamResponse
import com.augmentcode.sidecar.rpc.clientInterfaces.*
import com.augmentcode.sidecar.rpc.clientInterfaces.CheckToolSafetyResponse
import com.augmentcode.sidecar.rpc.tools.*
import com.google.gson.GsonBuilder
import com.google.protobuf.Empty
import com.google.protobuf.Struct
import com.google.protobuf.kotlin.toByteString
import com.intellij.diff.DiffContentFactory
import com.intellij.diff.DiffManager
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.PathManager
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.platform.ide.progress.withBackgroundProgress
import kotlinx.coroutines.*
import java.io.File
import java.util.concurrent.TimeUnit
import kotlin.io.path.*
import com.augmentcode.sidecar.rpc.clientInterfaces.AgentCodebaseRetrievalRequest as ProtoAgentCodebaseRetrievalRequest
import com.augmentcode.sidecar.rpc.clientInterfaces.AgentCodebaseRetrievalResponse as ProtoAgentCodebaseRetrievalResponse
import com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData as AgentTracingDataProto
import com.augmentcode.sidecar.rpc.tools.RemoteToolId as ProtoRemoteToolId

private fun ProtoRemoteToolId.toApi(): RemoteToolId {
  return RemoteToolId.values().find { it.value == this.number } ?: RemoteToolId.Unknown
}

@Service(Service.Level.PROJECT)
class SidecarService(private val project: Project, private val cs: CoroutineScope) : CredentialsChangeListener, Disposable {
  private val logger = Logger.getInstance(SidecarService::class.java)
  private var process: Process? = null

  private var initialChatMode: ChatMode = ChatMode.CHAT
  private var toolsManager: ToolsManager? = null
  private var pluginState = PluginStateForSidecar()
  private var pluginFileStore = PluginFileStore(project)
  private var connection: Connection? = null
  private val gson = GsonBuilder().create()
  private val credsMessageBus = CredentialsMessageBusWrapper(cs)

  private var serviceState = SidecarState.SIDECAR_STOPPED

  init {
    credsMessageBus.subscribe(project.messageBus.connect(this), this)
  }

  fun state(): SidecarState {
    return serviceState
  }

  // Returns a deferred that is completed when the sidecar is ready or cancelled if the initial node download fails.
  fun ready(): kotlinx.coroutines.Deferred<Unit> {
    val ready = kotlinx.coroutines.CompletableDeferred<Unit>()
    val messageBusConnection = project.messageBus.connect(this)
    messageBusConnection.subscribe(
      SidecarMessageBus.SIDECAR_MESSAGE_TOPIC,
      object : SidecarMessageBus {
        override fun emitState(
          state: SidecarState,
          status: String?,
        ) {
          when (state) {
            SidecarState.SIDECAR_RUNNING -> {
              ready.complete(Unit)
              messageBusConnection.dispose()
            }
            SidecarState.SIDECAR_STARTING -> { /* NOOP */ }
            else -> {
              ready.cancel(status ?: "Sidecar failed to start: $state")
              messageBusConnection.dispose()
            }
          }
        }
      },
    )
    if (serviceState == SidecarState.SIDECAR_RUNNING) {
      ready.complete(Unit)
      messageBusConnection.dispose()
      return ready
    }
    return ready
  }

  // Returns true when the sidecar is initialized and alive.
  fun isAvailable(): Boolean {
    return serviceState == SidecarState.SIDECAR_RUNNING && (process?.isAlive == true)
  }

  fun startServer() {
    cs.launch {
      withBackgroundProgress(project, AugmentBundle.message("augment.starting.agents.sidecar.title"), false) {
        startBlockingServer()
      }
    }
  }

  private fun updateState(
    state: SidecarState,
    status: String? = null,
  ) {
    if (state == serviceState) {
      logger.info("Sidecar state is already $state, not updating")
      return
    }
    logger.info("Updating sidecar state to $state")
    serviceState = state

    // When the sidecar service is disposed, the process is stopped and the state
    // is set to SIDECAR_STOPPED. However, the message bus cannot be used if the
    // project is disposed.
    if (!project.isDisposed) {
      cs.launch {
        SidecarMessageBus.syncPublisher(project).emitState(state, status)
      }
    }
  }

  private suspend fun startBlockingServer() {
    try {
      if (process != null || connection != null) {
        // Service is already running
        return
      }

      updateState(SidecarState.SIDECAR_STARTING)

      val featureFlagManager = FeatureFlagManager.instance

      val nodeInstallationService = NodeInstallationService.instance
      val nodeBinaryDeferable =
        nodeInstallationService.getNodeBinary(
          initialNodeDownloadFailedCallback = {
            updateState(SidecarState.SIDECAR_ERROR, "Initial sidecar download failed")
          },
        )
      val nodeBinary = nodeBinaryDeferable.await()
      if (nodeBinary == null) {
        logger.warn("Unable to find node binary")
        updateState(SidecarState.SIDECAR_ERROR, "Unable to find node binary")
        return
      }

      if (AugmentOAuthState.instance.getCredentials() == null) {
        logger.info("Not starting sidecar, not signed in")
        updateState(SidecarState.SIDECAR_ERROR, "Not starting sidecar, not signed in")
        return
      }

      logger.info("Starting sidecar...")

      // Get the bundled LSP server file
      val resourceStream =
        javaClass.classLoader.getResourceAsStream("sidecar/index.js")
          ?: throw RuntimeException("Failed to load LSP server resource")

      // Create a temporary file to store the LSP server
      val tempFile = File.createTempFile("augment-lsp", "")
      tempFile.deleteOnExit()

      // Copy the resource to the temporary file
      tempFile.outputStream().use { output ->
        resourceStream.copyTo(output)
      }

      // Make the temporary file executable
      tempFile.setExecutable(true)

      logger.info("Spawning sidecar process...")

      // Get the log directory and create a path for the sidecar log file
      val logDir = PathManager.getLogPath()
      val sidecarLogFile = File(logDir, "augment-sidecar.log").absolutePath
      logger.info("Sidecar logs will be written to: $sidecarLogFile")

      var sidecarCommand =
        mutableListOf(
          nodeBinary.toString(),
          tempFile.absolutePath,
          "--log-file",
          sidecarLogFile,
          "--stdio",
        )
      val os = System.getProperty("os.name").lowercase()
      val isWindows = os.contains("windows")
      if (!isWindows) {
        val shellEnv = System.getenv("SHELL")
        val shell = shellEnv ?: "/bin/sh"
        sidecarCommand =
          mutableListOf(
            shell,
            "-l",
            "-c",
            "\"${sidecarCommand.joinToString(" ").let { cmd ->
              // Escape for shell
              "\"${cmd.replace("\"", "\\\"")}\""
            }}\"",
          )
      }

      this.logger.info("Running sidecar command: $sidecarCommand")

      process =
        ProcessBuilder(sidecarCommand)
          .redirectError(ProcessBuilder.Redirect.PIPE)
          .start()

      connection = IOConnection(process!!.inputStream, process!!.outputStream, cs)
      toolsManager = ToolsManager(project, connection!!, initialChatMode)

      // The augmentcode/client-workspaces/* methods
      connection!!.onRequest("augmentcode/client-workspaces/getcwd", Empty.getDefaultInstance().javaClass) { _ ->
        GetCWDResponse.newBuilder().setCwd(this.getCwd()).build()
      }
      connection!!.onRequest("augmentcode/client-workspaces/readFile", ReadFileRequest.getDefaultInstance().javaClass) { params ->
        // unpack params into request
        val request = params.unpack(ReadFileRequest::class.java)

        val fileDetails = ClientWorkspaces(project).readFile(request.filePath)

        val builder = ReadFileResponse.newBuilder()
        if (fileDetails.qualifiedPath != null) {
          builder.setFilepath(fileDetails.qualifiedPath)
        }
        if (fileDetails.contents != null) {
          builder.setContents(fileDetails.contents)
        }
        builder.build()
      }

      connection!!.onRequest("augmentcode/client-workspaces/writeFile", WriteFileRequest.getDefaultInstance().javaClass) { params ->
        // unpack params into request
        val request = params.unpack(WriteFileRequest::class.java)

        if (request.filePath == null) {
          return@onRequest Empty.getDefaultInstance()
        }

        // Workaround so memory file updates are updated in the editor immediately
        if (request.filePath.relPath == MemoriesService.getInstance(project).getMemoriesAbsPath()) {
          MemoriesService.getInstance(project).updateMemories(request.contents)
          return@onRequest Empty.getDefaultInstance()
        }

        val rootPath = request.filePath.rootPath
        val relPath = request.filePath.relPath

        try {
          val existingFile = AugmentRoot.findFile(rootPath, relPath)
          WriteCommandAction.runWriteCommandAction(project) {
            try {
              if (existingFile != null) {
                VfsUtil.saveText(existingFile, request.contents)
              } else {
                // If file doesn't exist, create it
                val fullPath = Path(rootPath, relPath)
                val file = VfsUtil.createDirectoryIfMissing(fullPath.parent.pathString)?.findOrCreateChildData(this, fullPath.name)
                if (file != null) {
                  VfsUtil.saveText(file, request.contents)
                }
              }
            } catch (e: Exception) {
              logger.warn("Failed to write file: ${e.message}", e)
            }
          }
        } catch (e: Exception) {
          logger.warn("Failed to write file: ${e.message}", e)
        }

        Empty.getDefaultInstance()
      }

      connection!!.onRequest("augmentcode/client-workspaces/deleteFile", DeleteFileRequest.getDefaultInstance().javaClass) { params ->
        // unpack params into request
        val request = params.unpack(DeleteFileRequest::class.java)

        if (request.filePath == null) {
          return@onRequest Empty.getDefaultInstance()
        }

        val rootPath = request.filePath.rootPath
        val relPath = request.filePath.relPath

        WriteCommandAction.runWriteCommandAction(project) {
          AugmentRoot.findFile(rootPath, relPath)?.delete(this)
        }

        Empty.getDefaultInstance()
      }

      connection!!.onRequest(
        "augmentcode/client-workspaces/getQualifiedPathName",
        GetQualifiedPathNameRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(GetQualifiedPathNameRequest::class.java)

        val qualifiedPath = ClientWorkspaces(project).getQualifiedPathName(request.path)

        val builder = GetQualifiedPathNameResponse.newBuilder()
        if (qualifiedPath != null) {
          builder.setFilepath(qualifiedPath)
        }
        builder.build()
      }

      connection!!.onRequest("augmentcode/client-workspaces/findFiles", FindFilesRequest.getDefaultInstance().javaClass) { params ->
        // unpack params into request
        val request = params.unpack(FindFilesRequest::class.java)

        val includeGlob = request.includeGlob
        val excludeGlob = if (request.excludeGlob.isNullOrEmpty()) null else request.excludeGlob
        val maxResults = if (request.maxResults <= 0) null else request.maxResults

        logger.info("Finding files with pattern: $includeGlob, exclude: $excludeGlob, max: $maxResults")

        val files = ClientWorkspaces(project).findFiles(includeGlob, excludeGlob, maxResults)

        logger.info("Found ${files.size} files matching pattern $includeGlob")

        FindFilesResponse.newBuilder()
          .addAllFiles(files)
          .build()
      }

      connection!!.onRequest("augmentcode/client-workspaces/getPathInfo", GetPathInfoRequest.getDefaultInstance().javaClass) { params ->
        // unpack params into request
        val request = params.unpack(GetPathInfoRequest::class.java)

        val path = request.path
        logger.info("Getting path info for: $path")

        val pathInfo = ClientWorkspaces(project).getPathInfo(path)

        val builder = GetPathInfoResponse.newBuilder()
        if (pathInfo.type != null) {
          builder.setType(pathInfo.type)
        }
        if (pathInfo.filepath != null) {
          builder.setFilepath(pathInfo.filepath)
        }
        if (pathInfo.exists != null) {
          builder.setExists(pathInfo.exists)
        }
        builder.build()
      }

      connection!!.onRequest("augmentcode/client-workspaces/listDirectory", ListDirectoryRequest.getDefaultInstance().javaClass) { params ->
        // unpack params into request
        val request = params.unpack(ListDirectoryRequest::class.java)

        val path = request.path
        val depth = request.depth
        val showHidden = request.showHidden
        logger.info("Listing directory: $path, depth: $depth, showHidden: $showHidden")

        val result = ClientWorkspaces(project).listDirectory(path, depth, showHidden)

        val builder = ListDirectoryResponse.newBuilder()
        builder.addAllEntries(result.entries)
        if (result.errorMessage != null) {
          builder.setErrorMessage(result.errorMessage)
        }
        builder.build()
      }

      // the augmentcode/plugin-filestore/* methods
      connection!!.onRequest(
        "augmentcode/plugin-filestore/loadAsset",
        LoadAsset.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(LoadAsset::class.java)
        val asset = pluginFileStore.loadAsset(request.path) ?: return@onRequest LoadAssetResponse.newBuilder().build()
        LoadAssetResponse.newBuilder().setContents(asset.toByteString()).build()
      }
      connection!!.onRequest(
        "augmentcode/plugin-filestore/saveAsset",
        SaveAsset.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(SaveAsset::class.java)
        pluginFileStore.saveAsset(request.path, request.contents.toByteArray())
        Empty.getDefaultInstance()
      }
      connection!!.onRequest(
        "augmentcode/plugin-filestore/deleteAsset",
        DeleteAsset.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(DeleteAsset::class.java)
        pluginFileStore.deleteAsset(request.path)
        Empty.getDefaultInstance()
      }

      // The augmentcode/plugin-state-for-sidecar/* methods
      connection!!.onRequest(
        "augmentcode/plugin-state-for-sidecar/get-value",
        GetStateValueRequest.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(GetStateValueRequest::class.java)
        return@onRequest pluginState.handleGetRequest(request)
      }
      connection!!.onRequest(
        "augmentcode/plugin-state-for-sidecar/set-value",
        SetStateValueRequest.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(SetStateValueRequest::class.java)
        return@onRequest pluginState.handleSetRequest(request)
      }

      // The augmentcode/api-client/* methods
      connection!!.onRequest(
        "augmentcode/api-client/chat-stream",
        ChatStreamRequest.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(ChatStreamRequest::class.java)
        val progressToken = request.progressToken

        try {
          val chatRequest = gson.fromJson(request.requestPayloadJson, ChatRequest::class.java)
          val (_, flowOfResults) = AugmentAPI.instance.chatRawResponse(chatRequest)
          flowOfResults.collect { result ->
            if (result.isFailure) {
              thisLogger().warn("Failed to get chat response: ${result.exceptionOrNull()}")
              return@collect
            }
            connection!!.sendProgress(progressToken, ChatStreamResponse.newBuilder().setResponsePayloadJson(result.getOrNull()!!).build())
          }

          // Close the connection after streaming response is complete
          return@onRequest Empty.getDefaultInstance()
        } catch (e: Exception) {
          return@onRequest Empty.getDefaultInstance()
        }
      }

      connection!!.onRequest(
        "augmentcode/api-client/agent-codebase-retrieval",
        ProtoAgentCodebaseRetrievalRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(ProtoAgentCodebaseRetrievalRequest::class.java)
        val apiRequest =
          AgentCodebaseRetrievalRequest().apply {
            informationRequest = request.informationRequest
            maxOutputLength = request.maxOutputLength
          }
        apiRequest.blobs = AugmentRemoteSyncingManager.getInstance(project).synchronizedBlobsPayload()
        apiRequest.chatHistory = toTruncatedApiExchange(request.chatHistoryList, project)
        try {
          val response = AugmentAPI.instance.agentCodebaseRetrieval(apiRequest)
          return@onRequest ProtoAgentCodebaseRetrievalResponse.newBuilder()
            .setFormattedRetrieval(response.formattedRetrieval)
            .build()
        } catch (e: Exception) {
          logger.warn("Failed to perform codebase retrieval: ${e.message}", e)
          return@onRequest ProtoAgentCodebaseRetrievalResponse.newBuilder()
            .build()
        }
      }
      connection!!.onRequest(
        "augmentcode/api-client/check-tool-safety",
        CheckToolSafetyRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(CheckToolSafetyRequest::class.java)
        try {
          val apiResp = AugmentAPI.instance.checkToolSafety(request.toolIdValue, request.toolInputJson)
          return@onRequest CheckToolSafetyResponse.newBuilder()
            .setIsSafe(apiResp.is_safe)
            .build()
        } catch (e: Exception) {
          logger.warn("Failed to perform tool safety check: ${e.message}", e)
          return@onRequest CheckToolSafetyResponse.newBuilder()
            .setIsSafe(false)
            .build()
        }
      }
      connection!!.onRequest(
        "augmentcode/api-client/log-agent-request-event",
        LogAgentRequestEvent.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(LogAgentRequestEvent::class.java)
        logger.info("Logging agent request event: $request")
        val eventsByRequestId = mutableMapOf<String, MutableList<AgentRequestEvent>>()
        for (event in request.eventsList) {
          // Get or initialize the events array for this request_id
          val eventsForRequestId = eventsByRequestId.getOrPut(event.requestId) { mutableListOf() }

          // Create a modified event without the request_id field
          val eventWithoutRequestId =
            AgentRequestEvent().apply {
              event_time_sec = event.eventTimeSec
              event_time_nsec = event.eventTimeNsec
              event_name = event.eventName
              conversation_id = event.conversationId
              request_id = event.requestId
              chat_history_length = event.chatHistoryLength
              user_agent = getUserAgent()
            }
          eventsForRequestId.add(eventWithoutRequestId)
        }

        for ((requestId, eventsForRequestId) in eventsByRequestId) {
          try {
            AugmentAPI.instance.recordRequestEvents(
              requestId,
              RecordAgentRequestBody().apply {
                events =
                  eventsForRequestId.map { e ->
                    val timeMillis = (e.event_time_sec * 1000L) + (e.event_time_nsec / 1000000L)
                    RecordAgentRequestEvent().apply {
                      time = java.time.Instant.ofEpochMilli(timeMillis).toString()
                      event =
                        RecordAgentRequestEventEvent().apply {
                          agent_request_event = e
                        }
                    }
                  }
              },
            )
          } catch (e: Exception) {
            logger.warn("Failed to record request events: ${e.message}", e)
          }
        }
        Empty.getDefaultInstance()
      }
      connection!!.onRequest(
        "augmentcode/api-client/log-agent-session-event",
        LogAgentSessionEvent.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(LogAgentSessionEvent::class.java)
        logger.info("Logging agent session event: $request")
        AugmentAPI.instance.recordSessionEvents(
          RecordAgentSessionBody().apply {
            client_name = "intellij-extension"
            events =
              request.eventsList.map { e ->
                val timeMillis = (e.eventTimeSec * 1000L) + (e.eventTimeNsec / 1000000L)
                RecordAgentSessionEvent().apply {
                  time = java.time.Instant.ofEpochMilli(timeMillis).toString()
                  event =
                    RecordAgentSessionEventEvent().apply {
                      agent_session_event =
                        AgentSessionEvent().apply {
                          user_agent = getUserAgent()
                          event_time_sec = e.eventTimeSec
                          event_time_nsec = e.eventTimeNsec
                          event_name = e.eventName
                          conversation_id = e.conversationId
                          event_data =
                            if (e.hasEventData()) {
                              AgentSessionEventData().apply {
                                agent_interruption_data =
                                  if (e.eventData.hasAgentInterruptionData()) {
                                    AgentInterruptionData().apply {
                                      requestId = e.eventData.agentInterruptionData.requestId
                                      currConversationLength = e.eventData.agentInterruptionData.currConversationLength
                                    }
                                  } else {
                                    null
                                  }
                                remember_tool_call_data =
                                  if (e.eventData.hasRememberToolCallData()) {
                                    RememberToolCallData().apply {
                                      caller = e.eventData.rememberToolCallData.caller
                                      is_complex_new_memory = e.eventData.rememberToolCallData.isComplexNewMemory
                                      tracing_data = convertToAgentTracingData(e.eventData.rememberToolCallData.tracingData)
                                    }
                                  } else {
                                    null
                                  }
                                memories_file_open_data =
                                  if (e.eventData.hasMemoriesFileOpenData()) {
                                    MemoriesFileOpenData().apply {
                                      memoriesPathUndefined = e.eventData.memoriesFileOpenData.memoriesPathUndefined
                                    }
                                  } else {
                                    null
                                  }
                                initial_orientation_data =
                                  if (e.eventData.hasInitialOrientationData()) {
                                    val tracing = convertToAgentTracingData(e.eventData.initialOrientationData.tracingData)
                                    InitialOrientationData().apply {
                                      caller = e.eventData.initialOrientationData.caller
                                      flags = tracing?.flags
                                      nums = tracing?.nums
                                      string_stats = tracing?.string_stats
                                      request_ids = tracing?.request_ids
                                    }
                                  } else {
                                    null
                                  }
                                classify_and_distill_data =
                                  convertToAgentTracingData(e.eventData.classifyAndDistillData.tracingData)
                                flush_memories_data = convertToAgentTracingData(e.eventData.flushMemoriesData.tracingData)
                              }
                            } else {
                              null
                            }
                        }
                    }
                }
              }
          },
        )
        Empty.getDefaultInstance()
      }

      // The augmentcode/tools/* methods
      connection!!.onRequest(
        "augmentcode/tools/retrieve-remote-tools",
        RetrieveRemoteToolsRequest.getDefaultInstance().javaClass,
      ) { params ->
        // unpack params into request
        val request = params.unpack(RetrieveRemoteToolsRequest::class.java)
        toolsManager?.retrieveRemoteTools(request) ?: RetrieveRemoteToolsResponse.getDefaultInstance()
      }

      connection!!.onRequest(
        "augmentcode/tools/get-tool-status-for-settings-panel",
        GetToolStatusForSettingsPanelRequest.getDefaultInstance().javaClass,
      ) { params ->
        val request = params.unpack(GetToolStatusForSettingsPanelRequest::class.java)
        toolsManager?.getToolStatusForSettingsPanel(request) ?: GetToolStatusForSettingsPanelResponse.getDefaultInstance()
      }

      connection!!.onRequest("augmentcode/tools/call-remote-tool", CallRemoteToolRequest.getDefaultInstance().javaClass) { params ->
        val call = params.unpack(CallRemoteToolRequest::class.java)
        logger.info("Calling remote tool: $call")

        val response =
          AugmentAPI.instance.runRemoteTool(
            call.requestId,
            call.name,
            call.input,
            call.toolId.toApi(),
            getExtraToolInput(call.toolId.toApi()),
          )

        logger.info("RemoteTools: $response")

        CallRemoteToolResponse.newBuilder()
          .setToolOutput(response.tool_output)
          .setToolResultMessage(response.tool_result_message)
          .setStatus(response.status.value)
          .build()
      }

      connection!!.onRequest("augmentcode/tools/filter-tool-with-extra-input", FilterToolsWithExtraInputRequest::class.java) { params ->
        val request = params.unpack(FilterToolsWithExtraInputRequest::class.java)
        logger.info("Filtering tools with extra input: ${request.toolIdsList}")

        val integrations = AugmentIntegrationsConfig.instance
        val filteredTools =
          request.toolIdsList.filter { protoToolId ->
            val apiToolId = protoToolId.toApi()
            when (apiToolId) {
              RemoteToolId.Jira,
              RemoteToolId.Confluence,
              -> {
                !integrations.atlassianServerUrl.isNullOrEmpty() &&
                  !integrations.atlassianPersonalApiToken.isNullOrEmpty() &&
                  !integrations.atlassianUsername.isNullOrEmpty()
              }
              RemoteToolId.Notion,
              -> {
                !integrations.notionApiToken.isNullOrEmpty()
              }
              RemoteToolId.Linear,
              -> {
                !integrations.linearApiToken.isNullOrEmpty()
              }
              RemoteToolId.GitHubApi -> {
                !integrations.githubApiToken.isNullOrEmpty()
              }
              else -> false
            }
          }

        FilterToolsWithExtraInputResponse.newBuilder()
          .addAllToolIds(filteredTools)
          .build()
      }

      // The augmentcode/client-actions/* methods
      connection!!.onRequest("augmentcode/client-actions/showDiffView", ShowDiffViewRequest.getDefaultInstance().javaClass) { params ->
        val request = params.unpack(ShowDiffViewRequest::class.java)
        logger.info("Showing Diff View Request: $request")
        val virtualFile =
          AugmentBlobStateReader.findFilesByState(project) {
            it.relativePath == request.path.relPath
          }.firstOrNull()
        if (virtualFile != null) {
          val diffFactory = DiffContentFactory.getInstance()
          val originalContent = diffFactory.create(project, request.original, virtualFile.fileType)
          val modifiedContent = diffFactory.create(project, request.modified, virtualFile.fileType)
          val diffRequest =
            SimpleDiffRequest(
              "Review Code Changes",
              originalContent,
              modifiedContent,
              "${virtualFile.name} (Original)",
              "${virtualFile.name} (Augmented)",
            )
          val diffManager = DiffManager.getInstance()
          invokeLater {
            diffManager.showDiff(project, diffRequest)
          }
        }
        Empty.getDefaultInstance()
      }

      val startedSuccessfully =
        try {
          process?.exitValue() // will throw if process is still running
          false
        } catch (e: IllegalThreadStateException) {
          true
        }
      if (startedSuccessfully) {
        logger.info("Sidecar process spawned successfully with pid ${process?.pid()}...")
        val initializeResult =
          connection!!.sendRequest(
            "initialize",
            InitializeParams.newBuilder()
              .setProcessId(process?.pid() ?: 0)
              .setCapabilities(
                Capabilities.newBuilder()
                  .setFeatureFlags(
                    SidecarFlags.newBuilder()
                      .setEnableChatWithTools(featureFlagManager.chatWithToolsEnabled(waitForNetwork = true))
                      .setEnableAgentMode(featureFlagManager.agentModeEnabled(waitForNetwork = true))
                      .setMemoriesParamsJson(featureFlagManager.memoriesParams(waitForNetwork = true))
                      .setAgentEditTool(featureFlagManager.agentEditTool(waitForNetwork = true))
                      .setAgentEditToolMinViewSize(featureFlagManager.agentEditToolMinViewSize(waitForNetwork = true))
                      .setAgentEditToolSchemaType(featureFlagManager.agentEditToolSchemaType(waitForNetwork = true))
                      .setAgentEditToolEnableFuzzyMatching(featureFlagManager.agentEditToolEnableFuzzyMatching(waitForNetwork = true))
                      .setAgentEditToolFuzzyMatchSuccessMessage(
                        featureFlagManager.agentEditToolFuzzyMatchSuccessMessage(waitForNetwork = true),
                      )
                      .setAgentEditToolFuzzyMatchMaxDiff(featureFlagManager.agentEditToolFuzzyMatchMaxDiff(waitForNetwork = true))
                      .setAgentEditToolFuzzyMatchMaxDiffRatio(featureFlagManager.agentEditToolFuzzyMatchMaxDiffRatio(waitForNetwork = true))
                      .setAgentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs(
                        featureFlagManager.agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs(waitForNetwork = true),
                      )
                      .setAgentEditToolInstructionsReminder(featureFlagManager.agentEditToolInstructionsReminder(waitForNetwork = true))
                      .setAgentEditToolShowResultSnippet(featureFlagManager.agentEditToolShowResultSnippet(waitForNetwork = true))
                      .setAgentEditToolMaxLines(
                        featureFlagManager.agentEditToolMaxLines(waitForNetwork = true),
                      )
                      .setAgentSaveFileToolInstructionsReminder(
                        featureFlagManager.agentSaveFileToolInstructionsReminder(waitForNetwork = true),
                      )
                      .setGrepSearchToolEnable(featureFlagManager.grepSearchToolEnable(waitForNetwork = true))
                      .setGrepSearchToolTimelimitSec(featureFlagManager.grepSearchToolTimelimitSec(waitForNetwork = true))
                      .setGrepSearchToolOutputCharsLimit(featureFlagManager.grepSearchToolOutputCharsLimit(waitForNetwork = true))
                      .setGrepSearchToolNumContextLines(featureFlagManager.grepSearchToolNumContextLines(waitForNetwork = true))
                      .build(),
                  )
                  .setInitialState(
                    SidecarInitialState.newBuilder()
                      .setChatMode(initialChatMode)
                      .setMemoriesAbsPath(MemoriesService.getInstance(project).getMemoriesAbsPath())
                      .build(),
                  )
                  .build(),
              )
              .build(),
            InitializeResult::class.java,
          ).await()
        logger.info("Sidecar process initialized successfully: $initializeResult")
        connection!!.sendNotification("initialized", Empty.getDefaultInstance())

        // Load MCP servers from storage
        project.serviceOrNull<SidecarService>()?.setMcpServers(AugmentIntegrationsConfig.instance.mcpServersForSidecar)

        updateState(SidecarState.SIDECAR_RUNNING)
      } else {
        logger.warn("Sidecar failed to start with exit code ${process?.exitValue()}...")
      }
    } catch (e: Exception) {
      logger.warn("Failed to start sidecar", e)
      updateState(SidecarState.SIDECAR_ERROR, "Failed to start sidecar: ${e.message}")
    }
  }

  suspend fun toolExists(name: String): Boolean {
    return getToolState().toolsList.any { it.name == name }
  }

  suspend fun getToolState(): ToolsStateResponse {
    if (process?.isAlive != true) {
      logger.warn("Sidecar process is not alive, cannot get tool state")
      return ToolsStateResponse.getDefaultInstance()
    }
    return toolsManager?.getToolState() ?: ToolsStateResponse.getDefaultInstance()
  }

  suspend fun getToolStatusForSettingsPanel(useCache: Boolean = true): GetToolStatusForSettingsPanelResponse {
    if (process?.isAlive != true) {
      logger.warn("Sidecar process is not alive, cannot get tool status for settings panel")
      return GetToolStatusForSettingsPanelResponse.getDefaultInstance()
    }
    return toolsManager?.getToolStatusForSettingsPanel(
      GetToolStatusForSettingsPanelRequest.newBuilder().setUseCache(useCache).build(),
    ) ?: GetToolStatusForSettingsPanelResponse.getDefaultInstance()
  }

  suspend fun setMcpServers(mcpServers: List<McpServerConfig>) {
    if (process?.isAlive != true) {
      logger.warn("Sidecar process is not alive, cannot set MCP servers")
      return
    }
    toolsManager?.setMcpServers(mcpServers)
  }

  suspend fun checkToolSafety(
    name: String,
    input: Struct,
  ): Boolean {
    if (process?.isAlive != true) {
      logger.warn("Sidecar process is not alive, cannot check tool safety")
      return false
    }
    return toolsManager?.checkToolSafety(name, input) ?: false
  }

  suspend fun callTool(
    name: String,
    requestId: String,
    toolUseId: String,
    input: Struct,
    chatHistory: List<ChatHistoryItem>,
    conversationId: String,
  ): ToolCallResponse? {
    if (process?.isAlive != true) {
      logger.warn("Sidecar process is not alive, cannot call tool")
      return null
    }

    return toolsManager?.callTool(name, requestId, toolUseId, input, chatHistory, conversationId)
  }

  override fun onChange(credentials: AugmentCredentials?) =
    runBlocking {
      // Stop the current process and start a new one if the user has just signed in
      thisLogger().info("Credentials changed, stopping sidecar service (New credentials: ${credentials != null})")
      stopServer()
      if (credentials != null) {
        startServer()
      }
    }

  suspend fun sendWebviewMessage(baseMsgString: String): String? {
    if (process?.isAlive != true) {
      logger.warn("Sidecar process is not alive, cannot send webview message")
      return null
    }

    try {
      // Send the message to the sidecar using the connection
      val responseAny =
        connection!!.sendRequest(
          "augmentcode/webview-message",
          ProcessWebviewMessageRequest.newBuilder().setMessage(baseMsgString).build(),
          ProcessWebviewMessageResponse::class.java,
        ).await()
      val response = responseAny.unpack(ProcessWebviewMessageResponse::class.java)
      return response.message
    } catch (e: Exception) {
      logger.warn("Unhandled webview message: $baseMsgString ${e.message}")
      return null
    }
  }

  override fun dispose() {
    thisLogger().info("Disposing sidecar service")
    stopServer()
  }

  private fun stopServer() {
    updateState(SidecarState.SIDECAR_STOPPING)
    process?.destroy()
    process?.waitFor(5, TimeUnit.SECONDS)
    process?.destroyForcibly()
    process = null
    connection = null
    toolsManager = null
    updateState(SidecarState.SIDECAR_STOPPED)
  }

  private fun getCwd(): String {
    return AugmentRoot.findActiveProjectRoot(project)?.path ?: ""
  }

  suspend fun cancelToolRun(
    requestId: String,
    toolUseId: String,
  ) {
    if (process?.isAlive != true) return

    toolsManager?.cancelToolRun(requestId, toolUseId)
  }

  suspend fun changeChatMode(mode: String) {
    val chatMode = ChatMode.valueOf(mode.uppercase())
    if (process?.isAlive != true) {
      initialChatMode = chatMode
      return
    }

    toolsManager?.changeChatMode(chatMode)
  }

  private fun getExtraToolInput(toolId: RemoteToolId): ExtraToolInput? {
    // It's important we return null if we do not have any local credentials.
    // If the user has OAuth setup for an integration and we return empty credentials, the API
    // call will fail as it tries to use the API token instead of the OAuth token.
    return when (toolId) {
      RemoteToolId.Jira,
      RemoteToolId.Confluence,
      -> {
        if (AugmentIntegrationsConfig.instance.atlassianServerUrl.isNullOrEmpty() ||
          AugmentIntegrationsConfig.instance.atlassianPersonalApiToken.isNullOrEmpty() ||
          AugmentIntegrationsConfig.instance.atlassianUsername.isNullOrEmpty()
        ) {
          return null
        }
        AtlassianToolExtraInput().apply {
          serverUrl = AugmentIntegrationsConfig.instance.atlassianServerUrl
          personalApiToken = AugmentIntegrationsConfig.instance.atlassianPersonalApiToken
          username = AugmentIntegrationsConfig.instance.atlassianUsername
        }
      }
      RemoteToolId.Notion,
      -> {
        if (AugmentIntegrationsConfig.instance.notionApiToken.isNullOrEmpty()) {
          return null
        }
        NotionToolExtraInput().apply {
          apiToken = AugmentIntegrationsConfig.instance.notionApiToken
        }
      }
      RemoteToolId.Linear,
      -> {
        if (AugmentIntegrationsConfig.instance.linearApiToken.isNullOrEmpty()) {
          return null
        }
        LinearToolExtraInput().apply {
          apiToken = AugmentIntegrationsConfig.instance.linearApiToken
        }
      }
      RemoteToolId.GitHubApi -> {
        if (AugmentIntegrationsConfig.instance.githubApiToken.isNullOrEmpty()) {
          return null
        }
        GitHubToolExtraInput().apply {
          apiToken = AugmentIntegrationsConfig.instance.githubApiToken
        }
      }
      else -> null
    }
  }
}

private fun convertToAgentTracingData(data: AgentTracingDataProto?): AgentTracingData? {
  if (data == null) {
    return null
  }
  return AgentTracingData().apply {
    flags =
      data.flagsMap.mapValues {
          (_, value) ->
        Timed<Boolean>().apply {
          this.value = value.value
          this.timestamp = value.timestamp
        }
      }
    nums =
      data.numsMap.mapValues {
          (_, value) ->
        Timed<Int>().apply {
          this.value = value.value.toInt()
          this.timestamp = value.timestamp
        }
      }
    string_stats =
      data.stringStatsMap.mapValues {
          (_, value) ->
        Timed<StringStats>().apply {
          this.value =
            StringStats().apply {
              num_lines = value.value.numLines
              num_chars = value.value.numChars
            }
          this.timestamp = value.timestamp
        }
      }
    request_ids =
      data.requestIdsMap.mapValues {
          (_, value) ->
        Timed<String>().apply {
          this.value = value.value
          this.timestamp = value.timestamp
        }
      }
  }
}
