package com.augmentcode.intellij.status

import com.augmentcode.intellij.AugmentBundle
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.StatusBarWidget
import com.intellij.openapi.wm.StatusBarWidgetFactory
import kotlinx.coroutines.*

class AugmentStatusBarWidgetFactory : StatusBarWidgetFactory {
  override fun getId(): String = "AugmentStatusBarWidget"

  override fun getDisplayName(): String {
    return AugmentBundle.message("augment.statusbar.display.name")
  }

  override fun createWidget(project: Project): StatusBarWidget {
    return AugmentStatusBar(project)
  }
}
