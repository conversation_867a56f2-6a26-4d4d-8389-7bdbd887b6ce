package com.augmentcode.intellij.actions

import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.settings.AugmentSettings
import com.intellij.icons.AllIcons
import com.intellij.openapi.actionSystem.ActionGroup
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.Separator
import com.intellij.openapi.project.Project

/**
 * Hamburger menu for the Augment tool window.
 */
class AugmentHamburgerMenu(private val project: Project) : ActionGroup("Augment", true) {
  init {
    templatePresentation.icon = AllIcons.General.LayoutEditorOnly
  }

  override fun getChildren(e: AnActionEvent?): Array<AnAction> {
    val ctx = PluginStateService.instance.context
    val actions = mutableListOf<AnAction>()

    if (ctx.isSignedIn) {
      // User is signed in
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ToggleCompletionsAction"))
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.OpenSettingsWebviewAction"))

      actions.add(Separator.getInstance())
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ReindexAction"))
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ShowHelpAction"))
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ManageAccountAction"))
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ExtensionStatusAction"))

      if (ctx.flags.enableCompletionsHistory) {
        actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ShowHistoryAction"))
      }

      if (AugmentSettings.debugFeaturesEnabled) {
        actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.GenerateSyncReport"))
      }

      actions.add(Separator.getInstance())
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.SignOutAction"))
    } else {
      // User is signed out
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.SignInAction"))

      actions.add(Separator.getInstance())
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ShowHelpAction"))
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ExtensionStatusAction"))
    }

    return actions.toTypedArray()
  }
}
