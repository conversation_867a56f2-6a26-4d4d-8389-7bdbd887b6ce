package com.augmentcode.intellij.sidecar.clientinterfaces

import com.augmentcode.intellij.idea.AugmentFileStore
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.io.FileUtil
import java.nio.file.Files
import java.nio.file.Path

class PluginFileStore(private val project: Project) {
  fun loadAsset(path: String): ByteArray? {
    val file = getFileStoreDir().resolve(path).toFile()
    return if (file.exists() && file.isFile) {
      file.readBytes()
    } else {
      return null
    }
  }

  fun getAssetAbsPath(path: String): String {
    val fileStoreDir = getFileStoreDir()
    return FileUtil.toSystemDependentName("$fileStoreDir/$path")
  }

  fun saveAsset(
    path: String,
    contents: ByteArray,
  ) {
    val file = getFileStoreDir().resolve(path).toFile()
    file.parentFile.mkdirs()
    file.writeBytes(contents)
  }

  fun deleteAsset(path: String) {
    getFileStoreDir().resolve(path).toFile().delete()
  }

  private fun getFileStoreDir(): Path {
    val pluginDataDir = AugmentFileStore.getSubDirectoryForProject(project, "plugin-file-store")
    Files.createDirectories(pluginDataDir)
    return pluginDataDir
  }
}
