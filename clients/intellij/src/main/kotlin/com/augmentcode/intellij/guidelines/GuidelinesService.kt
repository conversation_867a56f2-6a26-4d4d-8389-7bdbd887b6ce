package com.augmentcode.intellij.guidelines

import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.settings.FeatureFlagManager
import com.augmentcode.intellij.webviews.chat.ChatWebviewMessageBus
import com.augmentcode.rpc.GuidelinesStates
import com.augmentcode.rpc.UpdateGuidelinesStateRequest
import com.augmentcode.rpc.UserGuidelinesState
import com.augmentcode.rpc.WorkspaceGuidelinesState
import com.intellij.ide.util.PropertiesComponent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.readText
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.util.indexing.FileBasedIndex
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.async
import org.jetbrains.annotations.VisibleForTesting
import java.io.File

/**
 * Service for managing user guidelines.
 * Guidelines persist across IDE restarts and upgrades.
 * They provide context to the AI about how to respond to queries.
 */
@Service(Service.Level.PROJECT)
class GuidelinesService(private val project: Project, private val cs: CoroutineScope) {
  companion object {
    /**
     * Gets the GuidelinesService instance for the given project.
     *
     * @param project The project to get the service for
     * @return The GuidelinesService instance
     */
    fun getInstance(project: Project): GuidelinesService {
      return project.service<GuidelinesService>()
    }

    private val logger = thisLogger()

    /**
     * Key for storing user guidelines in application properties.
     */
    const val USER_GUIDELINES_KEY = "augment.user.guidelines"

    /**
     * Default value for user guidelines.
     */
    const val DEFAULT_USER_GUIDELINES = ""

    /**
     * Path for storing workspace guidelines asset.
     */

    const val WORKSPACE_GUIDELINES_ASSET_PATH = ".augment-guidelines"
  }

  private val propertiesComponent = PropertiesComponent.getInstance()
  private val featureFlagManager = FeatureFlagManager.instance

  /**
   * Enum representing the different types of guidelines.
   */
  enum class GuidelineType {
    USER,
    WORKSPACE,
    ;

    override fun toString(): String {
      return name.lowercase()
    }
  }

  /**
   * Helper method to limit content length based on the specified limit.
   *
   * @param content The content to limit
   * @param lengthLimit The maximum allowed length
   * @param guidelineType The type of guideline (for logging purposes)
   * @return The content, truncated if necessary
   */
  private fun limitContentLength(
    content: String,
    lengthLimit: Int,
    guidelineType: GuidelineType,
  ): String {
    return if (content.length > lengthLimit) {
      logger.warn("Truncating $guidelineType guidelines content to length limit ($lengthLimit characters)")
      content.substring(0, lengthLimit)
    } else {
      content
    }
  }

  fun getUserGuidelines(): String = propertiesComponent.getValue(USER_GUIDELINES_KEY, DEFAULT_USER_GUIDELINES)

  fun getWorkspaceGuidelines(): String {
    if (DumbService.getInstance(project).isDumb) {
      // just being defensive here. It's not end of the world if we miss a guidelines.
      logger.info("Project is in dumb, skipping workspace guidelines")
      return ""
    }
    val files =
      runReadAction {
        FileBasedIndex.getInstance()
          .getContainingFiles(AUGMENT_WORKSPACE_GUIDELINES_INDEX_NAME, true, GlobalSearchScope.projectScope(project))
      }
    if (files.isEmpty()) {
      logger.info("No workspace guidelines files found")
      return ""
    }
    files.forEach {
      logger.info("Found workspace guidelines file: ${it.path}")
    }
    val guidelinesFile = files.first()
    return if (guidelinesFile.exists()) {
      val content =
        runReadAction {
          guidelinesFile.readText()
        }
      if (content.length > featureFlagManager.getWorkspaceGuidelinesLengthLimit()) {
        logger.warn(
          "Workspace guidelines exceed length limit " +
            "(${content.length} > ${featureFlagManager.getWorkspaceGuidelinesLengthLimit()}) and will be truncated.",
        )
      }
      limitContentLength(content, featureFlagManager.getWorkspaceGuidelinesLengthLimit(), GuidelineType.WORKSPACE)
    } else {
      ""
    }
  }

  /**
   * Creates and returns the GuidelinesStates object based on the current user guidelines,
   * workspace guidelines, and feature flag settings.
   *
   * @return The GuidelinesStates object containing the current state of user and workspace guidelines
   */
  fun getGuidelinesStates(): GuidelinesStates {
    // Create the builder for guidelines states
    val guidelinesStatesBuilder = GuidelinesStates.newBuilder()

    // Add user guidelines state
    guidelinesStatesBuilder.setUserGuidelines(createUserGuidelinesState())

    // Add workspace guidelines states if guidelines are enabled
    if (featureFlagManager.guidelinesEnabled()) {
      val workspaceGuidelines = getWorkspaceGuidelines()
      val rootDir = AugmentRoot.findActiveProjectRoot(project)
      if (workspaceGuidelines.isNotEmpty() && rootDir != null) {
        // Get the root directory to use as the workspace folder path

        guidelinesStatesBuilder.addAllWorkspaceGuidelines(
          createWorkspaceGuidelinesStates(workspaceGuidelines, rootDir.path),
        )
      }
    }

    // Return the built guidelines states
    return guidelinesStatesBuilder.build()
  }

  /**
   * Creates the UserGuidelinesState object based on the current user guidelines
   * and feature flag settings.
   *
   * @return The UserGuidelinesState object
   */
  private fun createUserGuidelinesState(): UserGuidelinesState {
    val userGuidelines = getUserGuidelines()

    return UserGuidelinesState.newBuilder()
      .setEnabled(userGuidelines.isNotEmpty())
      .setOverLimit(userGuidelines.length > featureFlagManager.getUserGuidelinesLengthLimit())
      .setContents(userGuidelines)
      .build()
  }

  /**
   * Creates WorkspaceGuidelinesState objects for the workspace guidelines.
   *
   * @param workspaceGuidelinesContent The content of the workspace guidelines
   * @param workspaceFolder The folder path of the workspace (optional)
   * @return List of WorkspaceGuidelinesState objects
   */
  private fun createWorkspaceGuidelinesStates(
    workspaceGuidelinesContent: String,
    workspaceFolder: String = WORKSPACE_GUIDELINES_ASSET_PATH,
  ): List<WorkspaceGuidelinesState> {
    logger.info("Creating workspace guidelines states with content length: ${workspaceGuidelinesContent.length}")

    if (workspaceGuidelinesContent.isNotEmpty()) {
      val lengthLimit = featureFlagManager.getWorkspaceGuidelinesLengthLimit()
      val isOverLimit = workspaceGuidelinesContent.length > lengthLimit

      if (isOverLimit) {
        logger.warn("Workspace guidelines exceed length limit (${workspaceGuidelinesContent.length} > $lengthLimit)")
      }

      return listOf(
        WorkspaceGuidelinesState.newBuilder()
          .setEnabled(featureFlagManager.guidelinesEnabled())
          .setOverLimit(isOverLimit)
          .setWorkspaceFolder(workspaceFolder)
          .build(),
      )
    }

    return emptyList()
  }

  /**
   * Updates the user guidelines with the provided content.
   * Stores the content as a persistent application setting.
   * Sends a notification to the webview about the updated guidelines.
   *
   * @param content The new guidelines content
   */
  fun updateUserGuidelines(content: String) {
    try {
      // Store the guidelines content in application properties
      propertiesComponent.setValue(USER_GUIDELINES_KEY, content)

      // Update the guidelines state in the webview
      val workspaceFolder = AugmentRoot.findActiveProjectRoot(project)?.path
      if (workspaceFolder == null) {
        logger.info("Updating webview without workspace folder")
      }
      notifyWebviewAboutGuidelinesUpdate(getWorkspaceGuidelines(), workspaceFolder ?: "")

      logger.info("Updated user guidelines and notified webview")
    } catch (e: Exception) {
      logger.error("Failed to update user guidelines: ${e.message}", e)
    }
  }

  /**
   * Notifies the webview about guidelines updates for the given project.
   * This sends both user and workspace guidelines to the webview.
   *
   * @param workspaceGuidelinesContent The content of the workspace guidelines
   * @param workspaceFolder The folder path of the workspace (optional)
   */
  fun notifyWebviewAboutGuidelinesUpdate(
    workspaceGuidelinesContent: String,
    workspaceFolder: String = WORKSPACE_GUIDELINES_ASSET_PATH,
  ) {
    try {
      // Send the update to the webview
      val publisher: ChatWebviewMessageBus = ChatWebviewMessageBus.syncPublisher(project)

      // Create the builder for guidelines states
      val guidelinesStatesBuilder = GuidelinesStates.newBuilder()

      // Add user guidelines state
      guidelinesStatesBuilder.setUserGuidelines(createUserGuidelinesState())

      // Add workspace guidelines states if guidelines are enabled
      if (featureFlagManager.guidelinesEnabled() && workspaceGuidelinesContent.isNotEmpty()) {
        guidelinesStatesBuilder.addAllWorkspaceGuidelines(
          createWorkspaceGuidelinesStates(workspaceGuidelinesContent, workspaceFolder),
        )
      }

      // Create and send the update request
      val request =
        UpdateGuidelinesStateRequest
          .newBuilder()
          .setData(guidelinesStatesBuilder.build())
          .build()

      publisher.postMessageToWebview(request)

      logger.info("Sent guidelines update to webview with workspace folder: $workspaceFolder")
    } catch (e: Exception) {
      logger.error("Failed to update guidelines in webview: ${e.message}", e)
    }
  }

  /**
   * Opens the workspace guidelines file for editing.
   * If the file doesn't exist, it will be created.
   *
   * This method uses AugmentRoot.guessCurrentRoot to determine which workspace root to use.
   * For multi-root projects, consider using openWorkspaceGuidelines(project, root) instead.
   *
   * @see openWorkspaceGuidelines(project, root)
   */
  fun openWorkspaceGuidelines() {
    try {
      // Use AugmentRoot to handle multi-root project setups
      val rootDir = AugmentRoot.findActiveProjectRoot(project)
      if (rootDir == null) {
        logger.warn("Cannot open workspace guidelines: unable to determine project root")
        return
      }

      // Delegate to the method that accepts a specific root
      openWorkspaceGuidelines(project, rootDir)
    } catch (e: Exception) {
      logger.error("Failed to open workspace guidelines: ${e.message}", e)
    }
  }

  /**
   * Opens the workspace guidelines file for editing for a specific workspace root.
   * If the file doesn't exist, it will be created.
   *
   * This method is useful for multi-root projects where you want to specify which
   * workspace root to use.
   *
   * @param project The project to open guidelines for
   * @param rootDir The specific workspace root directory to use
   */
  fun openWorkspaceGuidelines(
    project: Project,
    rootDir: VirtualFile,
  ) {
    try {
      val virtualFile =
        rootDir.findChild(WORKSPACE_GUIDELINES_ASSET_PATH) ?: runWriteAction {
          rootDir.createChildData(this, WORKSPACE_GUIDELINES_ASSET_PATH)
        }
      openFileInEditor(project, virtualFile)
    } catch (e: Exception) {
      logger.error("Failed to open workspace guidelines for root ${rootDir.path}: ${e.message}", e)
    }
  }

  /**
   * Check if a file exists.
   */
  @VisibleForTesting
  internal fun fileExists(file: File): Boolean = file.exists()

  /**
   * Create a new file.
   */
  @VisibleForTesting
  internal fun createFile(file: File): Boolean = file.createNewFile()

  /**
   * Open a file in the editor.
   */
  @VisibleForTesting
  internal fun openFileInEditor(
    project: Project,
    virtualFile: VirtualFile,
  ) {
    ApplicationManager.getApplication().invokeLater {
      FileEditorManager.getInstance(project).openFile(virtualFile, true)
    }
  }

  /**
   * This method is called when workspace guidelines are updated for a project.
   * The method is wrapped in a coroutine to ensure thread safety when called from
   * AugmentWorkspaceGuidelinesIndexer.map() which holds a read lock.
   *
   * @param content The content of the updated guidelines
   * @param workspaceFolder The folder path of the workspace (optional)
   */
  fun guidelinesUpdated(
    content: String,
    workspaceFolder: String,
  ) {
    // Wrap in async to avoid blocking when called from a context that holds a read lock
    cs.async {
      try {
        logger.info("Guidelines updated for project ${project.name} in folder $workspaceFolder")
        // Send the update to the webview
        notifyWebviewAboutGuidelinesUpdate(content, workspaceFolder)
      } catch (e: Exception) {
        logger.error("Failed to handle guidelines update notification: ${e.message}", e)
      }
    }
  }
}
