package com.augmentcode.intellij.sidecar.clientinterfaces

import com.augmentcode.intellij.testutils.AugmentHeavyPlatformTestCase
import com.intellij.openapi.util.io.FileUtil
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.testFramework.PsiTestUtil
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.nio.file.Paths

@RunWith(JUnit4::class)
class ClientWorkspacesTest : AugmentHeavyPlatformTestCase() {
  override fun tearDown() {
    PsiTestUtil.removeAllRoots(module, null)
    super.tearDown()
  }

  @Test
  fun testReadAbsolutePathFileFromProjectRoot() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a temporary file
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val file = tempDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(file.absolutePath)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadRelativePathFileFromProjectRoot() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a temporary file
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val file = tempDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(wantFileName)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadAbsolutePathFileFromProjectSubDirectory() {
    val wantContent = "test content"
    val wantFileName = "example/subdir/file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(file.absolutePath)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadRelativePathFileFromProjectSubDirectory() {
    val wantContent = "test content"
    val wantFileName = "example/subdir/file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(wantFileName)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadAbsolutePathFileFromGitRoot() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Make git file
    val gitFile = tempDir.resolve(".git/example")
    gitFile.parentFile.mkdirs()
    gitFile.writeText("git file")

    // Make file we want to read
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(file.absolutePath)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDir.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadRelativePathFileFromGitRoot() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Make git file
    val gitFile = tempDir.resolve(".git/example")
    gitFile.parentFile.mkdirs()
    gitFile.writeText("git file")

    // Make file we want to read
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Since the file is nested in a subdirectory, not the root of the workspace, we can't find it
    val result = ClientWorkspaces(project).readFile(wantFileName)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDir.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadAbsolutePathFileFromGitRootSubDirectory() {
    val wantContent = "test content"
    val wantFileName = "example/subdir/file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Make git file
    val gitFile = tempDir.resolve(".git/example")
    gitFile.parentFile.mkdirs()
    gitFile.writeText("git file")

    // Make file we want to read
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(file.absolutePath)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDir.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadRelativePathFileFromGitRootSubDirectory() {
    val wantContent = "test content"
    val wantFileName = "example/subdir/file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Make git file
    val gitFile = tempDir.resolve(".git/example")
    gitFile.parentFile.mkdirs()
    gitFile.writeText("git file")

    // Make file we want to read
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(wantFileName)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDir.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadAbsolutePathFileFromGitRootOutsideProjectRoot() {
    val wantContent = "test content"
    val wantFileName = "example/subdir/file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Make git file
    val gitFile = tempDir.resolve(".git/example")
    gitFile.parentFile.mkdirs()
    gitFile.writeText("git file")

    // Make file we want to read
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)

    // IMPORTANT: We register the subdirectory as the content root, not the root of the temp dir.
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!.findChild("example")!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(file.absolutePath)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDir.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadRelativePathFileFromGitRootOutsideProjectRoot() {
    val wantContent = "test content"
    val wantFileName = "subdir/file.txt"
    val workspaceSubDirectory = "example"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Make git file
    val gitFile = tempDir.resolve(".git/example")
    gitFile.parentFile.mkdirs()
    gitFile.writeText("git file")

    // Make file we want to read
    val file = tempDir.resolve(Paths.get(workspaceSubDirectory, wantFileName).toString())
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)

    // IMPORTANT: We register the subdirectory as the content root, not the root of the temp dir.
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!.findChild(workspaceSubDirectory)!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(wantFileName)
    assertNotNull(result.qualifiedPath)
    assertEquals(Paths.get(workspaceSubDirectory, wantFileName).toString(), result.qualifiedPath?.relPath)
    assertEquals(tempDir.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testListDirectoryBasic() {
    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create some files and directories
    tempDir.resolve("file1.txt").writeText("content1")
    tempDir.resolve("file2.txt").writeText("content2")
    tempDir.resolve("subdir1").mkdir()
    tempDir.resolve("subdir1/nested.txt").writeText("nested content")
    tempDir.resolve("subdir2").mkdir()
    tempDir.resolve("subdir2/another.txt").writeText("another content")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the directory
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNull("Should not have error message", result.errorMessage)
    assertTrue("Should have entries", result.entries.isNotEmpty())

    // Check that we have the expected files and directories
    val expectedEntries = setOf("file1.txt", "file2.txt", "subdir1", "subdir2", "subdir1/nested.txt", "subdir2/another.txt")
    assertEquals("Should have all expected entries", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testListDirectoryWithDepthLimit() {
    // Create a temporary directory structure with multiple levels
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create nested structure: root -> level1 -> level2 -> level3
    tempDir.resolve("root_file.txt").writeText("root content")
    tempDir.resolve("level1").mkdir()
    tempDir.resolve("level1/level1_file.txt").writeText("level1 content")
    tempDir.resolve("level1/level2").mkdir()
    tempDir.resolve("level1/level2/level2_file.txt").writeText("level2 content")
    tempDir.resolve("level1/level2/level3").mkdir()
    tempDir.resolve("level1/level2/level3/level3_file.txt").writeText("level3 content")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test with depth 2 (should only see root level and one level down)
    val result1 = ClientWorkspaces(project).listDirectory(tempDir.absolutePath, depth = 2)
    assertNull("Should not have error message", result1.errorMessage)
    val expectedDepth1 = setOf("root_file.txt", "level1", "level1/level1_file.txt", "level1/level2")
    assertEquals("Should respect depth limit of 2", expectedDepth1, result1.entries.toSet())

    // Test with depth 1 (should only see root level)
    val result0 = ClientWorkspaces(project).listDirectory(tempDir.absolutePath, depth = 1)
    assertNull("Should not have error message", result0.errorMessage)
    val expectedDepth0 = setOf("root_file.txt", "level1")
    assertEquals("Should respect depth limit of 1", expectedDepth0, result0.entries.toSet())
  }

  @Test
  fun testListDirectoryWithHiddenFiles() {
    // Create a temporary directory structure with hidden files
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create regular and hidden files
    tempDir.resolve("visible.txt").writeText("visible content")
    tempDir.resolve(".hidden.txt").writeText("hidden content")
    tempDir.resolve(".hiddendir").mkdir()
    tempDir.resolve(".hiddendir/nested.txt").writeText("nested in hidden")
    tempDir.resolve("normaldir").mkdir()
    tempDir.resolve("normaldir/.hidden_nested.txt").writeText("hidden nested")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test without showing hidden files (default)
    val resultNoHidden = ClientWorkspaces(project).listDirectory(tempDir.absolutePath, showHidden = false)
    assertNull("Should not have error message", resultNoHidden.errorMessage)
    val expectedNoHidden = setOf("visible.txt", "normaldir")
    assertEquals("Should not include hidden files", expectedNoHidden, resultNoHidden.entries.toSet())

    // Test with showing hidden files
    val resultWithHidden = ClientWorkspaces(project).listDirectory(tempDir.absolutePath, showHidden = true)
    assertNull("Should not have error message", resultWithHidden.errorMessage)
    val expectedWithHidden =
      setOf(
        "visible.txt",
        ".hidden.txt",
        ".hiddendir",
        "normaldir",
        ".hiddendir/nested.txt",
        "normaldir/.hidden_nested.txt",
      )
    assertEquals("Should include hidden files", expectedWithHidden, resultWithHidden.entries.toSet())
  }

  @Test
  fun testListDirectoryRelativePath() {
    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create some files
    tempDir.resolve("file1.txt").writeText("content1")
    tempDir.resolve("subdir").mkdir()
    tempDir.resolve("subdir/file2.txt").writeText("content2")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing with relative path (empty string should list the root)
    val result = ClientWorkspaces(project).listDirectory("")
    assertNull("Should not have error message", result.errorMessage)
    assertTrue("Should have entries", result.entries.isNotEmpty())

    val expectedEntries = setOf("file1.txt", "subdir", "subdir/file2.txt")
    assertEquals("Should have all expected entries", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testListDirectoryNonExistentPath() {
    // Test listing a directory that doesn't exist
    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create some files
    tempDir.resolve("file1.txt").writeText("content1")
    tempDir.resolve("subdir").mkdir()
    tempDir.resolve("subdir/file2.txt").writeText("content2")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    val result = ClientWorkspaces(project).listDirectory("non-existent/path")
    assertNotNull("Should have error message", result.errorMessage)
    assertEquals("Should have specific error message", "Directory not found", result.errorMessage)
    assertTrue("Should have empty entries", result.entries.isEmpty())
  }

  @Test
  fun testListDirectoryFileInsteadOfDirectory() {
    // Create a temporary file (not directory)
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val file = tempDir.resolve("file.txt")
    file.writeText("content")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing a file instead of directory
    val result = ClientWorkspaces(project).listDirectory(file.absolutePath)
    assertNotNull("Should have error message", result.errorMessage)
    assertEquals("Should have specific error message", "Path is not a directory", result.errorMessage)
    assertTrue("Should have empty entries", result.entries.isEmpty())
  }

  @Test
  fun testListDirectoryOutsideWorkspace() {
    // Create a temporary directory that's not registered as a content root
    val tempDir = FileUtil.createTempDirectory("client-workspaces-outside", null)
    tempDir.resolve("file.txt").writeText("content")

    // Don't register this directory with the project - it should be outside workspace

    // Test listing directory outside workspace
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNotNull("Should have error message", result.errorMessage)
    assertEquals("Should have specific error message", "Path is outside the workspace", result.errorMessage)
    assertTrue("Should have empty entries", result.entries.isEmpty())
  }

  @Test
  fun testListDirectoryEmptyDirectory() {
    // Create an empty temporary directory
    val tempDir = FileUtil.createTempDirectory("client-workspaces-empty", null)

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing empty directory
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNull("Should not have error message", result.errorMessage)
    assertTrue("Should have empty entries", result.entries.isEmpty())
  }

  @Test
  fun testListDirectorySubdirectoryPath() {
    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create subdirectory with files
    tempDir.resolve("subdir").mkdir()
    tempDir.resolve("subdir/file1.txt").writeText("content1")
    tempDir.resolve("subdir/file2.txt").writeText("content2")
    tempDir.resolve("subdir/nested").mkdir()
    tempDir.resolve("subdir/nested/deep.txt").writeText("deep content")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the subdirectory
    val subdirPath = tempDir.resolve("subdir").absolutePath
    val result = ClientWorkspaces(project).listDirectory(subdirPath)
    assertNull("Should not have error message", result.errorMessage)

    val expectedEntries = setOf("file1.txt", "file2.txt", "nested", "nested/deep.txt")
    assertEquals("Should have all expected entries", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testListDirectorySortedOutput() {
    // Create a temporary directory with files in non-alphabetical order
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create files in a specific order to test sorting
    tempDir.resolve("zebra.txt").writeText("z")
    tempDir.resolve("alpha.txt").writeText("a")
    tempDir.resolve("beta").mkdir()
    tempDir.resolve("beta/gamma.txt").writeText("g")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the directory
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNull("Should not have error message", result.errorMessage)

    // Check that results are sorted
    val expectedSorted = listOf("alpha.txt", "beta", "beta/gamma.txt", "zebra.txt")
    assertEquals("Should have sorted entries", expectedSorted, result.entries)
  }

  @Test
  fun testListDirectoryWithSymlinks() {
    // Create a temporary directory structure with symlinks
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create regular files and directories
    tempDir.resolve("regular.txt").writeText("regular content")
    tempDir.resolve("targetdir").mkdir()
    tempDir.resolve("targetdir/target.txt").writeText("target content")

    // Create symlinks (using Java NIO for cross-platform compatibility)
    val symlinkFile = tempDir.resolve("symlink_to_file.txt").toPath()
    val symlinkDir = tempDir.resolve("symlink_to_dir").toPath()

    try {
      // Create symlink to file
      java.nio.file.Files.createSymbolicLink(symlinkFile, tempDir.resolve("regular.txt").toPath())

      // Create symlink to directory
      java.nio.file.Files.createSymbolicLink(symlinkDir, tempDir.resolve("targetdir").toPath())
    } catch (e: Exception) {
      // Skip test if symlinks are not supported on this platform
      println("Skipping symlink test - symlinks not supported: ${e.message}")
      return
    }

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the directory - symlinks should be skipped
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNull("Should not have error message", result.errorMessage)

    // Should only include regular files and directories, not symlinks
    val expectedEntries = setOf("regular.txt", "targetdir", "targetdir/target.txt")
    assertEquals("Should skip symlinks", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testListDirectoryWithRecursiveSymlinks() {
    // Create a temporary directory structure with recursive symlinks
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create a directory structure
    tempDir.resolve("dir1").mkdir()
    tempDir.resolve("dir1/file1.txt").writeText("file1 content")
    tempDir.resolve("dir1/dir2").mkdir()
    tempDir.resolve("dir1/dir2/file2.txt").writeText("file2 content")

    // Create recursive symlinks
    val symlinkToParent = tempDir.resolve("dir1/dir2/link_to_parent").toPath()
    val symlinkToRoot = tempDir.resolve("dir1/link_to_root").toPath()

    try {
      // Create symlink from dir2 back to dir1 (parent)
      java.nio.file.Files.createSymbolicLink(symlinkToParent, tempDir.resolve("dir1").toPath())

      // Create symlink from dir1 back to root
      java.nio.file.Files.createSymbolicLink(symlinkToRoot, tempDir.toPath())
    } catch (e: Exception) {
      // Skip test if symlinks are not supported on this platform
      println("Skipping recursive symlink test - symlinks not supported: ${e.message}")
      return
    }

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the directory - should not follow recursive symlinks
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath, depth = 3)
    assertNull("Should not have error message", result.errorMessage)

    // Should only include regular files and directories, not symlinks
    // This prevents infinite recursion
    val expectedEntries = setOf("dir1", "dir1/file1.txt", "dir1/dir2", "dir1/dir2/file2.txt")
    assertEquals("Should skip recursive symlinks", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testListDirectoryWithCircularSymlinks() {
    // Create a temporary directory structure with circular symlinks
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create directories
    tempDir.resolve("dirA").mkdir()
    tempDir.resolve("dirB").mkdir()
    tempDir.resolve("dirA/fileA.txt").writeText("fileA content")
    tempDir.resolve("dirB/fileB.txt").writeText("fileB content")

    // Create circular symlinks: dirA -> dirB, dirB -> dirA
    val symlinkAtoB = tempDir.resolve("dirA/link_to_B").toPath()
    val symlinkBtoA = tempDir.resolve("dirB/link_to_A").toPath()

    try {
      java.nio.file.Files.createSymbolicLink(symlinkAtoB, tempDir.resolve("dirB").toPath())
      java.nio.file.Files.createSymbolicLink(symlinkBtoA, tempDir.resolve("dirA").toPath())
    } catch (e: Exception) {
      // Skip test if symlinks are not supported on this platform
      println("Skipping circular symlink test - symlinks not supported: ${e.message}")
      return
    }

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the directory - should not follow circular symlinks
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNull("Should not have error message", result.errorMessage)

    // Should only include regular files and directories, not symlinks
    // This prevents infinite loops
    val expectedEntries = setOf("dirA", "dirB", "dirA/fileA.txt", "dirB/fileB.txt")
    assertEquals("Should skip circular symlinks", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testListDirectoryWithBrokenSymlinks() {
    // Create a temporary directory structure with broken symlinks
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create regular files
    tempDir.resolve("regular.txt").writeText("regular content")

    // Create broken symlinks (pointing to non-existent targets)
    val brokenSymlinkFile = tempDir.resolve("broken_link_file").toPath()
    val brokenSymlinkDir = tempDir.resolve("broken_link_dir").toPath()

    try {
      // Create symlinks to non-existent targets
      java.nio.file.Files.createSymbolicLink(brokenSymlinkFile, tempDir.resolve("nonexistent.txt").toPath())
      java.nio.file.Files.createSymbolicLink(brokenSymlinkDir, tempDir.resolve("nonexistent_dir").toPath())
    } catch (e: Exception) {
      // Skip test if symlinks are not supported on this platform
      println("Skipping broken symlink test - symlinks not supported: ${e.message}")
      return
    }

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the directory - should skip broken symlinks
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNull("Should not have error message", result.errorMessage)

    // Should only include regular files, not broken symlinks
    val expectedEntries = setOf("regular.txt")
    assertEquals("Should skip broken symlinks", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testSearchForRelativeFileWithOverlappingPath() {
    val wantContent = "test content"
    val wantFileName = "main.py"

    // Create a temporary directory structure that simulates the scenario:
    // root = /tmp/augment/clients
    // path = clients/main.py
    // should find file at /tmp/augment/clients/main.py
    val tempDir = FileUtil.createTempDirectory("augment", null)
    val clientsDir = tempDir.resolve("clients")
    clientsDir.mkdir()
    val file = clientsDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the clients directory as the content root
    val clientsDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(clientsDir)
    assertNotNull("Failed to find virtual file for clients directory", clientsDirVirtualFile)
    PsiTestUtil.addContentRoot(module, clientsDirVirtualFile!!)

    // Test reading the file using overlapping path "clients/main.py"
    // This should detect that "clients" overlaps with the root path and use "main.py" as the relative path
    val result = ClientWorkspaces(project).readFile("clients/$wantFileName")
    assertNotNull("Should find file with overlapping path", result.qualifiedPath)
    assertEquals("Should have correct relative path", wantFileName, result.qualifiedPath?.relPath)
    assertEquals("Should have correct root path", clientsDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithMultipleOverlappingSegments() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a nested directory structure:
    // root = /tmp/project/src/main
    // path = src/main/file.txt
    // should find file at /tmp/project/src/main/file.txt
    val tempDir = FileUtil.createTempDirectory("project", null)
    val srcDir = tempDir.resolve("src")
    srcDir.mkdir()
    val mainDir = srcDir.resolve("main")
    mainDir.mkdir()
    val file = mainDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the main directory as the content root
    val mainDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(mainDir)
    assertNotNull("Failed to find virtual file for main directory", mainDirVirtualFile)
    PsiTestUtil.addContentRoot(module, mainDirVirtualFile!!)

    // Test reading the file using overlapping path "src/main/file.txt"
    // This should detect that "src/main" overlaps with the root path and use "file.txt" as the relative path
    val result = ClientWorkspaces(project).readFile("src/main/$wantFileName")
    assertNotNull("Should find file with multiple overlapping segments", result.qualifiedPath)
    assertEquals("Should have correct relative path", wantFileName, result.qualifiedPath?.relPath)
    assertEquals("Should have correct root path", mainDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithPartialOverlap() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a directory structure where only part of the path overlaps:
    // root = /tmp/myproject/src
    // path = project/src/file.txt (only "src" overlaps)
    // should NOT find the file because "project" doesn't match "myproject"
    val tempDir = FileUtil.createTempDirectory("myproject", null)
    val srcDir = tempDir.resolve("src")
    srcDir.mkdir()
    val file = srcDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the src directory as the content root
    val srcDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(srcDir)
    assertNotNull("Failed to find virtual file for src directory", srcDirVirtualFile)
    PsiTestUtil.addContentRoot(module, srcDirVirtualFile!!)

    // Test reading the file using non-overlapping path "project/src/file.txt"
    // This should NOT find the file because "project" doesn't match "myproject"
    val result = ClientWorkspaces(project).readFile("project/src/$wantFileName")
    assertNull("Should not find file with partial overlap", result.qualifiedPath)
    assertNull("Should not have content", result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithExactOverlap() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a directory structure where the path exactly matches the root name:
    // root = /tmp/workspace
    // path = workspace/file.txt
    // should find file at /tmp/workspace/file.txt
    val tempDir = FileUtil.createTempDirectory("workspace", null)
    val file = tempDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the workspace directory as the content root
    val workspaceDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for workspace directory", workspaceDirVirtualFile)
    PsiTestUtil.addContentRoot(module, workspaceDirVirtualFile!!)

    // Test reading the file using overlapping path "workspace/file.txt"
    // This should detect that "workspace" overlaps with the root path and use "file.txt" as the relative path
    val result = ClientWorkspaces(project).readFile("workspace/$wantFileName")
    assertNotNull("Should find file with exact overlap", result.qualifiedPath)
    assertEquals("Should have correct relative path", wantFileName, result.qualifiedPath?.relPath)
    assertEquals("Should have correct root path", workspaceDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithNoOverlap() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a directory structure with no overlap:
    // root = /tmp/workspace
    // path = different/file.txt (no overlap)
    // should NOT find the file
    val tempDir = FileUtil.createTempDirectory("workspace", null)
    val file = tempDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the workspace directory as the content root
    val workspaceDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for workspace directory", workspaceDirVirtualFile)
    PsiTestUtil.addContentRoot(module, workspaceDirVirtualFile!!)

    // Test reading the file using non-overlapping path "different/file.txt"
    // This should NOT find the file
    val result = ClientWorkspaces(project).readFile("different/$wantFileName")
    assertNull("Should not find file with no overlap", result.qualifiedPath)
    assertNull("Should not have content", result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithNestedOverlap() {
    val wantContent = "test content"
    val wantFileName = "nested.txt"

    // Create a nested directory structure:
    // root = /tmp/project/src/main/java
    // path = main/java/com/example/nested.txt
    // should find file at /tmp/project/src/main/java/com/example/nested.txt
    val tempDir = FileUtil.createTempDirectory("project", null)
    val srcDir = tempDir.resolve("src")
    srcDir.mkdir()
    val mainDir = srcDir.resolve("main")
    mainDir.mkdir()
    val javaDir = mainDir.resolve("java")
    javaDir.mkdir()
    val comDir = javaDir.resolve("com")
    comDir.mkdir()
    val exampleDir = comDir.resolve("example")
    exampleDir.mkdir()
    val file = exampleDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the java directory as the content root
    val javaDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(javaDir)
    assertNotNull("Failed to find virtual file for java directory", javaDirVirtualFile)
    PsiTestUtil.addContentRoot(module, javaDirVirtualFile!!)

    // Test reading the file using overlapping path "main/java/com/example/nested.txt"
    // This should detect that "main/java" overlaps with the root path and use "com/example/nested.txt" as the relative path
    val result = ClientWorkspaces(project).readFile("main/java/com/example/$wantFileName")
    assertNotNull("Should find file with nested overlap", result.qualifiedPath)
    assertEquals("Should have correct relative path", "com/example/$wantFileName", result.qualifiedPath?.relPath)
    assertEquals("Should have correct root path", javaDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testGetPathInfoWithOverlappingPath() {
    // Create a temporary directory structure that simulates the scenario:
    // root = /tmp/workspace/src
    // path = src/file.txt
    // should find file at /tmp/workspace/src/file.txt
    val tempDir = FileUtil.createTempDirectory("workspace", null)
    val srcDir = tempDir.resolve("src")
    srcDir.mkdir()
    val file = srcDir.resolve("file.txt")
    file.writeText("test content")

    // Register the src directory as the content root
    val srcDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(srcDir)
    assertNotNull("Failed to find virtual file for src directory", srcDirVirtualFile)
    PsiTestUtil.addContentRoot(module, srcDirVirtualFile!!)

    // Test getting path info using overlapping path "src/file.txt"
    // This should detect that "src" overlaps with the root path and use "file.txt" as the relative path
    val result = ClientWorkspaces(project).getPathInfo("src/file.txt")
    assertEquals("Should exist", true, result.exists)
    assertEquals("Should be a file", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.FILE, result.type)
    assertNotNull("Should have qualified path", result.filepath)
    assertEquals("Should have correct relative path", "file.txt", result.filepath?.relPath)
    assertEquals("Should have correct root path", srcDirVirtualFile.path, result.filepath?.rootPath)
  }

  @Test
  fun testGetPathInfoWithMultipleOverlappingSegments() {
    // Create a nested directory structure:
    // root = /tmp/project/src/main/resources
    // path = src/main/resources/config.properties
    // should find file at /tmp/project/src/main/resources/config.properties
    val tempDir = FileUtil.createTempDirectory("project", null)
    val srcDir = tempDir.resolve("src")
    srcDir.mkdir()
    val mainDir = srcDir.resolve("main")
    mainDir.mkdir()
    val resourcesDir = mainDir.resolve("resources")
    resourcesDir.mkdir()
    val file = resourcesDir.resolve("config.properties")
    file.writeText("key=value")

    // Register the resources directory as the content root
    val resourcesDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(resourcesDir)
    assertNotNull("Failed to find virtual file for resources directory", resourcesDirVirtualFile)
    PsiTestUtil.addContentRoot(module, resourcesDirVirtualFile!!)

    // Test getting path info using overlapping path "src/main/resources/config.properties"
    // This should detect that "src/main/resources" overlaps with the root path and use "config.properties" as the relative path
    val result = ClientWorkspaces(project).getPathInfo("src/main/resources/config.properties")
    assertEquals("Should exist", true, result.exists)
    assertEquals("Should be a file", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.FILE, result.type)
    assertNotNull("Should have qualified path", result.filepath)
    assertEquals("Should have correct relative path", "config.properties", result.filepath?.relPath)
    assertEquals("Should have correct root path", resourcesDirVirtualFile.path, result.filepath?.rootPath)
  }

  @Test
  fun testGetPathInfoWithOverlappingDirectoryPath() {
    // Create a directory structure:
    // root = /tmp/workspace/docs
    // path = docs/api
    // should find directory at /tmp/workspace/docs/api
    val tempDir = FileUtil.createTempDirectory("workspace", null)
    val docsDir = tempDir.resolve("docs")
    docsDir.mkdir()
    val apiDir = docsDir.resolve("api")
    apiDir.mkdir()

    // Register the docs directory as the content root
    val docsDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(docsDir)
    assertNotNull("Failed to find virtual file for docs directory", docsDirVirtualFile)
    PsiTestUtil.addContentRoot(module, docsDirVirtualFile!!)

    // Test getting path info using overlapping path "docs/api"
    // This should detect that "docs" overlaps with the root path and use "api" as the relative path
    val result = ClientWorkspaces(project).getPathInfo("docs/api")
    assertEquals("Should exist", true, result.exists)
    assertEquals("Should be a directory", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.DIRECTORY, result.type)
    assertNotNull("Should have qualified path", result.filepath)
    assertEquals("Should have correct relative path", "api", result.filepath?.relPath)
    assertEquals("Should have correct root path", docsDirVirtualFile.path, result.filepath?.rootPath)
  }

  @Test
  fun testSearchForRelativeFileWithEmptySegments() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a directory structure:
    // root = /tmp/workspace
    // path = workspace//file.txt (double slash)
    // should find file at /tmp/workspace/file.txt
    val tempDir = FileUtil.createTempDirectory("workspace", null)
    val file = tempDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the workspace directory as the content root
    val workspaceDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for workspace directory", workspaceDirVirtualFile)
    PsiTestUtil.addContentRoot(module, workspaceDirVirtualFile!!)

    // Test reading the file using path with empty segments (double slash)
    // Our implementation should filter out empty segments
    val result = ClientWorkspaces(project).readFile("workspace//$wantFileName")
    assertNotNull("Should find file with empty segments", result.qualifiedPath)
    assertEquals("Should have correct relative path", wantFileName, result.qualifiedPath?.relPath)
    assertEquals("Should have correct root path", workspaceDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithComplexPath() {
    val wantContent = "test content"
    val wantFileName = "Component.tsx"

    // Create a complex directory structure similar to a real project:
    // root = /tmp/myapp/frontend/src/components
    // path = frontend/src/components/ui/Button/Component.tsx
    // should find file at /tmp/myapp/frontend/src/components/ui/Button/Component.tsx
    val tempDir = FileUtil.createTempDirectory("myapp", null)
    val frontendDir = tempDir.resolve("frontend")
    frontendDir.mkdir()
    val srcDir = frontendDir.resolve("src")
    srcDir.mkdir()
    val componentsDir = srcDir.resolve("components")
    componentsDir.mkdir()
    val uiDir = componentsDir.resolve("ui")
    uiDir.mkdir()
    val buttonDir = uiDir.resolve("Button")
    buttonDir.mkdir()
    val file = buttonDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the components directory as the content root
    val componentsDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(componentsDir)
    assertNotNull("Failed to find virtual file for components directory", componentsDirVirtualFile)
    PsiTestUtil.addContentRoot(module, componentsDirVirtualFile!!)

    // Test reading the file using overlapping path "frontend/src/components/ui/Button/Component.tsx"
    // This should detect that "frontend/src/components" overlaps with the root path and use "ui/Button/Component.tsx" as the relative path
    val result = ClientWorkspaces(project).readFile("frontend/src/components/ui/Button/$wantFileName")
    assertNotNull("Should find file with complex overlapping path", result.qualifiedPath)
    assertEquals("Should have correct relative path", "ui/Button/$wantFileName", result.qualifiedPath?.relPath)
    assertEquals("Should have correct root path", componentsDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testGetPathInfoForFile() {
    // Create a temporary file
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val file = tempDir.resolve("test.txt")
    file.writeText("test content")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test getting path info for the file using absolute path
    val result = ClientWorkspaces(project).getPathInfo(file.absolutePath)
    assertEquals("Should exist", true, result.exists)
    assertEquals("Should be a file", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.FILE, result.type)
    assertNotNull("Should have qualified path", result.filepath)
    assertEquals("Should have correct relative path", "test.txt", result.filepath?.relPath)
    assertEquals("Should have correct root path", tempDirVirtualFile.path, result.filepath?.rootPath)
  }

  @Test
  fun testGetPathInfoForDirectory() {
    // Create a temporary directory
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val subDir = tempDir.resolve("subdir")
    subDir.mkdir()

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test getting path info for the directory using absolute path
    val result = ClientWorkspaces(project).getPathInfo(subDir.absolutePath)
    assertEquals("Should exist", true, result.exists)
    assertEquals("Should be a directory", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.DIRECTORY, result.type)
    assertNotNull("Should have qualified path", result.filepath)
    assertEquals("Should have correct relative path", "subdir", result.filepath?.relPath)
    assertEquals("Should have correct root path", tempDirVirtualFile.path, result.filepath?.rootPath)
  }

  @Test
  fun testGetPathInfoForRelativePath() {
    // Create a temporary file
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val file = tempDir.resolve("relative.txt")
    file.writeText("relative content")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test getting path info using relative path
    val result = ClientWorkspaces(project).getPathInfo("relative.txt")
    assertEquals("Should exist", true, result.exists)
    assertEquals("Should be a file", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.FILE, result.type)
    assertNotNull("Should have qualified path", result.filepath)
    assertEquals("Should have correct relative path", "relative.txt", result.filepath?.relPath)
    assertEquals("Should have correct root path", tempDirVirtualFile.path, result.filepath?.rootPath)
  }

  @Test
  fun testGetPathInfoForNestedPath() {
    // Create a nested directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val nestedDir = tempDir.resolve("level1/level2")
    nestedDir.mkdirs()
    val nestedFile = tempDir.resolve("level1/level2/nested.txt")
    nestedFile.writeText("nested content")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test getting path info for nested file using absolute path
    val result = ClientWorkspaces(project).getPathInfo(nestedFile.absolutePath)
    assertEquals("Should exist", true, result.exists)
    assertEquals("Should be a file", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.FILE, result.type)
    assertNotNull("Should have qualified path", result.filepath)
    assertEquals("Should have correct relative path", "level1/level2/nested.txt", result.filepath?.relPath)
    assertEquals("Should have correct root path", tempDirVirtualFile.path, result.filepath?.rootPath)
  }

  @Test
  fun testGetPathInfoForNonExistentPath() {
    // Test getting path info for a non-existent path
    val result = ClientWorkspaces(project).getPathInfo("/non/existent/path.txt")
    assertEquals("Should not exist", false, result.exists)
    assertNull("Should not have file type", result.type)
    assertNull("Should not have qualified path", result.filepath)
  }

  @Test
  fun testGetPathInfoForNonExistentRelativePath() {
    // Create a temporary directory but don't create the file
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test getting path info for non-existent relative path
    val result = ClientWorkspaces(project).getPathInfo("nonexistent.txt")
    assertEquals("Should not exist", false, result.exists)
    assertNull("Should not have file type", result.type)
    assertNull("Should not have qualified path", result.filepath)
  }

  @Test
  fun testGetPathInfoWithSymlinks() {
    // Create a temporary directory structure with symlinks
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create target file and directory
    tempDir.resolve("target.txt").writeText("target content")
    tempDir.resolve("targetdir").mkdir()

    // Create symlinks
    val symlinkFile = tempDir.resolve("symlink_file").toPath()
    val symlinkDir = tempDir.resolve("symlink_dir").toPath()

    java.nio.file.Files.createSymbolicLink(symlinkFile, tempDir.resolve("target.txt").toPath())
    java.nio.file.Files.createSymbolicLink(symlinkDir, tempDir.resolve("targetdir").toPath())

    // Register with the LocalFileSystem and force refresh to detect symlinks
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Force refresh the entire directory to ensure symlinks are detected
    tempDirVirtualFile.refresh(false, true)

    // Test getting path info for symlink to file
    val resultFile = ClientWorkspaces(project).getPathInfo(symlinkFile.toString())
    assertEquals("Symlink to file should exist", true, resultFile.exists)
    // Should be SYMBOLIC_LINK_TO_FILE = 65
    assertEquals("Should be detected as symlink to file", 65, resultFile.type?.number)
    assertNotNull("Should have qualified path", resultFile.filepath)

    // Test getting path info for symlink to directory
    val resultDir = ClientWorkspaces(project).getPathInfo(symlinkDir.toString())
    assertEquals("Symlink to directory should exist", true, resultDir.exists)
    // Should be SYMBOLIC_LINK (64) | DIRECTORY (2) = 66
    assertEquals("Should be detected as symlink to directory", 66, resultDir.type?.number)
    assertNotNull("Should have qualified path", resultDir.filepath)
  }

  @Test
  fun testGetPathInfoWithBrokenSymlink() {
    // Create a temporary directory with broken symlink
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create broken symlink
    val brokenSymlink = tempDir.resolve("broken_symlink").toPath()

    try {
      java.nio.file.Files.createSymbolicLink(brokenSymlink, tempDir.resolve("nonexistent.txt").toPath())
    } catch (e: Exception) {
      // Skip test if symlinks are not supported on this platform
      println("Skipping broken symlink getPathInfo test - symlinks not supported: ${e.message}")
      return
    }

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test getting path info for broken symlink
    val result = ClientWorkspaces(project).getPathInfo(brokenSymlink.toString())
    // Broken symlinks might not be found by IntelliJ's VirtualFile system
    // The exact behavior depends on the platform and IntelliJ implementation
    if (result.exists == true) {
      // If the broken symlink is detected, it should be identified as a symlink
      // For broken symlinks, we might get just SYMBOLIC_LINK (64) or SYMBOLIC_LINK | UNKNOWN (64 | 0 = 64)
      assertTrue("Should be detected as symlink if found", (result.type?.number ?: 0) and 64 == 64)
      assertNotNull("Should have qualified path if exists", result.filepath)
    } else {
      assertEquals("Should not exist if not found", false, result.exists)
      assertNull("Should not have file type if not found", result.type)
      assertNull("Should not have qualified path if not found", result.filepath)
    }
  }

  @Test
  fun testGetPathInfoForRootDirectory() {
    // Create a temporary directory
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test getting path info for the root directory using absolute path
    val result = ClientWorkspaces(project).getPathInfo(tempDir.absolutePath)
    assertEquals("Should exist", true, result.exists)
    assertEquals("Should be a directory", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.DIRECTORY, result.type)
    // For the root directory, the qualified path behavior may vary
    // The important thing is that the file exists and is detected as a directory
    if (result.filepath != null) {
      assertNotNull("Should have relative path", result.filepath?.relPath)
      // Root path may be empty or the full path depending on implementation
      assertNotNull("Should have root path", result.filepath?.rootPath)
    }
  }

  @Test
  fun testGetPathInfoForEmptyPath() {
    // Create a temporary directory
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test getting path info for empty path (should refer to root)
    val result = ClientWorkspaces(project).getPathInfo("")
    assertEquals("Should exist", true, result.exists)
    assertEquals("Should be a directory", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.DIRECTORY, result.type)
    assertNotNull("Should have qualified path", result.filepath)
  }

  @Test
  fun testGetPathInfoWithGitRoot() {
    // Create a temporary directory with .git structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create .git directory and a file
    tempDir.resolve(".git").mkdir()
    tempDir.resolve(".git/config").writeText("git config")
    tempDir.resolve("project_file.txt").writeText("project content")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test getting path info for file in git root
    val result = ClientWorkspaces(project).getPathInfo(tempDir.resolve("project_file.txt").absolutePath)
    assertEquals("Should exist", true, result.exists)
    assertEquals("Should be a file", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.FILE, result.type)
    assertNotNull("Should have qualified path", result.filepath)
    assertEquals("Should have correct relative path", "project_file.txt", result.filepath?.relPath)
    // The root path should be the git root (tempDir), not the project root
    assertEquals("Should have git root as root path", tempDir.path, result.filepath?.rootPath)
  }

  @Test
  fun testGetPathInfoSymlinkTypeDetection() {
    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create target file and directory
    tempDir.resolve("target_file.txt").writeText("target content")
    tempDir.resolve("target_dir").mkdir()
    tempDir.resolve("target_dir/nested.txt").writeText("nested content")

    // Create symlinks
    val symlinkToFile = tempDir.resolve("link_to_file").toPath()
    val symlinkToDir = tempDir.resolve("link_to_dir").toPath()

    try {
      java.nio.file.Files.createSymbolicLink(symlinkToFile, tempDir.resolve("target_file.txt").toPath())
      java.nio.file.Files.createSymbolicLink(symlinkToDir, tempDir.resolve("target_dir").toPath())
    } catch (e: Exception) {
      // Skip test if symlinks are not supported on this platform
      println("Skipping symlink type detection test - symlinks not supported: ${e.message}")
      return
    }

    // Register with the LocalFileSystem and force refresh to detect symlinks
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Force refresh the entire directory to ensure symlinks are detected
    tempDirVirtualFile.refresh(false, true)

    // Test that regular file is detected correctly
    val regularFileResult = ClientWorkspaces(project).getPathInfo(tempDir.resolve("target_file.txt").absolutePath)
    assertEquals("Regular file should exist", true, regularFileResult.exists)
    assertEquals("Should be detected as file", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.FILE, regularFileResult.type)

    // Test that regular directory is detected correctly
    val regularDirResult = ClientWorkspaces(project).getPathInfo(tempDir.resolve("target_dir").absolutePath)
    assertEquals("Regular directory should exist", true, regularDirResult.exists)
    assertEquals("Should be detected as directory", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.DIRECTORY, regularDirResult.type)

    // Test that symlink to file is detected with bitmask SYMBOLIC_LINK | FILE
    val symlinkFileResult = ClientWorkspaces(project).getPathInfo(symlinkToFile.toString())
    assertEquals("Symlink to file should exist", true, symlinkFileResult.exists)
    // Should be SYMBOLIC_LINK (64) | FILE (1) = 65
    assertEquals("Should be detected as symlink to file", 65, symlinkFileResult.type?.number)
    // Verify it has both symlink and file bits set
    assertTrue("Should have symlink bit set", (symlinkFileResult.type?.number ?: 0) and 64 == 64)
    assertTrue("Should have file bit set", (symlinkFileResult.type?.number ?: 0) and 1 == 1)
    assertNotNull("Should have qualified path", symlinkFileResult.filepath)

    // Test that symlink to directory is detected with bitmask SYMBOLIC_LINK | DIRECTORY
    val symlinkDirResult = ClientWorkspaces(project).getPathInfo(symlinkToDir.toString())
    assertEquals("Symlink to directory should exist", true, symlinkDirResult.exists)
    // Should be SYMBOLIC_LINK (64) | DIRECTORY (2) = 66
    assertEquals("Should be detected as symlink to directory", 66, symlinkDirResult.type?.number)
    // Verify it has both symlink and directory bits set
    assertTrue("Should have symlink bit set", (symlinkDirResult.type?.number ?: 0) and 64 == 64)
    assertTrue("Should have directory bit set", (symlinkDirResult.type?.number ?: 0) and 2 == 2)
    assertNotNull("Should have qualified path", symlinkDirResult.filepath)
  }

  @Test
  fun testFileTypeBitmaskValues() {
    // Test that the FileType enum values match expected bitmask values
    assertEquals("FILE should be 1", 1, com.augmentcode.sidecar.rpc.clientInterfaces.FileType.FILE.number)
    assertEquals("DIRECTORY should be 2", 2, com.augmentcode.sidecar.rpc.clientInterfaces.FileType.DIRECTORY.number)
    assertEquals("SYMBOLIC_LINK should be 64", 64, com.augmentcode.sidecar.rpc.clientInterfaces.FileType.SYMBOLIC_LINK.number)
    assertEquals("UNKNOWN should be 0", 0, com.augmentcode.sidecar.rpc.clientInterfaces.FileType.UNKNOWN.number)

    // Test bitmask combinations
    val symlinkToFile = 64 or 1 // 65
    val symlinkToDir = 64 or 2 // 66

    assertEquals("Symlink to file should be 65", 65, symlinkToFile)
    assertEquals("Symlink to directory should be 66", 66, symlinkToDir)

    // Test that we can extract individual bits
    assertTrue("Symlink to file should have symlink bit", (symlinkToFile and 64) == 64)
    assertTrue("Symlink to file should have file bit", (symlinkToFile and 1) == 1)
    assertFalse("Symlink to file should not have directory bit", (symlinkToFile and 2) == 2)

    assertTrue("Symlink to directory should have symlink bit", (symlinkToDir and 64) == 64)
    assertTrue("Symlink to directory should have directory bit", (symlinkToDir and 2) == 2)
    assertFalse("Symlink to directory should not have file bit", (symlinkToDir and 1) == 1)
  }

  @Test
  fun testSearchForRelativeFileWithNestedContentRoots() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a directory structure with nested content roots:
    // outer root = /tmp/project
    // inner root = /tmp/project/src
    // file = /tmp/project/src/file.txt
    // When searching for "src/file.txt", should find file relative to outer root
    val tempDir = FileUtil.createTempDirectory("project", null)
    val srcDir = tempDir.resolve("src")
    srcDir.mkdir()
    val file = srcDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register both directories as content roots (nested scenario)
    val outerRootVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    val innerRootVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(srcDir)
    assertNotNull("Failed to find virtual file for outer root", outerRootVirtualFile)
    assertNotNull("Failed to find virtual file for inner root", innerRootVirtualFile)

    // Add outer root first, then inner root
    PsiTestUtil.addContentRoot(module, outerRootVirtualFile!!)
    PsiTestUtil.addContentRoot(module, innerRootVirtualFile!!)

    // Test reading the file using path "src/file.txt"
    // Should prioritize the outer root and find the file with relative path "src/file.txt"
    val result = ClientWorkspaces(project).readFile("src/$wantFileName")
    assertNotNull("Should find file with nested content roots", result.qualifiedPath)
    assertEquals("Should have correct relative path", "src/$wantFileName", result.qualifiedPath?.relPath)
    assertEquals("Should use outer root path", outerRootVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithNestedContentRootsReverseOrder() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Same as above but add inner root first, then outer root
    // Should still prioritize outer root
    val tempDir = FileUtil.createTempDirectory("project", null)
    val srcDir = tempDir.resolve("src")
    srcDir.mkdir()
    val file = srcDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register both directories as content roots (nested scenario)
    val outerRootVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    val innerRootVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(srcDir)
    assertNotNull("Failed to find virtual file for outer root", outerRootVirtualFile)
    assertNotNull("Failed to find virtual file for inner root", innerRootVirtualFile)

    // Add inner root first, then outer root (reverse order)
    PsiTestUtil.addContentRoot(module, innerRootVirtualFile!!)
    PsiTestUtil.addContentRoot(module, outerRootVirtualFile!!)

    // Test reading the file using path "src/file.txt"
    // Should still prioritize the outer root regardless of registration order
    val result = ClientWorkspaces(project).readFile("src/$wantFileName")
    assertNotNull("Should find file with nested content roots in reverse order", result.qualifiedPath)
    assertEquals("Should have correct relative path", "src/$wantFileName", result.qualifiedPath?.relPath)
    assertEquals("Should use outer root path", outerRootVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithMultipleNestedContentRoots() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a directory structure with multiple nested content roots:
    // root1 = /tmp/project
    // root2 = /tmp/project/src
    // root3 = /tmp/project/src/main
    // file = /tmp/project/src/main/file.txt
    val tempDir = FileUtil.createTempDirectory("project", null)
    val srcDir = tempDir.resolve("src")
    srcDir.mkdir()
    val mainDir = srcDir.resolve("main")
    mainDir.mkdir()
    val file = mainDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register all three directories as content roots
    val root1VirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    val root2VirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(srcDir)
    val root3VirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(mainDir)
    assertNotNull("Failed to find virtual file for root1", root1VirtualFile)
    assertNotNull("Failed to find virtual file for root2", root2VirtualFile)
    assertNotNull("Failed to find virtual file for root3", root3VirtualFile)

    PsiTestUtil.addContentRoot(module, root1VirtualFile!!)
    PsiTestUtil.addContentRoot(module, root2VirtualFile!!)
    PsiTestUtil.addContentRoot(module, root3VirtualFile!!)

    // Test reading the file using path "src/main/file.txt"
    // Should prioritize the outermost root (root1) and find the file with relative path "src/main/file.txt"
    val result = ClientWorkspaces(project).readFile("src/main/$wantFileName")
    assertNotNull("Should find file with multiple nested content roots", result.qualifiedPath)
    assertEquals("Should have correct relative path", "src/main/$wantFileName", result.qualifiedPath?.relPath)
    assertEquals("Should use outermost root path", root1VirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithSeparateContentRoots() {
    val wantContent1 = "content1"
    val wantContent2 = "content2"
    val wantFileName = "file.txt"

    // Create two separate (non-nested) content roots with same file name
    val tempDir1 = FileUtil.createTempDirectory("project1", null)
    val tempDir2 = FileUtil.createTempDirectory("project2", null)
    val file1 = tempDir1.resolve(wantFileName)
    val file2 = tempDir2.resolve(wantFileName)
    file1.writeText(wantContent1)
    file2.writeText(wantContent2)

    // Register both directories as content roots
    val root1VirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir1)
    val root2VirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir2)
    assertNotNull("Failed to find virtual file for root1", root1VirtualFile)
    assertNotNull("Failed to find virtual file for root2", root2VirtualFile)

    PsiTestUtil.addContentRoot(module, root1VirtualFile!!)
    PsiTestUtil.addContentRoot(module, root2VirtualFile!!)

    // Test reading the file using just the filename
    // Should find one of the files (order depends on iteration order)
    val result = ClientWorkspaces(project).readFile(wantFileName)
    assertNotNull("Should find file with separate content roots", result.qualifiedPath)
    assertEquals("Should have correct relative path", wantFileName, result.qualifiedPath?.relPath)
    assertTrue(
      "Should have content from one of the files",
      result.contents == wantContent1 || result.contents == wantContent2,
    )
  }

  @Test
  fun testSearchForRelativeFileWithNestedContentRootsAndOverlappingPath() {
    val wantContent = "test content"
    val wantFileName = "Component.tsx"

    // Create a complex nested structure:
    // outer root = /tmp/myapp/frontend
    // inner root = /tmp/myapp/frontend/src/components
    // file = /tmp/myapp/frontend/src/components/ui/Component.tsx
    // path = frontend/src/components/ui/Component.tsx
    // Should prioritize outer root and use overlapping path logic
    val tempDir = FileUtil.createTempDirectory("myapp", null)
    val frontendDir = tempDir.resolve("frontend")
    frontendDir.mkdir()
    val srcDir = frontendDir.resolve("src")
    srcDir.mkdir()
    val componentsDir = srcDir.resolve("components")
    componentsDir.mkdir()
    val uiDir = componentsDir.resolve("ui")
    uiDir.mkdir()
    val file = uiDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register both frontend and components directories as content roots
    val frontendVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(frontendDir)
    val componentsVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(componentsDir)
    assertNotNull("Failed to find virtual file for frontend directory", frontendVirtualFile)
    assertNotNull("Failed to find virtual file for components directory", componentsVirtualFile)

    PsiTestUtil.addContentRoot(module, frontendVirtualFile!!)
    PsiTestUtil.addContentRoot(module, componentsVirtualFile!!)

    // Test reading the file using overlapping path "frontend/src/components/ui/Component.tsx"
    // Should prioritize the outer root (frontend) and detect overlap, resulting in "src/components/ui/Component.tsx"
    val result = ClientWorkspaces(project).readFile("frontend/src/components/ui/$wantFileName")
    assertNotNull("Should find file with nested roots and overlapping path", result.qualifiedPath)
    assertEquals("Should have correct relative path", "src/components/ui/$wantFileName", result.qualifiedPath?.relPath)
    assertEquals("Should use outer root path", frontendVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithIdenticalContentRoots() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Edge case: same directory registered multiple times as content root
    val tempDir = FileUtil.createTempDirectory("project", null)
    val file = tempDir.resolve(wantFileName)
    file.writeText(wantContent)

    val rootVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for root", rootVirtualFile)

    // Add the same root multiple times
    PsiTestUtil.addContentRoot(module, rootVirtualFile!!)
    PsiTestUtil.addContentRoot(module, rootVirtualFile)
    PsiTestUtil.addContentRoot(module, rootVirtualFile)

    // Should still work correctly and not cause issues
    val result = ClientWorkspaces(project).readFile(wantFileName)
    assertNotNull("Should find file with identical content roots", result.qualifiedPath)
    assertEquals("Should have correct relative path", wantFileName, result.qualifiedPath?.relPath)
    assertEquals("Should have correct root path", rootVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithDeepNestedContentRoots() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a very deep nested structure:
    // root1 = /tmp/project
    // root2 = /tmp/project/a/b/c/d/e
    // file = /tmp/project/a/b/c/d/e/file.txt
    val tempDir = FileUtil.createTempDirectory("project", null)
    val deepDir = tempDir.resolve("a/b/c/d/e")
    deepDir.mkdirs()
    val file = deepDir.resolve(wantFileName)
    file.writeText(wantContent)

    val outerRootVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    val deepRootVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(deepDir)
    assertNotNull("Failed to find virtual file for outer root", outerRootVirtualFile)
    assertNotNull("Failed to find virtual file for deep root", deepRootVirtualFile)

    PsiTestUtil.addContentRoot(module, outerRootVirtualFile!!)
    PsiTestUtil.addContentRoot(module, deepRootVirtualFile!!)

    // Test reading the file using path "a/b/c/d/e/file.txt"
    // Should prioritize the outer root
    val result = ClientWorkspaces(project).readFile("a/b/c/d/e/$wantFileName")
    assertNotNull("Should find file with deep nested content roots", result.qualifiedPath)
    assertEquals("Should have correct relative path", "a/b/c/d/e/$wantFileName", result.qualifiedPath?.relPath)
    assertEquals("Should use outer root path", outerRootVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }
}
