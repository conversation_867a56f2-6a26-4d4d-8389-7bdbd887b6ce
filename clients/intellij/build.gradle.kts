import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent
import org.jetbrains.changelog.Changelog
import org.jetbrains.changelog.markdownToHTML
import org.jetbrains.intellij.platform.gradle.IntelliJPlatformType
import org.jetbrains.intellij.platform.gradle.TestFrameworkType

fun properties(key: String) = providers.gradleProperty(key)

fun environment(key: String) = providers.environmentVariable(key)

plugins {
  id("java") // Java support
  alias(libs.plugins.kotlin) // Kotlin support
  alias(libs.plugins.gradleIntelliJPlugin) // Gradle IntelliJ Plugin
  alias(libs.plugins.changelog) // Gradle Changelog Plugin
  alias(libs.plugins.qodana) // Gradle Qodana Plugin
  alias(libs.plugins.kover) // Gradle Kover Plugin
  alias(libs.plugins.proto) // Gradle ProtoBuf Plugin
  alias(libs.plugins.sentryGradle) // Sentry Gradle Plugin
}

group = properties("pluginGroup").get()
version = environment("PUBLISH_VERSION").getOrElse("1.0.0-snapshot")

// Configure project's dependencies
repositories {
  mavenCentral()
  intellijPlatform {
    defaultRepositories()
  }
}

// to override IDE dependencies https://plugins.jetbrains.com/docs/intellij/plugin-class-loaders.html#overriding-ide-dependencies
configurations.all {
  resolutionStrategy.sortArtifacts(ResolutionStrategy.SortOrder.DEPENDENCY_FIRST)
}

// Dependencies are managed with Gradle version catalog - read more: https://docs.gradle.org/current/userguide/platforms.html#sub:version-catalog
dependencies {
  intellijPlatform {
    create(properties("platformType"), properties("platformVersion"))
    plugins(properties("platformPlugins").map { it.split(',').map(String::trim).filter(String::isNotEmpty) })
    bundledModule("intellij.platform.collaborationTools")
    bundledPlugins(
      // to have terminal classes for compilation
      "com.jetbrains.sh",
      "org.jetbrains.plugins.terminal",
    )
    pluginVerifier()
    zipSigner()

    testFramework(TestFrameworkType.Platform)
  }
  // bundle all the dependencies except for kotlin coroutines
  // since https://plugins.jetbrains.com/docs/intellij/using-kotlin.html#coroutinesLibraries
  implementation(libs.caffeine)
  implementation(libs.codeownersEnforcerRules)
  implementation(libs.grpc)
  implementation(libs.grpcKotlin) {
    exclude(group = "org.jetbrains.kotlinx")
  }
  implementation(libs.gson)
  implementation(libs.ktorCore) {
    exclude(group = "org.jetbrains.kotlinx")
  }
  implementation(libs.ktorCIO) {
    exclude(group = "org.jetbrains.kotlinx")
  }
  implementation(libs.protoKotlin)
  implementation(libs.protoJavaUtil)

  // Sentry dependencies
  implementation(libs.sentry)
  implementation(libs.sentryKotlin)

  testImplementation(libs.ktorMock) {
    exclude(group = "org.jetbrains.kotlinx")
  }
  testImplementation(libs.mockk) {
    exclude(group = "org.jetbrains.kotlinx")
  }
  testImplementation(libs.coroutinesTest) {
    exclude(group = "org.jetbrains.kotlinx")
  }
}

protobuf {
  protoc {
    artifact = libs.protoCompiler.get().toString()
  }
  plugins {
    create("grpc") {
      artifact = libs.protocGenJava.get().toString()
    }
    create("grpckt") {
      artifact = libs.protocGenKotlin.get().toString() + ":jdk8@jar"
    }
  }

  // Define the proto source directory
  val sidecarProtoSrcDir = file("../sidecar/node-process/protos")
  val webviewMsgsProtoSrcDir = file("../common/webviews/protos")

  // Create a directory with modified proto files to optimize build speed
  val modifiedProtosDir = layout.buildDirectory.dir("generated/modified-protos").get().asFile

  // Register the task using the proper task class
  tasks.register<PrepareProtoFilesTask>("prepareSidecarProtoFiles") {
    sourceDir = sidecarProtoSrcDir
    outputDir = modifiedProtosDir.resolve("clients/sidecar/node-process/protos")
    dependsOn("cleanProtoFiles")
  }
  tasks.register<PrepareProtoFilesTask>("prepareWebviewMsgsProtoFiles") {
    sourceDir = webviewMsgsProtoSrcDir
    outputDir = modifiedProtosDir.resolve("clients/common/webviews/protos")
    dependsOn("cleanProtoFiles")
  }

  // Add Google protobuf dependencies
  dependencies {
    // This ensures the Google protobuf files are available for import
    protobuf("com.google.protobuf:protobuf-java:${libs.versions.protobufVersion.get()}")
  }

  // Make the protobuf compilation depend on preparing the proto files
  tasks.named("generateProto") {
    dependsOn("prepareProtoFiles")
  }

  sourceSets {
    main {
      proto {
        // Use the directory with modified proto files
        srcDir(modifiedProtosDir)
      }
    }
  }

  generateProtoTasks {
    all().forEach {
      it.plugins {
        create("grpc")
        create("grpckt")
      }
      it.builtins {
        create("kotlin")
      }

      // Set up caching to improve build speed
      it.outputs.cacheIf { true }
    }
  }
}

kotlin {
  jvmToolchain(21)

  // Add compiler options to treat warnings as errors
  compilerOptions {
    // Treat all warnings as errors
    allWarningsAsErrors.set(true)
  }
}

// Configure Gradle IntelliJ Plugin - read more: https://plugins.jetbrains.com/docs/intellij/tools-gradle-intellij-plugin.html
intellijPlatform {
  pluginConfiguration {
    name = properties("pluginName")
    ideaVersion {
      untilBuild = provider { null }
    }
  }

  pluginVerification {
    ides {
      recommended()
    }
  }
}

// Configure Gradle Changelog Plugin - read more: https://github.com/JetBrains/gradle-changelog-plugin
changelog {
  groups.empty()
  repositoryUrl = properties("pluginRepositoryUrl")
}

// Configure Gradle Qodana Plugin - read more: https://github.com/JetBrains/gradle-qodana-plugin
qodana {
  cachePath = provider { file(".qodana").canonicalPath }
  reportPath = provider { file("build/reports/inspections").canonicalPath }
  saveReport = true
  showReport = environment("QODANA_SHOW_REPORT").map { it.toBoolean() }.getOrElse(false)
}

val runIdeLatest by intellijPlatformTesting.runIde.registering {
  type = IntelliJPlatformType.IntellijIdeaCommunity
  version = properties("platformVersionLatest").get()
}

tasks {
  wrapper {
    gradleVersion = properties("gradleVersion").get()
  }

  // Task to build webviews for IntelliJ
  register<Exec>("buildWebviews") {
    description = "Build webviews for IntelliJ plugin"
    group = "build"
    workingDir = file("../common/webviews")
    commandLine = listOf("/bin/sh", "-c", "pnpm run build:intellij")

    // Track all source files that might affect the build
    inputs.files(
      fileTree("../common/webviews/src") {
        include("**/*")
      },
    )
    inputs.files(file("../common/webviews/package.json"), file("../common/webviews/vite.config.ts"))

    // Track the output directory
    outputs.dir(file("src/main/resources/webviews"))
  }

  // Task to build sidecar for IntelliJ
  register<Exec>("buildSidecar") {
    description = "Build sidecar for IntelliJ plugin"
    group = "build"
    workingDir = file("../sidecar/node-process")
    commandLine = listOf("/bin/sh", "-c", "pnpm run build:intellij")

    // Track all source files that might affect the build
    inputs.files(
      fileTree("../sidecar/node-process/src") {
        include("**/*")
      },
    )
    inputs.files(
      fileTree("../sidecar/node-process/protos") {
        include("**/*")
      },
    )
    inputs.files(file("../sidecar/node-process/package.json"), file("../sidecar/node-process/tsconfig.json"))
    inputs.files(
      fileTree("../sidecar/libs/src") {
        include("**/*")
      },
    )

    // Track the output directory
    outputs.dir(file("src/main/resources/sidecar"))
  }

  // makes the standard streams (err and out) visible at console when running tests
  test {
    testLogging {
      events(TestLogEvent.PASSED, TestLogEvent.SKIPPED, TestLogEvent.FAILED)

      showExceptions = true
      exceptionFormat = TestExceptionFormat.FULL
      showCauses = true
      showStackTraces = true
    }
  }
  patchPluginXml {
    sinceBuild = properties("pluginSinceBuild")

    // Extract the <!-- Plugin description --> section from README.md and provide for the plugin's manifest
    pluginDescription =
      providers.fileContents(layout.projectDirectory.file("README.md")).asText.map {
        val start = "<!-- Plugin description -->"
        val end = "<!-- Plugin description end -->"

        with(it.lines()) {
          if (!containsAll(listOf(start, end))) {
            throw GradleException("Plugin description section not found in README.md:\n$start ... $end")
          }
          subList(indexOf(start) + 1, indexOf(end)).joinToString("\n").let(::markdownToHTML)
        }
      }

    val changelog = project.changelog // local variable for configuration cache compatibility
    // Get the latest available change notes from the changelog file or use custom change notes if provided
    changeNotes =
      environment("CHANGE_NOTES").map { customNotes ->
        if (customNotes.isNotEmpty()) {
          // Use custom change notes if provided
          markdownToHTML(customNotes.replace("\\n", "\n").replace("\\r", "\r").replace("\\t", "\t"))
        } else {
          null
        }
      }.getOrElse(
        environment("PUBLISH_VERSION").map { pluginVersion ->
          with(changelog) {
            renderItem(
              (getOrNull(pluginVersion) ?: getUnreleased())
                .withHeader(false)
                .withEmptySections(false),
              Changelog.OutputType.HTML,
            )
          }
        }.getOrElse(""),
      )
  }

  signPlugin {
    certificateChain = environment("CERTIFICATE_CHAIN")
    privateKey = environment("PRIVATE_KEY")
    password = environment("PRIVATE_KEY_PASSWORD")
  }

  publishPlugin {
    dependsOn("patchChangelog")
    token = environment("PUBLISH_TOKEN")
    channels = listOf(environment("PUBLISH_CHANNEL").getOrElse("default"))
  }
}

// Proto files are built by gradle AND bazel but both builds have different build considerations.
// Bazel assumes all proto imports are from the root of the Augment repo.
// Gradle assumes all proto imports are relative to the root of a srcDir.
// We could tell gradle a root is the root of Augment, but that results in gradle searching every proto file in the
// repo, which dramatically slows down the build.
// The following task copies the sidecar proto files to a temporary directory and modifies the imports to be relative.
// This allows us to use the same proto files for both bazel and gradle builds.

abstract class PrepareProtoFilesTask : DefaultTask() {
  @get:InputDirectory
  var sourceDir: File? = null

  @get:OutputDirectory
  var outputDir: File? = null

  @TaskAction
  fun prepareProtoFiles() {
    // Make the output directory
    outputDir?.mkdirs()
    // Process each proto file
    sourceDir?.listFiles()?.filter { it.extension == "proto" }?.forEach { protoFile ->
      if (sourceDir == null || outputDir == null) return@forEach

      val targetFile = File(outputDir, protoFile.name)
      if (!targetFile.exists() || targetFile.lastModified() < protoFile.lastModified()) {
        // Write the copy of the proto file
        targetFile.writeText(protoFile.readText())
      }
    }
  }
}

// Create a task to clean the proto files directory
abstract class CleanProtoFilesTask : DefaultTask() {
  @get:OutputDirectory
  val outputDir: File = project.layout.buildDirectory.dir("generated/modified-protos").get().asFile

  @TaskAction
  fun clean() {
    // Delete the directory and its contents
    outputDir.deleteRecursively()
    println("Cleaned proto files directory: $outputDir")
    // Recreate the empty directory
    outputDir.mkdirs()
  }
}

tasks.register<CleanProtoFilesTask>("cleanProtoFiles") {
  description = "Clean the proto files output directory"
  group = "build"
}

// Create a parent task that depends on both proto preparation tasks
tasks.register("prepareProtoFiles") {
  description = "Prepare all proto files for compilation"
  group = "build"
  dependsOn("cleanProtoFiles", "prepareSidecarProtoFiles", "prepareWebviewMsgsProtoFiles")
}

tasks.named("processResources") {
  dependsOn("prepareProtoFiles", "buildWebviews", "buildSidecar")
}

// Configure Sentry Gradle Plugin
sentry {
  // Generates a source bundle and uploads it to Sentry.
  // keeping this as False as it would require additonal setup in CI
  includeSourceContext = false

  // Disables or enables dependencies metadata reporting for Sentry.
  includeDependenciesReport = true

  // Automatically adds Sentry dependencies to your project.
  autoInstallation {
    enabled = true
  }
}
