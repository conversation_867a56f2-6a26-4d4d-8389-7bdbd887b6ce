/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import ThreadLengthWarning from "$common-webviews/src/apps/chat/components/warnings/ThreadLengthWarning.svelte";

const meta = {
  title: "app/Chat/components/warnings/ThreadLengthWarning",
  component: ThreadLengthWarning,
  tags: ["autodocs"],
  argTypes: {
    onNewThreadClick: {
      description: "Callback when the new thread button is clicked. Should be a function.",
    },
    onDismiss: {
      description: "Callback when the dismiss button is clicked. Should be a function.",
    },
  },
} satisfies Meta<ThreadLengthWarning>;

export default meta;

type Story = StoryObj<typeof meta>;

// Default thread length warning
export const Default: Story = {
  name: "Default",
  args: {
    onNewThreadClick: () => {
      console.log("New thread clicked");
    },
    onDismiss: () => {
      console.log("Dismiss clicked");
    },
  },
};

// Interactive example with action logging
export const Interactive: Story = {
  name: "Interactive (Check Console)",
  args: {
    onNewThreadClick: () => {
      alert("New thread button clicked! Check console for logs.");
      console.log("ThreadLengthWarning: New thread button clicked");
    },
    onDismiss: () => {
      alert("Dismiss button clicked! Check console for logs.");
      console.log("ThreadLengthWarning: Dismiss button clicked");
    },
  },
};
