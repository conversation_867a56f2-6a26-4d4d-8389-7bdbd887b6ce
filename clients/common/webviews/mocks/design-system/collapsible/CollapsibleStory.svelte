<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";
</script>

<ColumnLayout>
  <Fieldset title="Default">
    <div style="max-width: 600px">
      <CollapsibleAugment>
        <div slot="header" class="header-content">
          <CollapseButtonAugment />
          <h3>Collapsible Header</h3>
          <ButtonAugment variant="ghost" size={1}>Action</ButtonAugment>
        </div>
        <div class="content">
          <p>This is the main content of the collapsible component.</p>
          <p>You can put any content here.</p>
        </div>
        <div slot="footer" class="header-content">
          <span>Footer content</span>
          <ButtonAugment variant="ghost" size={1}>Footer Action</ButtonAugment>
        </div>
      </CollapsibleAugment>
    </div>
  </Fieldset>

  <Fieldset title="Initially Collapsed">
    <div style="max-width: 600px">
      <CollapsibleAugment collapsed={true}>
        <div slot="header" class="header-content">
          <CollapseButtonAugment />
          <h3>Collapsible Header</h3>
          <ButtonAugment variant="ghost" size={1}>Action</ButtonAugment>
        </div>
        <div class="content">
          <p>This is the main content of the collapsible component.</p>
          <p>You can put any content here.</p>
        </div>
      </CollapsibleAugment>
    </div>
  </Fieldset>
  <Fieldset title="Not exapandable">
    <div style="max-width: 600px">
      <CollapsibleAugment expandable={false}>
        <div slot="header" class="header-content">
          <CollapseButtonAugment />
          <h3>Collapsible Header</h3>
          <ButtonAugment variant="ghost" size={1}>Action</ButtonAugment>
        </div>
        <div class="content">
          <p>This is the main content of the collapsible component.</p>
          <p>You can put any content here.</p>
        </div>
      </CollapsibleAugment>
    </div>
  </Fieldset>

  <Fieldset title="With Sticky Header">
    <div class="scroll-container">
      <div
        style="height: 100px; background: var(--ds-panel-solid); margin-bottom: var(--ds-spacing-3);"
      >
        Scroll down
      </div>
      <CollapsibleAugment stickyHeader={true}>
        <div slot="header" class="header-content">
          <CollapseButtonAugment />
          <h3>Sticky Header</h3>
          <ButtonAugment variant="ghost" size={1}>Action</ButtonAugment>
        </div>
        <div class="content">
          <p>This header will stick to the top when scrolling.</p>
          <p>Try scrolling to see the effect.</p>
          <div style="height: 400px;"></div>
          <p>Bottom content</p>
        </div>
      </CollapsibleAugment>
    </div>
  </Fieldset>

  <Fieldset title="With sticky header with 50px offset">
    <div class="sticky-header-overlay-container">
      <div class="scroll-container">
        <div
          style="height: 100px; background: var(--ds-panel-solid); margin-bottom: var(--ds-spacing-3);"
        >
          Scroll down
        </div>
        <div class="demo-container">
          <CollapsibleAugment stickyHeaderTop={50} stickyHeader={true}>
            <div slot="header" class="header-content">
              <CollapseButtonAugment />
              <h3>Custom Background Color</h3>
              <ButtonAugment variant="ghost" size={1}>Action</ButtonAugment>
            </div>
            <div class="content">
              <p>This header uses a custom background color for the overlay.</p>
              <p>It also has a wider horizontal padding.</p>
              <div style="height: 400px;"></div>
              <p>Bottom content</p>
            </div>
          </CollapsibleAugment>
        </div>
      </div>
    </div>
  </Fieldset>
</ColumnLayout>

<style>
  .header-content {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-1);
    flex: 1;
  }

  .content {
    padding: var(--ds-spacing-3);
  }

  h3 {
    margin: 0;
    font-size: var(--ds-font-size-2);
    font-weight: var(--ds-font-weight-medium);
    flex: 1;
  }

  .scroll-container {
    height: 300px;
    overflow-y: auto;
  }
</style>
