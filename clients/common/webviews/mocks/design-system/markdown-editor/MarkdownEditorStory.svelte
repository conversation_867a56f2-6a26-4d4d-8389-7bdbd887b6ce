<script lang="ts">
  import MarkdownEditor from "$common-webviews/src/design-system/components/MarkdownEditor.svelte";
  import { onMount } from "svelte";

  // Props that can be passed to the story
  export let initialValue =
    "# Markdown Editor\n\nThis is a sample markdown content.\n\n## Features\n\n- Edit markdown content\n- Auto-save content\n- Keyboard shortcuts (Ctrl+S or Cmd+S to save)";
  export let resize: "none" | "both" | "horizontal" | "vertical" = "none";
  export let variant: "classic" | "surface" | "soft" = "surface";
  export let size: 1 | 2 | 3 = 2;
  export let debounceValue = 500;

  // Local state
  let value = initialValue;
  let selectedText = "";
  let selectionStart = 0;
  let selectionEnd = 0;

  // Mock save function
  const onSave = () => {
    // This function is passed to the MarkdownEditor component as saveFunction
    console.log("Saving content:", value);
  };

  onMount(() => {
    // Any initialization needed
  });
</script>

<div class="markdown-editor-container">
  <MarkdownEditor
    bind:selectedText
    bind:selectionStart
    bind:selectionEnd
    bind:value
    saveFunction={onSave}
    {variant}
    {size}
    {resize}
    {debounceValue}
    class="markdown-editor"
  />

  <div class="info-panel">
    <h3>Editor Information</h3>
    <div class="info-row">
      <strong>Selected Text:</strong>
      {selectedText ? `"${selectedText}"` : "None"}
    </div>
    <div class="info-row">
      <strong>Selection Range:</strong>
      {selectionStart !== selectionEnd ? `${selectionStart} - ${selectionEnd}` : "None"}
    </div>
    <div class="info-row">
      <strong>Content Length:</strong>
      {value.length} characters
    </div>
    <div class="info-row">
      <strong>Debounce Value:</strong>
      {debounceValue}ms
    </div>
    <div class="info-row">
      <strong>Resize Mode:</strong>
      {resize}
    </div>
    <div class="info-row">
      <strong>Keyboard Shortcuts:</strong>
      <ul>
        <li>Ctrl+S / Cmd+S: Save content</li>
        <li>Escape: Save content</li>
      </ul>
    </div>
  </div>
</div>

<style>
  .markdown-editor-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
  }

  .info-panel {
    padding: var(--ds-spacing-3);
    background: var(--augment-panel-background);
    border-radius: var(--ds-radius-2);
    border: var(--augment-border);
  }

  .info-row {
    margin-bottom: var(--ds-spacing-2);
  }

  h3 {
    margin-top: 0;
    margin-bottom: var(--ds-spacing-3);
  }

  ul {
    margin: var(--ds-spacing-1) 0;
    padding-left: var(--ds-spacing-4);
  }
</style>
