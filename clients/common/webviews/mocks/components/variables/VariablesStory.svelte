<script lang="ts">
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";
  import ColorSwatch from "./ColorSwatch.svelte";
  import CSSVarTable from "$common-webviews/mocks/components/variables/CSSVarTable.svelte";
  import BlockSwatch from "$common-webviews/mocks/components/variables/BlockSwatch.svelte";

  const fontVars = [
    "--augment-font-family",
    "--augment-monospace-font-family",
    "--augment-font-size",
  ];

  const colorVars = [
    "--augment-foreground",
    "--augment-window-background",
    "--augment-panel-background",
    "--augment-scrollbar-color",
  ];

  const textColorVars = [
    "--augment-text-color",
    "--augment-text-color-secondary",
    "--augment-text-color-tertiary",
  ];
</script>

<ColumnLayout>
  <Fieldset title={`Augment Colors`}>
    <ColumnLayout>
      <div class="l-colors">
        {#each colorVars as color}
          <ColorSwatch varName={color} />
        {/each}
      </div>
    </ColumnLayout>
  </Fieldset>
  <Fieldset title={`Augment Text Colors`}>
    <ColumnLayout>
      <div class="l-colors">
        {#each textColorVars as color}
          <ColorSwatch varName={color} />
        {/each}
      </div>
    </ColumnLayout>
  </Fieldset>
  <Fieldset title={`Border`}>
    <ColumnLayout>
      <div class="l-colors">
        <ColorSwatch varName={"--augment-border-color"} />
        <ColorSwatch varName={"--augment-focus-border-color"} />
      </div>
      <BlockSwatch varName={"--augment-border"} style="border: var(--augment-border)" />
      <BlockSwatch
        varName={"--augment-border-radius"}
        style="border-radius: var(--augment-border-radius)"
      />
    </ColumnLayout>
  </Fieldset>

  <Fieldset title={`Font`}>
    <ColumnLayout>
      <CSSVarTable varNames={fontVars} />
    </ColumnLayout>
  </Fieldset>
</ColumnLayout>

<style>
  .l-colors {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ds-spacing-1);
  }
</style>
