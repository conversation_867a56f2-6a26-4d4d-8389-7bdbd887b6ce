<script lang="ts">
  import ShowMore from "$common-webviews/src/common/components/ShowMore.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";

  export let maxHeight: number = 200;
  export let expanded: boolean | undefined = undefined;
  export let expandText: string = "Show more";
  export let collapseText: string = "Show less";
  export let showOnHover: boolean = false;
</script>

<div>
  <Fieldset title="Basic Usage">
    <div style="max-width: 600px">
      <ShowMore {maxHeight} {expanded} {expandText} {collapseText} {showOnHover}>
        <div class="content">
          <p>
            This is a component that allows content to be collapsed with a "Show more" button at the
            bottom.
          </p>
          <p>
            When the content exceeds the specified max height, it will be truncated with a gradient
            fade at the bottom.
          </p>
          <p>Clicking the button will expand the content to show everything.</p>
          <p>The component supports the following props:</p>
          <ul>
            <li>
              <code>maxHeight</code>: The maximum height of the content when collapsed (default:
              "200px")
            </li>
            <li>
              <code>expanded</code>: Whether the content is initially expanded (default: false)
            </li>
            <li>
              <code>expandText</code>: The text to show on the expand button (default: "Show more")
            </li>
            <li>
              <code>collapseText</code>: The text to show on the collapse button (default: "Show
              less")
            </li>
            <li>
              <code>showOnHover</code>: Whether to show the button only on hover (default: true)
            </li>
          </ul>
          <p>This component is useful for:</p>
          <ul>
            <li>Long descriptions that might take up too much space</li>
            <li>Content that is not immediately relevant to the user</li>
            <li>Sections where you want to provide a preview with the option to see more</li>
          </ul>
          <p>
            The component handles the transition animation automatically, providing a smooth user
            experience.
          </p>
        </div>
      </ShowMore>
    </div>
  </Fieldset>

  <Fieldset title="With Auto">
    <ShowMore maxHeight={200} {showOnHover}>
      <p>Here's an example of how to use the ShowMore component, there is no overflow :</p>
    </ShowMore>
  </Fieldset>
</div>

<style>
  .content {
    padding: var(--ds-spacing-3);
    background: var(--ds-panel-solid);
  }

  .content p {
    margin-bottom: var(--ds-spacing-2);
  }

  .content ul {
    margin-bottom: var(--ds-spacing-2);
    padding-left: var(--ds-spacing-4);
  }

  .content code {
    font-family: monospace;
    background: var(--ds-color-neutral-3);
    padding: 0 var(--ds-spacing-1);
    border-radius: var(--ds-radius-1);
  }
</style>
