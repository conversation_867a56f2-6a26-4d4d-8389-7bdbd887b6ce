/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON><PERSON>, StoryObj } from "@storybook/svelte";
import ToolUse from "$common-webviews/src/apps/chat/components/conversation/blocks/tools/ToolUse.svelte";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import mockSetupScript from "./mock-setup-script.json";
import mockScriptOutput from "./mock-script-output.json";
import { LocalToolType } from "$vscode/src/webview-providers/tool-types";
import { LONG_JS_FILE_CONTENT } from "./long_file_content";

// Long terminal command content for testing
const LONG_TERMINAL_COMMAND = `$ #!/bin/bash

# Long Bash Script Demonstration
echo "🚀 Starting Long Bash Script Demonstration..."
echo "================================================"

# Function definitions
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

create_test_files() {
    local dir=$1
    log_message "Creating test files in $dir"
    mkdir -p "$dir"
    for i in {1..5}; do
        echo "This is test file $i with some content" > "$dir/test_file_$i.txt"
        echo "Created on: $(date)" >> "$dir/test_file_$i.txt"
        echo "Random number: $RANDOM" >> "$dir/test_file_$i.txt"
    done
}

process_files() {
    local dir=$1
    log_message "Processing files in $dir"

    for file in "$dir"/*.txt; do
        if [[ -f "$file" ]]; then
            local word_count=$(wc -w < "$file")
            local line_count=$(wc -l < "$file")
            log_message "File $(basename "$file"): $line_count lines, $word_count words"
            sleep 0.5
        fi
    done
}

perform_calculations() {
    log_message "Performing mathematical calculations..."

    local sum=0
    for i in {1..50}; do
        sum=$((sum + i))
        if (( i % 10 == 0 )); then
            log_message "Progress: Calculated sum up to $i = $sum"
        fi
        sleep 0.1
    done

    log_message "Final sum of 1-50: $sum"
}`;

const meta = {
  title: "components/ToolUse/Terminal",
} satisfies Meta<typeof ToolUse>;

export default meta;

type Story = StoryObj<typeof ToolUse>;

export const SaveFile = {
  name: "ToolUse Save File",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "js-file-example",
      tool_name: LocalToolType.saveFile,
      input_json: JSON.stringify({
        file_path: "/path/to/example-component.jsx",
        file_content: LONG_JS_FILE_CONTENT,
      }),
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCli = {
  name: "ToolUse Shell CLI",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 3 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCli1 = {
  name: "ToolUse Shell CLI - Loading - Phase 1",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 1 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCli4 = {
  name: "ToolUse Shell CLI - Running - Phase 4",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 4 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCli5 = {
  name: "ToolUse Shell CLI - Success - Phase 5",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCli6 = {
  name: "ToolUse Shell CLI - Error - Phase 6",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 6 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCliLongCommand = {
  name: "ToolUse Shell CLI - Long Command",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "long-command",
      tool_name: SidecarToolType.shell,
      input_json: JSON.stringify({
        command_type: "complex",
        complex_command: LONG_TERMINAL_COMMAND,
      }),
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseSetupScript = {
  name: "ToolUse Setup Script",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: mockSetupScript,
    toolUseState: {
      phase: 5,
      requestId: "1",
      toolUseId: mockSetupScript.tool_use_id,
      result: {
        text: JSON.stringify(mockScriptOutput),
        isError: false,
      },
    },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;
