import { describe, test, expect, beforeEach, vi } from "vitest";
import {
  MCPServerModel,
  MCPServerError,
} from "$common-webviews/src/apps/settings/models/mcp-server-model";

describe("MCPServerModel", () => {
  let model: MCPServerModel;
  let mockHost: any;

  beforeEach(() => {
    // Create a mock host
    mockHost = {
      postMessage: vi.fn(),
      getState: vi.fn(),
      setState: vi.fn(),
    };

    // Create a new instance of the model with the mock host
    model = new MCPServerModel(mockHost);
  });

  describe("parseServerConfigFromJSON", () => {
    test("should parse an array of server configurations", () => {
      const jsonString = `[
        {
          "name": "Server 1",
          "command": "command1",
          "args": ["arg1", "arg2"]
        },
        {
          "name": "Server 2",
          "command": "command2",
          "args": ["arg3", "arg4"]
        }
      ]`;

      const result = model.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(2);
      expect(result[0].name).toBe("Server 1");
      expect(result[0].command).toBe("command1 arg1 arg2");
      expect(result[0].arguments).toBe("");
      expect(result[0].useShellInterpolation).toBe(true);

      expect(result[1].name).toBe("Server 2");
      expect(result[1].command).toBe("command2 arg3 arg4");
      expect(result[1].arguments).toBe("");
      expect(result[1].useShellInterpolation).toBe(true);
    });

    test("should parse an object with 'servers' property as an array", () => {
      const jsonString = `{
        "servers": [
          {
            "name": "Server 1",
            "command": "command1",
            "args": ["arg1", "arg2"]
          },
          {
            "name": "Server 2",
            "command": "command2",
            "args": ["arg3", "arg4"]
          }
        ]
      }`;

      const result = model.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(2);
      expect(result[0].name).toBe("Server 1");
      expect(result[0].command).toBe("command1 arg1 arg2");
      expect(result[1].name).toBe("Server 2");
      expect(result[1].command).toBe("command2 arg3 arg4");
    });

    test("should parse an object with 'mcpServers' property as an array", () => {
      const jsonString = `{
        "mcpServers": [
          {
            "name": "Server 1",
            "command": "command1",
            "args": ["arg1", "arg2"]
          }
        ]
      }`;

      const result = model.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe("Server 1");
      expect(result[0].command).toBe("command1 arg1 arg2");
    });

    test("should parse an object with 'servers' property as a record", () => {
      const jsonString = `{
        "servers": {
          "server-1": {
            "command": "command1",
            "args": ["arg1", "arg2"]
          },
          "server-2": {
            "command": "command2",
            "args": ["arg3", "arg4"]
          }
        }
      }`;

      const result = model.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(2);
      // Name should be taken from the key if not present in the config
      expect(result[0].name).toBe("server-1");
      expect(result[0].command).toBe("command1 arg1 arg2");
      expect(result[1].name).toBe("server-2");
      expect(result[1].command).toBe("command2 arg3 arg4");
    });

    test("should parse a Claude Desktop format with server names as keys", () => {
      const jsonString = `{
        "time-server": {
          "command": "uvx",
          "args": ["mcp-server-time"]
        },
        "echo-server": {
          "command": "uvx",
          "args": ["mcp-server-echo"]
        }
      }`;

      const result = model.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(2);
      expect(result[0].name).toBe("time-server");
      expect(result[0].command).toBe("uvx mcp-server-time");
      expect(result[1].name).toBe("echo-server");
      expect(result[1].command).toBe("uvx mcp-server-echo");
    });

    test("should parse a single server object", () => {
      const jsonString = `{
        "name": "Single Server",
        "command": "single-command",
        "args": ["arg1", "arg2"]
      }`;

      const result = model.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe("Single Server");
      expect(result[0].command).toBe("single-command arg1 arg2");
    });

    test("should handle environment variables", () => {
      const jsonString = `{
        "name": "Server With Env",
        "command": "command",
        "args": ["arg1"],
        "env": {
          "DEBUG": true,
          "PORT": 3000,
          "NULL_VALUE": null
        }
      }`;

      const result = model.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe("Server With Env");
      expect(result[0].command).toBe("command arg1");
      expect(result[0].env).toEqual({
        /* eslint-disable @typescript-eslint/naming-convention */
        DEBUG: "true",
        PORT: "3000",
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    });

    test("should use 'title' as name if 'name' is not provided", () => {
      const jsonString = `{
        "title": "Server Title",
        "command": "command"
      }`;

      const result = model.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe("Server Title");
    });

    test("should use command as name if neither 'name' nor 'title' is provided", () => {
      const jsonString = `{
        "command": "fallback-name-command"
      }`;

      const result = model.parseServerConfigFromJSON(jsonString);

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe("fallback-name-command");
    });

    test("should throw an error for invalid JSON", () => {
      const invalidJson = "{invalid: json}";

      expect(() => model.parseServerConfigFromJSON(invalidJson)).toThrow(MCPServerError);
    });

    test("should throw an error for unsupported format", () => {
      const invalidFormat = `"not an object or array"`;

      expect(() => model.parseServerConfigFromJSON(invalidFormat)).toThrow(MCPServerError);
    });

    test("should throw an error for JSON with invalid key format", () => {
      const jsonString = `{
        invalid key: {
          "command": "uvx",
          "args": ["mcp-server-time"]
        },
      }`;

      expect(() => model.parseServerConfigFromJSON(jsonString)).toThrow(MCPServerError);
    });
  });

  describe("getDuplicateServerIds", () => {
    test("should return empty set when there are no duplicate server names", () => {
      const servers = [
        {
          id: "1",
          name: "Server 1",
          command: "command1",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          id: "2",
          name: "Server 2",
          command: "command2",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          id: "3",
          name: "Server 3",
          command: "command3",
          arguments: "",
          useShellInterpolation: true,
        },
      ];

      const duplicateIds = MCPServerModel.parseDuplicateServerIds(servers);

      expect(duplicateIds.size).toBe(0);
    });

    test("should identify duplicate server names and return their IDs", () => {
      const servers = [
        {
          id: "1",
          name: "Server 1",
          command: "command1",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          id: "2",
          name: "Server 2",
          command: "command2",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          id: "3",
          name: "Server 1",
          command: "command3",
          arguments: "",
          useShellInterpolation: true,
        }, // Duplicate name
        {
          id: "4",
          name: "Server 3",
          command: "command4",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          id: "5",
          name: "Server 2",
          command: "command5",
          arguments: "",
          useShellInterpolation: true,
        }, // Duplicate name
      ];

      const duplicateIds = MCPServerModel.parseDuplicateServerIds(servers);

      expect(duplicateIds.size).toBe(2);
      expect(duplicateIds.has("3")).toBe(true); // Second occurrence of "Server 1"
      expect(duplicateIds.has("5")).toBe(true); // Second occurrence of "Server 2"
    });

    test("should identify all duplicates beyond the first occurrence", () => {
      const servers = [
        {
          id: "1",
          name: "Server 1",
          command: "command1",
          arguments: "",
          useShellInterpolation: true,
        },
        {
          id: "2",
          name: "Server 1",
          command: "command2",
          arguments: "",
          useShellInterpolation: true,
        }, // Duplicate
        {
          id: "3",
          name: "Server 1",
          command: "command3",
          arguments: "",
          useShellInterpolation: true,
        }, // Duplicate
        {
          id: "4",
          name: "Server 2",
          command: "command4",
          arguments: "",
          useShellInterpolation: true,
        },
      ];

      const duplicateIds = MCPServerModel.parseDuplicateServerIds(servers);

      expect(duplicateIds.size).toBe(2);
      expect(duplicateIds.has("2")).toBe(true); // Second occurrence of "Server 1"
      expect(duplicateIds.has("3")).toBe(true); // Third occurrence of "Server 1"
    });

    test("should handle empty server array", () => {
      const servers: any[] = [];
      const duplicateIds = MCPServerModel.parseDuplicateServerIds(servers);

      expect(duplicateIds.size).toBe(0);
    });
  });
});
