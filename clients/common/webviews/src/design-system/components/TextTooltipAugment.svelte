<script lang="ts">
  import "@radix-ui/colors/gray.css";
  import "@radix-ui/colors/gray-dark.css";
  import Tooltip from "$common-webviews/src/design-system/_primitives/TooltipAugment";
  import type TooltipRoot from "$common-webviews/src/design-system/_primitives/TooltipAugment/Root.svelte";
  import type {
    TooltipTriggerOn,
    TooltipContentSide,
  } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import TextAugment from "./TextAugment.svelte";

  export let content: string | undefined = undefined;
  export let width: string | undefined = undefined;
  export let minWidth: string | undefined = undefined;
  export let maxWidth: string = "360px";
  export let delayDurationMs: number | undefined = undefined;
  export let triggerOn: TooltipTriggerOn[] | undefined = undefined;
  export let side: TooltipContentSide = "top";
  export let nested: boolean | undefined = undefined;
  export let hasPointerEvents: boolean | undefined = undefined;
  export let offset: [number, number] | undefined = undefined;

  export let align: "start" | "center" | "end" = "center";
  let className: string = "";
  export { className as class };

  export let onOpenChange: ((open: boolean) => void) | undefined = undefined;
  export let referenceClientRect: DOMRect | undefined = undefined;

  // Public API for external requests for control
  let tooltip: TooltipRoot | undefined = undefined;
  export const requestOpen = () => tooltip?.requestOpen();
  export const requestClose = () => tooltip?.requestClose();
</script>

<Tooltip.Root
  bind:this={tooltip}
  {delayDurationMs}
  {onOpenChange}
  {triggerOn}
  {nested}
  {hasPointerEvents}
  {offset}
  tippyTheme="default text-tooltip-augment"
>
  <Tooltip.Trigger {referenceClientRect} class={className}>
    <slot />
  </Tooltip.Trigger>
  {#if content || $$slots.content}
    <Tooltip.Content {side} {align}>
      <div style:width style:min-width={minWidth} style:max-width={maxWidth}>
        {#if $$slots.content}
          <slot name="content" />
        {:else}
          <TextAugment size={1} class="tooltip-text">
            {content}
          </TextAugment>
        {/if}
      </div>
    </Tooltip.Content>
  {/if}
</Tooltip.Root>

<style>
  :global(.tippy-box[data-theme~="text-tooltip-augment"] .c-text) {
    display: flex;
    align-items: center;
  }

  :global(.tippy-box[data-theme~="text-tooltip-augment"] .tooltip-text) {
    white-space: normal;
    word-break: break-word;
    overflow-wrap: break-word;
  }
  :global(.tippy-box[data-theme~="text-tooltip-augment"]:has(.l-tooltip-contents)) {
    --text-tooltip-background-color: var(--gray-12);
    --text-tooltip-text-color: var(--gray-1);

    background-color: var(--text-tooltip-background-color);
    border-radius: var(--ds-radius-3);

    & .tippy-content {
      hyphens: auto;
      padding: var(--ds-spacing-1) var(--ds-spacing-2);
      color: var(--text-tooltip-text-color);
      white-space: normal;
      word-break: break-word;
      overflow-wrap: break-word;
      overflow-y: auto;
    }

    & .tippy-arrow {
      color: var(--text-tooltip-background-color);

      &::before {
        transform: scale(0.625);
      }
    }
  }
</style>
