<script lang="ts">
  import { type Readable, writable } from "svelte/store";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-down.svg?component";
  import type { Rule } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
  import ArrowRight from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-right.svg?component";
  import { onMount } from "svelte";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let onRuleSelected: (rule: Rule) => void;
  export let disabled: boolean = false;

  // State management
  const rulesFiles = writable<Rule[]>([]);
  const loading = writable<boolean>(true);
  const selectedRule = writable<Rule | undefined>(undefined);
  let focusedIndex: Readable<number | undefined> | undefined = undefined;
  let requestClose: () => void = () => {};

  // Load rules on component mount
  onMount(() => {
    loadRules();

    // Listen for rules list response
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === WebViewMessageType.getRulesListResponse) {
        rulesFiles.set(event.data.data || []);
        loading.set(false);
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  });

  // Function to load rules from the extension
  function loadRules() {
    loading.set(true);
    host.postMessage({
      type: WebViewMessageType.getRulesListRequest,
      data: { query: "", maxResults: 100 },
    });
  }

  // Handle dropdown selection
  function handleRuleSelect(rule: Rule): void {
    selectedRule.set(rule);
    onRuleSelected(rule);
    requestClose();
  }

  // Get rule description based on path
  function getRuleDescription(rule: Rule): string {
    return `Move to ${rule.path}`;
  }
</script>

{#if !$loading && $rulesFiles.length > 0}
  <DropdownMenuAugment.Root bind:requestClose bind:focusedIndex>
    <DropdownMenuAugment.Trigger>
      <ButtonAugment color="neutral" variant="soft" size={1} {disabled}>
        <ArrowRight slot="iconLeft" />
        {$selectedRule ? $selectedRule.path : "Rules"}
        <ChevronDown slot="iconRight" />
      </ButtonAugment>
    </DropdownMenuAugment.Trigger>
    <DropdownMenuAugment.Content side="bottom" align="start">
      {#each $rulesFiles as rule, index}
        <DropdownMenuAugment.Item
          onSelect={() => handleRuleSelect(rule)}
          highlight={$focusedIndex === index}
        >
          <Filespan filepath={rule.path} />
        </DropdownMenuAugment.Item>
      {/each}
      {#if $focusedIndex !== undefined && $rulesFiles[$focusedIndex]}
        <DropdownMenuAugment.Separator />
        <DropdownMenuAugment.Label>
          <TextAugment size={1} color="neutral">
            {getRuleDescription($rulesFiles[$focusedIndex])}
          </TextAugment>
        </DropdownMenuAugment.Label>
      {/if}
    </DropdownMenuAugment.Content>
  </DropdownMenuAugment.Root>
{/if}
