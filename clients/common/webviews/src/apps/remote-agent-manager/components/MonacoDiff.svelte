<script lang="ts">
  import { on<PERSON><PERSON><PERSON>, onMount, createEventDispatcher } from "svelte";
  import type * as Monaco from "monaco-editor/esm/vs/editor/editor.api";
  import type { DiffExplanationSubSectionDescription } from "$vscode/src/remote-agent-manager/types";
  import { writable, type Writable } from "svelte/store";
  import MarkdownWithColors from "$common-webviews/src/common/components/markdown/MarkdownWithColors.svelte";
  import { MonacoContext } from "$common-webviews/src/design-system/components/MonacoProvider";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import VsCodeCodicon from "$common-webviews/src/common/components/vscode/VSCodeCodicon.svelte";
  import { getMonacoLayoutManager } from "$common-webviews/src/common/utils/monaco-layout-manager";

  // Event dispatcher for code changes and UI events
  const dispatch = createEventDispatcher<{
    codeChange: { modifiedCode: string };
  }>();

  // Component props
  export let originalCode = "";
  export let modifiedCode = "";
  export let path: string | undefined;
  export let descriptions: DiffExplanationSubSectionDescription[] = [];
  export let lineOffset: number = 0;
  export let extraPrefixLines: string[] = [];
  export let extraSuffixLines: string[] = [];
  export let theme: string;
  export let areDescriptionsVisible: boolean = true; // New prop to control description visibility
  export let isNewFile: boolean = false; // New prop to identify if this is a new file
  export let isDeletedFile: boolean = false; // New prop to identify if this is a deleted file

  const monacoContext = MonacoContext.getContext();
  const monaco = monacoContext.monaco;

  // Editor state
  let diffEditor: Monaco.editor.IDiffEditor | undefined;
  let singleEditor: Monaco.editor.IStandaloneCodeEditor | undefined;
  let originalModel: Monaco.editor.ITextModel | undefined;
  let modifiedModel: Monaco.editor.ITextModel | undefined;
  let scrollListeners: Monaco.IDisposable[] = [];
  let editorContainer: HTMLElement;

  const layoutManager = getMonacoLayoutManager();
  let unregisterEditor: (() => void) | undefined;

  // UI state
  let scrollY = writable(0);
  let heightInPx = isNewFile ? modifiedCode.split("\n").length * 20 + 40 : 100;

  // Get the list of supported languages from Monaco
  const supportedLanguages: string[] = $monaco
    ? $monaco.languages.getLanguages().map((lang) => lang.id)
    : [];

  /**
   * Detect language for code using the same approach as SimpleMonaco
   */
  function detectLanguage(_code: string, path?: string): string | undefined {
    // First try to get language from file extension if path is provided
    if (path) {
      const extension = path.split(".").pop()?.toLowerCase();
      if (extension) {
        // Try to find a language that matches this extension
        const language = $monaco?.languages
          .getLanguages()
          .find((lang) => lang.extensions?.includes("." + extension))?.id;

        if (language && supportedLanguages.includes(language)) {
          return language;
        }
      }
    }

    // If no language found from extension, use HLJS to auto-detect
    return "plaintext";
  }

  // Store for description positions
  // This allows us to calculate positions once and reuse them
  // We'll update these positions when the editor loads and when content changes
  type PositionMap = { [id: number]: number };
  const descriptionPositions: Writable<PositionMap> = writable({});

  // Track timeout IDs for cleanup
  let editorHeightTimeout: ReturnType<typeof setTimeout> | null = null;

  /**
   * Clean up resources when component is destroyed
   */
  onDestroy(() => {
    diffEditor?.dispose();
    singleEditor?.dispose();
    originalModel?.dispose();
    modifiedModel?.dispose();
    scrollListeners.forEach((listener) => listener.dispose());
    // Clear any pending timeouts
    if (editorHeightTimeout) {
      clearTimeout(editorHeightTimeout);
    }
    unregisterEditor?.();
  });

  /**
   * Set up scroll synchronization between editor and descriptions
   */
  function setupScrollSync() {
    if (!diffEditor) return;

    // Clean up any existing listeners
    scrollListeners = scrollListeners.filter((listener) => {
      listener.dispose();
      return false;
    });

    // Get both editors
    const originalEditor = diffEditor.getOriginalEditor();
    const modifiedEditor = diffEditor.getModifiedEditor();

    // Add scroll listeners to both editors
    scrollListeners.push(
      originalEditor.onDidScrollChange(() => {
        $scrollY = originalEditor.getScrollTop();
      }),
      modifiedEditor.onDidScrollChange(() => {
        $scrollY = modifiedEditor.getScrollTop();
      }),
    );
  }

  /**
   * Set up all event listeners for editor height updates
   */
  function setupEditorListeners() {
    if (!diffEditor || !editorContainer) return;

    const originalEditor = diffEditor.getOriginalEditor();
    const modifiedEditor = diffEditor.getModifiedEditor();

    // Standard Monaco editor events
    scrollListeners.push(
      // Content size changes
      modifiedEditor.onDidContentSizeChange(() => layoutManager.requestLayout()),
      originalEditor.onDidContentSizeChange(() => layoutManager.requestLayout()),

      // Diff updates
      diffEditor.onDidUpdateDiff(() => layoutManager.requestLayout()),

      // Hidden areas changes
      modifiedEditor.onDidChangeHiddenAreas(() => layoutManager.requestLayout()),
      originalEditor.onDidChangeHiddenAreas(() => layoutManager.requestLayout()),

      // Layout changes
      modifiedEditor.onDidLayoutChange(() => layoutManager.requestLayout()),
      originalEditor.onDidLayoutChange(() => layoutManager.requestLayout()),

      // Focus tracking
      modifiedEditor.onDidFocusEditorWidget(() => {
        updateScrollOptions(true);
      }),
      originalEditor.onDidFocusEditorWidget(() => {
        updateScrollOptions(true);
      }),
      modifiedEditor.onDidBlurEditorWidget(() => {
        updateScrollOptions(false);
      }),
      originalEditor.onDidBlurEditorWidget(() => {
        updateScrollOptions(false);
      }),

      // Listen for content changes in the modified editor
      modifiedEditor.onDidChangeModelContent(() => {
        // Mark that the user is editing
        isUserEditing = true;
        lastUserEditTime = Date.now();

        const currentModifiedCode = modifiedModel?.getValue() || "";

        // Only dispatch if the code has actually changed
        if (currentModifiedCode === modifiedCode) return;

        // Remove prefix and suffix before dispatching
        const cleanModifiedCode = currentModifiedCode
          .replace(extraPrefixLines.join(""), "")
          .replace(extraSuffixLines.join(""), "");

        dispatch("codeChange", { modifiedCode: cleanModifiedCode });

        // Reset the editing flag after a short delay
        const editTimeout = setTimeout(() => {
          isUserEditing = false;
        }, 500);
        scrollListeners.push({ dispose: () => clearTimeout(editTimeout) });
      }),
    );

    // Set up listeners for unchanged region controls
    setupUnchangedRegionControls();
  }

  // Debounce function to prevent too frequent height recalculations
  let heightUpdateTimeout: ReturnType<typeof setTimeout> | null = null;

  /**
   * Debounced version of updateEditorHeight to prevent too frequent recalculations
   */
  function debouncedUpdateHeight() {
    if (heightUpdateTimeout) {
      clearTimeout(heightUpdateTimeout);
    }

    heightUpdateTimeout = setTimeout(() => {
      layoutManager.requestLayout();
      heightUpdateTimeout = null;
    }, 100);

    // Store for cleanup
    if (heightUpdateTimeout) {
      scrollListeners.push({
        dispose: () => {
          if (heightUpdateTimeout) {
            clearTimeout(heightUpdateTimeout);
            heightUpdateTimeout = null;
          }
        },
      });
    }
  }

  /**
   * Set up event listeners specifically for unchanged region controls
   * This is a simplified version that only adds a single click listener to the container
   */
  function setupUnchangedRegionControls() {
    if (!editorContainer || !diffEditor) return;

    // Wait for the editor to fully render
    if (editorHeightTimeout) {
      clearTimeout(editorHeightTimeout);
    }
    editorHeightTimeout = setTimeout(() => {
      // Add a single delegated click listener to the editor container
      // This is more efficient than adding listeners to each control
      if (!(editorContainer as any).__hasClickListener) {
        const clickHandler = (event: MouseEvent) => {
          const target = event.target as Element;

          // Check if the clicked element is related to unchanged regions
          if (
            target &&
            (target.closest('[title="Show Unchanged Region"]') ||
              target.closest('[title="Hide Unchanged Region"]'))
          ) {
            debouncedUpdateHeight();
          }
        };

        editorContainer.addEventListener("click", clickHandler);
        (editorContainer as any).__hasClickListener = true;

        // Store for cleanup
        scrollListeners.push({
          dispose: () => {
            editorContainer.removeEventListener("click", clickHandler);
          },
        });
      }

      // Also listen for the didUpdateDiff event from Monaco
      // This is more reliable than trying to detect DOM changes
      if (diffEditor) {
        scrollListeners.push(
          diffEditor.onDidUpdateDiff(() => {
            debouncedUpdateHeight();
          }),
        );
      }
    }, 300);
  }

  /**
   * Initialize the editor when the component is mounted
   */
  onMount(() => {
    if (!$monaco) {
      console.error("Monaco not loaded. Diff view cannot be initialized.");
      return;
    }

    if (isNewFile) {
      // For new files, create a regular editor instead of a diff editor
      singleEditor = $monaco.editor.create(editorContainer, {
        automaticLayout: true,
        stickyScroll: { enabled: true },
        scrollBeyondLastLine: false,
        minimap: { enabled: false },
        overviewRulerBorder: false,
        theme,
        // Prevent scrolling when not focused
        scrollbar: {
          alwaysConsumeMouseWheel: false,
          handleMouseWheel: false,
        },
        lineNumbers: (n) => `${lineOffset - extraPrefixLines.length + n}`,
      });

      // Create a model for the new file with language detection based on file extension
      const language = detectLanguage(modifiedCode, path);
      modifiedModel = $monaco.editor.createModel(
        modifiedCode,
        language,
        path !== undefined
          ? $monaco.Uri.parse("file://" + path + `#${crypto.randomUUID()}`)
          : undefined,
      );

      // Set the model
      singleEditor.setModel(modifiedModel);

      // Listen for content changes
      scrollListeners.push(
        singleEditor.onDidChangeModelContent(() => {
          // Mark that the user is editing
          isUserEditing = true;
          lastUserEditTime = Date.now();

          const currentModifiedCode = modifiedModel?.getValue() || "";

          // Only dispatch if the code has actually changed
          if (currentModifiedCode === modifiedCode) return;

          dispatch("codeChange", { modifiedCode: currentModifiedCode });

          // Reset the editing flag after a short delay
          const editTimeout = setTimeout(() => {
            isUserEditing = false;
          }, 500);
          scrollListeners.push({ dispose: () => clearTimeout(editTimeout) });
        }),
      );

      // Focus tracking
      scrollListeners.push(
        singleEditor.onDidFocusEditorWidget(() => {
          singleEditor?.updateOptions({
            scrollbar: {
              handleMouseWheel: true,
            },
          });
        }),
        singleEditor.onDidBlurEditorWidget(() => {
          singleEditor?.updateOptions({
            scrollbar: {
              handleMouseWheel: false,
            },
          });
        }),
      );

      // Calculate initial height
      const contentHeight = singleEditor.getContentHeight();
      heightInPx = Math.max(contentHeight, 60); // Minimum height of 60px

      // Force the editor to layout with the new height
      const layoutTimeout = setTimeout(() => {
        singleEditor?.layout();
      }, 0);
      scrollListeners.push({ dispose: () => clearTimeout(layoutTimeout) });
    } else {
      // For modified files, create a diff editor
      diffEditor = $monaco.editor.createDiffEditor(editorContainer, {
        automaticLayout: true,
        useInlineViewWhenSpaceIsLimited: true,
        enableSplitViewResizing: true,
        stickyScroll: { enabled: true },
        scrollBeyondLastLine: false,
        minimap: { enabled: false },
        renderOverviewRuler: false,
        renderGutterMenu: false,
        theme,
        // Prevent scrolling when not focused
        scrollbar: {
          alwaysConsumeMouseWheel: false,
          handleMouseWheel: false,
        },
        lineNumbers: (n) => `${lineOffset - extraPrefixLines.length + n}`,
        hideUnchangedRegions: {
          enabled: true,
          revealLineCount: 3,
          minimumLineCount: 3,
          contextLineCount: 3,
        },
      });

      if (unregisterEditor) {
        unregisterEditor();
      }
      unregisterEditor = layoutManager.registerEditor({
        editor: diffEditor,
        updateHeight: updateEditorHeight,
        id: `monaco-diff-${crypto.randomUUID().slice(0, 8)}`, // Short ID for debugging
      });

      // Set up the initial models
      updateModels(path, originalCode, modifiedCode, extraPrefixLines, extraSuffixLines);

      // Set up scroll synchronization
      setupScrollSync();

      // Set up all editor event listeners
      setupEditorListeners();

      // Calculate initial height and description positions after a short delay
      if (editorHeightTimeout) {
        clearTimeout(editorHeightTimeout);
      }
      editorHeightTimeout = setTimeout(() => {
        layoutManager.requestLayout();
        editorHeightTimeout = null;
      }, 100);
    }
  });

  /**
   * Update the editor models with new code
   */
  function updateModels(
    path: string | undefined,
    originalCode: string | undefined,
    modifiedCode: string | undefined,
    extraPrefixLines: string[] = [],
    extraSuffixLines: string[] = [],
  ) {
    if (!$monaco) {
      console.error("Monaco not loaded. Diff view cannot be updated.");
      return;
    }

    // Clean up existing models
    originalModel?.dispose();
    modifiedModel?.dispose();

    // Prepare the code with prefix and suffix lines
    originalCode = originalCode || "";
    modifiedCode = modifiedCode || "";
    const extraPrefix = extraPrefixLines.join("");
    const extraSuffix = extraSuffixLines.join("");

    // For new files, we don't want to show a red line at the top
    if (isNewFile) {
      // For new files, use a space character for original code
      // This prevents the red line while still showing the green additions
      originalCode = modifiedCode
        .split("\n")
        .map(() => " ")
        .join("\n");
    } else {
      originalCode = extraPrefix + originalCode + extraSuffix;
    }

    modifiedCode = extraPrefix + modifiedCode + extraSuffix;

    // Monaco requires all URIs passed here to be unique, so we append identifiers
    originalModel = $monaco.editor.createModel(
      originalCode,
      undefined,
      path !== undefined
        ? $monaco.Uri.parse("file://" + path + `#${crypto.randomUUID()}`)
        : undefined,
    );
    if (isDeletedFile) {
      // For deleted files, use a space character for modified code
      // This prevents the green line while still showing the red deletions
      modifiedCode = modifiedCode
        .split("\n")
        .map(() => " ")
        .join("\n");
    }
    modifiedModel = $monaco.editor.createModel(
      modifiedCode,
      undefined,
      path !== undefined
        ? $monaco.Uri.parse("file://" + path + `#${crypto.randomUUID()}`)
        : undefined,
    );

    if (diffEditor) {
      // Set the new models
      diffEditor.setModel({
        original: originalModel,
        modified: modifiedModel,
      });
      // Hide line numbers for the original editor
      const originalEditor = diffEditor.getOriginalEditor();
      if (originalEditor) {
        originalEditor.updateOptions({ lineNumbers: "off" });
      }

      // Re-setup scroll sync when models change
      setupScrollSync();

      // Re-setup event listeners after a short delay
      if (editorHeightTimeout) {
        clearTimeout(editorHeightTimeout);
      }
      editorHeightTimeout = setTimeout(() => {
        setupEditorListeners();
        editorHeightTimeout = null;
      }, 300);
    }
  }

  // Track if the user is currently editing
  let isUserEditing = false;
  let lastUserEditTime = 0;
  const USER_EDIT_COOLDOWN = 1000; // 1 second cooldown after user edits

  // Function to check if we should update the model based on prop changes
  function shouldUpdateModel(newModifiedCode: string): boolean {
    // If user is actively editing, don't update the model
    if (isUserEditing) return false;

    // If we recently had a user edit, don't update
    if (Date.now() - lastUserEditTime < USER_EDIT_COOLDOWN) return false;

    // If the model doesn't exist yet, we should update
    if (!modifiedModel) return true;

    // Get the current content from the model
    const currentContent = modifiedModel.getValue();
    const extraPrefix = extraPrefixLines.join("");
    const extraSuffix = extraSuffixLines.join("");
    const expectedContent = extraPrefix + newModifiedCode + extraSuffix;

    // Only update if the content is different and not due to user edits
    return currentContent !== expectedContent;
  }

  // Reactivity block to update models when props change
  $: if (shouldUpdateModel(modifiedCode)) {
    if (isNewFile && singleEditor) {
      // For new files, just update the model content
      if (modifiedModel) {
        modifiedModel.setValue(modifiedCode);
      } else {
        // Create a new model if it doesn't exist with language detection
        const language = detectLanguage(modifiedCode, path);
        if ($monaco) {
          modifiedModel = $monaco.editor.createModel(
            modifiedCode,
            language,
            path !== undefined
              ? $monaco.Uri.parse("file://" + path + `#${crypto.randomUUID()}`)
              : undefined,
          );
        }
        if (modifiedModel) {
          singleEditor.setModel(modifiedModel);
        }
      }

      // Update height for the single editor
      heightInPx = modifiedCode.split("\n").length * 20 + 40;
      singleEditor.layout();
    } else if (!isNewFile && diffEditor) {
      // For modified files, update the diff models
      updateModels(path, originalCode, modifiedCode, extraPrefixLines, extraSuffixLines);
      layoutManager.requestLayout();
    }
  }

  // Update description positions when descriptions change
  $: if (diffEditor && descriptions.length > 0) {
    updateDescriptionPositions();
  }

  // Update height for new files when modifiedCode changes
  $: if (isNewFile && modifiedCode && singleEditor) {
    const contentHeight = singleEditor.getContentHeight();
    heightInPx = Math.max(contentHeight, 60); // Minimum height of 60px
    singleEditor.layout();
  }

  /**
   * Get the visible position for a line in the editor
   * This is a simplified version that just uses Monaco's built-in method
   * We don't need the complex logic anymore since we're only checking the last visible lines
   */
  function getVisibleLinePosition(lineNumber: number, useModifiedEditor: boolean = true): number {
    if (!diffEditor) return lineNumber * 18; // Fallback

    // Get the appropriate editor
    const editorInstance = useModifiedEditor
      ? diffEditor.getModifiedEditor()
      : diffEditor.getOriginalEditor();

    // Use Monaco's built-in method to get the position for a line number
    return editorInstance.getTopForLineNumber(lineNumber);
  }

  /**
   * Update the editor height based on content
   * Accounts for hidden lines in the diff view
   * Prevents last line from being scrolled to the top of the viewport
   * Only considers changed lines for height calculation
   * Optimized for performance
   */
  function updateEditorHeight() {
    if (!diffEditor) return;

    // Get both editors and their models
    const model = diffEditor.getModel();
    const originalModel = model?.original;
    const modifiedModel = model?.modified;

    if (!originalModel || !modifiedModel) return;

    // Get the editors
    const originalEditor = diffEditor.getOriginalEditor();
    const modifiedEditor = diffEditor.getModifiedEditor();

    // Get all line changes from the diff editor
    const lineChanges = diffEditor.getLineChanges() || [];

    // Calculate height based on visible content
    let calculatedHeight: number;

    if (lineChanges.length === 0) {
      // If there are no changes, use the content height directly
      // This is more efficient than calculating positions for each line
      const originalContentHeight = originalEditor.getContentHeight();
      const modifiedContentHeight = modifiedEditor.getContentHeight();

      // Use the maximum of the two content heights, or a minimum height
      const minHeight = 100; // Minimum height when no changes or very little content
      calculatedHeight = Math.max(minHeight, originalContentHeight, modifiedContentHeight);
    } else {
      // For files with changes, we'll optimize by only checking the last visible change
      // This is much more efficient than checking every line position

      // Find the last visible change in both editors
      let lastOriginalLine = 0;
      let lastModifiedLine = 0;

      // Process each line change to find the last visible lines
      for (const change of lineChanges) {
        // Update the last visible lines
        if (change.originalEndLineNumber > 0) {
          lastOriginalLine = Math.max(lastOriginalLine, change.originalEndLineNumber);
        }

        if (change.modifiedEndLineNumber > 0) {
          lastModifiedLine = Math.max(lastModifiedLine, change.modifiedEndLineNumber);
        }
      }

      // Add a few lines for context
      lastOriginalLine = Math.min(lastOriginalLine + 3, originalModel.getLineCount());
      lastModifiedLine = Math.min(lastModifiedLine + 3, modifiedModel.getLineCount());

      // Get the positions for the last visible lines
      const originalPos = originalEditor.getTopForLineNumber(lastOriginalLine);
      const modifiedPos = modifiedEditor.getTopForLineNumber(lastModifiedLine);

      // Use the maximum position plus padding
      calculatedHeight = Math.max(originalPos, modifiedPos) + 60;
    }

    // Apply a maximum height limit to prevent the editor from becoming too tall
    const maxHeight = 20000; // Maximum height in pixels

    // Update the height with the limit applied
    heightInPx = Math.min(calculatedHeight, maxHeight);

    // Force the editor to layout with the new height
    diffEditor.layout();

    // Update description positions
    updateDescriptionPositions();
  }

  /**
   * Update the editor's scroll options based on focus state
   */
  function updateScrollOptions(isFocused: boolean) {
    if (!diffEditor) return;

    const originalEditor = diffEditor.getOriginalEditor();
    const modifiedEditor = diffEditor.getModifiedEditor();

    // Update scrollbar options based on focus state
    originalEditor.updateOptions({
      scrollbar: {
        handleMouseWheel: isFocused,
      },
    });

    modifiedEditor.updateOptions({
      scrollbar: {
        handleMouseWheel: isFocused,
      },
    });
  }

  /**
   * Get the position for a description based on its line number
   */
  function getDescriptionPosition(description: DiffExplanationSubSectionDescription): number {
    if (!diffEditor) return 0;

    // Get both editors and their models
    const model = diffEditor.getModel();
    const originalModel = model?.original;
    const modifiedModel = model?.modified;

    if (!originalModel || !modifiedModel) return 0;

    // Get the top position of the last line in both editors using our helper function
    const originalLastLineTop = getVisibleLinePosition(description.range.start + 1, false);
    const modifiedLastLineTop = getVisibleLinePosition(description.range.start + 1, true);

    if (originalLastLineTop && !modifiedLastLineTop) return originalLastLineTop;
    if (!originalLastLineTop && modifiedLastLineTop) return modifiedLastLineTop;

    return Math.min(originalLastLineTop, modifiedLastLineTop);
  }

  /**
   * Update all description positions and store them
   * This is called when the editor loads and when content changes
   */
  function updateDescriptionPositions(): void {
    if (!diffEditor || descriptions.length === 0) return;

    // Calculate positions for all descriptions
    const positions: PositionMap = {};

    descriptions.forEach((description, index) => {
      positions[index] = getDescriptionPosition(description);
    });

    descriptionPositions.set(positions);
  }
</script>

<div
  class="monaco-diff-container"
  class:monaco-diff-container-with-descriptions={descriptions.length > 0 && areDescriptionsVisible}
>
  <!-- Monaco editor container - used for both diff and regular editor -->
  <div bind:this={editorContainer} class="editor-container" style="height: {heightInPx}px"></div>

  <!-- Description annotations that follow editor scroll -->
  {#if descriptions.length > 0}
    <div class="toggle-button">
      <IconButtonAugment
        variant="ghost"
        color="neutral"
        size={1}
        on:click={() => (areDescriptionsVisible = !areDescriptionsVisible)}
      >
        {#if areDescriptionsVisible}
          <VsCodeCodicon icon="x" />
        {:else}
          <VsCodeCodicon icon="book" />
        {/if}
      </IconButtonAugment>
    </div>
    <div class="descriptions" style="transform: translateY({-$scrollY}px)">
      {#each descriptions as description, index}
        <div
          class="description"
          style="top: {$descriptionPositions[index] || getDescriptionPosition(description)}px;
          --ds-panel-solid: transparent;"
        >
          <MarkdownWithColors markdown={description.text} />
        </div>
      {/each}
    </div>
  {/if}
</div>

<style>
  .monaco-diff-container {
    position: relative;
    --description-sidebar: 0;
  }
  .descriptions {
    display: none;
    overflow: hidden;
  }
  .toggle-button {
    display: none;
  }
  @media (min-width: 1000px) {
    .monaco-diff-container-with-descriptions {
      --description-sidebar: 200px;
    }
    .descriptions {
      display: flex;
    }
    .toggle-button {
      display: flex;
    }
  }
  @media (min-width: 1200px) {
    .monaco-diff-container-with-descriptions {
      --description-sidebar: 300px;
    }
  }

  .editor-container {
    width: 100%;
    overflow: auto;
    padding-right: var(--description-sidebar);
    transition: padding-right 0.2s ease;
  }

  .descriptions {
    position: absolute;
    right: 0;
    top: 0;
    width: var(--description-sidebar);
    height: 100%;
    transition: width 0.2s ease;
  }

  .description {
    position: absolute;
    left: 0;
    right: 0;
    background: var(--collapsible-panel-background);
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    margin-top: calc(0px - var(--ds-spacing-1));
    border-radius: var(--ds-border-radius-1);
    margin-bottom: var(--ds-spacing-2);
    font-size: 0.8rem;
  }
  .description:hover {
    z-index: 10;
  }

  .description :global(p) {
    line-height: 1.3em;
  }

  .toggle-button {
    position: absolute;
    right: -5px;
    top: 3px;
    z-index: 20;
  }
</style>
