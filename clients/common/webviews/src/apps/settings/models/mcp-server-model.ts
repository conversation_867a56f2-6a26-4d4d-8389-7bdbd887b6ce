import { get, writable, type Writable } from "svelte/store";
import type { HostInterface } from "$common-webviews/src/common/hosts/host-types";
import {
  type MCPServer,
  type WebViewMessage,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import type { MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import { z } from "zod";

/**
 * Base schema for server configuration properties.
 *
 * These are common properties that might be present in any server config.
 */
const baseServerConfigSchema = z
  .object({
    name: z.string().optional(),
    title: z.string().optional(),
    command: z.string().optional(),
    args: z.array(z.union([z.string(), z.number(), z.boolean()])).optional(),
    env: z
      .record(z.union([z.string(), z.number(), z.boolean(), z.null(), z.undefined()]))
      .optional(),
  })
  .passthrough(); // Allow additional properties

// Type for a single server config
type ImportedServerConfig = z.infer<typeof baseServerConfigSchema>;

// Type definition for a normalized MCPServer without an ID
type NormalizedMCPServer = Omit<MCPServer, "id">;

/**
 * Format 1: Array of server configs
 */
const arrayOfServersSchema = z.array(baseServerConfigSchema);

/**
 * Format 2: Object with 'servers' property as an array
 */
const serversArrayContainerSchema = z
  .object({
    servers: z.array(baseServerConfigSchema),
  })
  .passthrough();

/**
 * Format 3: Object with 'mcpServers' property as an array
 */
const mcpServersArrayContainerSchema = z
  .object({
    mcpServers: z.array(baseServerConfigSchema),
  })
  .passthrough();

/**
 * Format 4: Object with 'servers' property as a record
 */
const serversRecordContainerSchema = z
  .object({
    servers: z.record(baseServerConfigSchema),
  })
  .passthrough();

/**
 * Format 5: Object with 'mcpServers' property as a record
 */
const mcpServersRecordContainerSchema = z
  .object({
    mcpServers: z.record(baseServerConfigSchema),
  })
  .passthrough();

/**
 * Format 6: Claude Desktop format (direct key-value pairs of server configs)
 * We'll validate this by checking if at least one value has command
 */
const claudeDesktopSchema = z.record(baseServerConfigSchema);

/**
 * Format 7: Single server object
 */
const singleServerSchema = baseServerConfigSchema.refine(
  (data: ImportedServerConfig) => data.command !== undefined,
  {
    message: "Server must have a 'command' property",
  },
);

const mcpServerSymbol: unique symbol = Symbol("MCPServerError");

export function isMCPServerError(error: unknown): error is MCPServerError {
  return error === null || error === undefined
    ? false
    : typeof error === "object"
      ? error instanceof MCPServerError || mcpServerSymbol in (error.constructor || error)
      : false;
}

export class MCPServerError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "MCPServerError";
    Object.setPrototypeOf(this, MCPServerError.prototype);
  }
}

export class MCPServerModel implements MessageConsumer {
  static [mcpServerSymbol] = "MCPServerError";
  private servers: Writable<MCPServer[]> = writable([]);

  constructor(private readonly host: HostInterface) {
    this.loadServersFromStorage();
  }

  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    const message = e.data;
    if (message.type === WebViewMessageType.getStoredMCPServersResponse) {
      const storedServers: MCPServer[] = message.data;
      if (Array.isArray(storedServers)) {
        // Just set the servers without triggering a save
        this.servers.set(storedServers);
      }
      return true;
    }
    return false;
  }

  /**
   * Sends a request to import MCP servers from JSON
   * @param jsonString The JSON string containing MCP server configurations
   */
  async importServersFromJSON(jsonString: string): Promise<number> {
    return this.importFromJSON(jsonString);
  }

  private loadServersFromStorage() {
    try {
      // Request stored servers from the host
      this.host.postMessage({
        type: WebViewMessageType.getStoredMCPServers,
      });
    } catch (error) {
      console.error("Failed to load MCP servers:", error);
      // Initialize with empty array if loading fails
      this.servers.set([]);
    }
  }

  private saveServers(servers: MCPServer[]) {
    try {
      // Save servers to storage
      this.host.postMessage({
        type: WebViewMessageType.setStoredMCPServers,
        data: servers,
      });
    } catch (error) {
      console.error("Failed to save MCP servers:", error);
      throw new MCPServerError("Failed to save MCP servers");
    }
  }

  getServers() {
    return this.servers;
  }

  addServer(server: Omit<MCPServer, "id">) {
    this.checkExistingServerName(server.name);
    this.servers.update((servers) => {
      const updatedServers = [
        ...servers,
        {
          ...server,
          id: crypto.randomUUID(),
        },
      ];
      // Save the updated servers
      this.saveServers(updatedServers);
      return updatedServers;
    });
  }

  checkExistingServerName(name: string, id?: string) {
    const existingServer = get(this.servers).find((server) => server.name === name);
    if (existingServer && existingServer?.id !== id) {
      throw new MCPServerError(`Server name '${name}' already exists`);
    }
  }

  updateServer(updatedServer: MCPServer) {
    this.checkExistingServerName(updatedServer.name, updatedServer.id);
    this.servers.update((servers) => {
      const updatedServers = servers.map((server) =>
        server.id === updatedServer.id ? updatedServer : server,
      );

      this.saveServers(updatedServers);
      return updatedServers;
    });
  }

  deleteServer(id: string) {
    this.servers.update((servers) => {
      const newServers = servers.filter((server) => server.id !== id);
      // Always save after deletion, even if the list is now empty
      this.saveServers(newServers);
      return newServers;
    });
  }

  toggleDisabledServer(id: string) {
    this.servers.update((servers) => {
      const updatedServers = servers.map((server) => {
        if (server.id === id) {
          return {
            ...server,
            disabled: !server.disabled,
          };
        }
        return server;
      });

      this.saveServers(updatedServers);
      return updatedServers;
    });
  }

  static convertServerToJSON(server: MCPServer): string {
    return JSON.stringify(
      {
        mcpServers: {
          [server.name]: {
            command: server.command.split(" ")[0],
            args: server.command.split(" ").slice(1),
            env: server.env,
          },
        },
      },
      null,
      2,
    );
  }

  /*
   * Takes in an array of servers and returns a record of server IDs to error messages
   * For now the only error we check for is duplicate server names.
   */
  static parseServerValidationMessages(servers: MCPServer[]) {
    const serverErrorMap = new Map<string, string>();
    const serverWarningMap = new Map<string, string>();

    servers.forEach((server) => {
      if (server.disabled) {
        serverErrorMap.set(server.id, "MCP server has been manually disabled");
      } else if (server.tools && server.tools.length === 0) {
        serverErrorMap.set(server.id, "No tools are available for this MCP server");
      } else if (server.disabledTools && server.disabledTools.length === server.tools?.length) {
        serverErrorMap.set(
          server.id,
          "All tools for this MCP server have validation errors: " +
            server.disabledTools.join(", "),
        );
      } else if (server.disabledTools && server.disabledTools.length > 0) {
        serverWarningMap.set(
          server.id,
          "MCP server has validation errors in the following tools which have been disabled: " +
            server.disabledTools.join(", "),
        );
      }
    });

    const duplicateServerMap = this.parseDuplicateServerIds(servers);
    const allErrorsMap = new Map([...serverErrorMap, ...duplicateServerMap]);

    return {
      errors: allErrorsMap,
      warnings: serverWarningMap,
    };
  }

  /*
   * Takes in an array of servers and returns a set of server IDs that have duplicate names
   */
  static parseDuplicateServerIds(servers: MCPServer[]): Map<string, string> {
    const nameMap = new Map<string, string[]>();

    // First pass: collect server IDs by name
    for (const server of servers) {
      if (!nameMap.has(server.name)) {
        nameMap.set(server.name, []);
      }
      nameMap.get(server.name)!.push(server.id);
    }

    // Second pass: add duplicate IDs to the result set
    const duplicateIds = new Map<string, string>();
    for (const [, ids] of nameMap) {
      if (ids.length > 1) {
        // Skip the first ID (keep it as the original) and mark the rest as duplicates
        for (let i = 1; i < ids.length; i++) {
          duplicateIds.set(ids[i], "MCP server is disabled due to duplicate server names");
        }
      }
    }

    return duplicateIds;
  }

  /**
   * Parses MCP server configurations from a JSON string
   * @param jsonString The JSON string containing MCP server configurations
   * @returns An array of normalized server configurations
   * @throws If the JSON is invalid or has an unsupported format
   */
  parseServerConfigFromJSON(jsonString: string): NormalizedMCPServer[] {
    try {
      const parsedData = JSON.parse(jsonString);

      // Define a parser that tries all supported formats
      const serverConfigParser = z.union([
        // Format 1: Array of server configs
        arrayOfServersSchema.transform((configs: ImportedServerConfig[]) =>
          configs.map((config: ImportedServerConfig) => this.normalizeServerConfig(config)),
        ),

        // Format 2: Object with 'servers' property as an array
        serversArrayContainerSchema.transform((container: { servers: ImportedServerConfig[] }) =>
          container.servers.map((config: ImportedServerConfig) =>
            this.normalizeServerConfig(config),
          ),
        ),

        // Format 3: Object with 'mcpServers' property as an array
        mcpServersArrayContainerSchema.transform(
          (container: { mcpServers: ImportedServerConfig[] }) =>
            container.mcpServers.map((config: ImportedServerConfig) =>
              this.normalizeServerConfig(config),
            ),
        ),

        // Format 4: Object with 'servers' property as a record
        serversRecordContainerSchema.transform((container: { servers: Record<string, unknown> }) =>
          Object.entries(container.servers).map(([serverName, configUnknown]) => {
            const config = baseServerConfigSchema.parse(configUnknown);
            return this.normalizeServerConfig({
              ...config,
              name: config.name || serverName,
            });
          }),
        ),

        // Format 5: Object with 'mcpServers' property as a record
        mcpServersRecordContainerSchema.transform(
          (container: { mcpServers: Record<string, unknown> }) =>
            Object.entries(container.mcpServers).map(([serverName, configUnknown]) => {
              const config = baseServerConfigSchema.parse(configUnknown);
              return this.normalizeServerConfig({
                ...config,
                name: config.name || serverName,
              });
            }),
        ),

        // Format 6: Claude Desktop format (direct key-value pairs of server configs)
        claudeDesktopSchema.transform((record: Record<string, unknown>) => {
          // Verify at least one value has command to confirm it's a server config
          const hasCommand = Object.values(record).some((value) => {
            const parsed = baseServerConfigSchema.safeParse(value);
            return parsed.success && parsed.data.command !== undefined;
          });

          if (!hasCommand) {
            throw new Error("No command property found in any server config");
          }

          return Object.entries(record).map(([serverName, configUnknown]) => {
            const config = baseServerConfigSchema.parse(configUnknown);
            return this.normalizeServerConfig({
              ...config,
              name: config.name || serverName,
            });
          });
        }),

        // Format 7: Single server object
        singleServerSchema.transform((config: ImportedServerConfig) => [
          this.normalizeServerConfig(config),
        ]),
      ]);

      // Try to parse with the unified parser
      const result = serverConfigParser.safeParse(parsedData);

      if (result.success) {
        return result.data;
      }

      // If we get here, none of the schemas matched
      throw new MCPServerError(
        "Invalid JSON format. Expected an array of servers or an object with a 'servers' property.",
      );
    } catch (error) {
      if (error instanceof MCPServerError) {
        throw error;
      }
      throw new MCPServerError("Failed to parse MCP servers from JSON. Please check the format.");
    }
  }

  /**
   * Imports MCP servers from a JSON string
   * @param jsonString The JSON string containing MCP server configurations
   * @throws If the JSON is invalid or if a server with the same name already exists
   */
  importFromJSON(jsonString: string) {
    try {
      const serversToImport = this.parseServerConfigFromJSON(jsonString);

      // Validate server names before importing
      const currentServers = get(this.servers);
      const existingNames = new Set(currentServers.map((s: MCPServer) => s.name));
      for (const server of serversToImport) {
        if (!server.name) {
          throw new MCPServerError("All servers must have a name.");
        }
        if (existingNames.has(server.name)) {
          throw new MCPServerError(`A server with the name '${server.name}' already exists.`);
        }
        existingNames.add(server.name);
      }

      // Import the servers
      this.servers.update((servers) => {
        const updatedServers = [
          ...servers,
          ...serversToImport.map((server) => ({
            ...server,
            id: crypto.randomUUID(),
          })),
        ];
        this.saveServers(updatedServers);
        return updatedServers;
      });

      return serversToImport.length;
    } catch (error) {
      if (error instanceof MCPServerError) {
        throw error;
      }
      throw new MCPServerError("Failed to import MCP servers from JSON. Please check the format.");
    }
  }

  /**
   * Normalizes a server configuration to ensure it has the required properties
   * @param server The server configuration to normalize
   * @returns A normalized server configuration
   */
  private normalizeServerConfig(server: ImportedServerConfig): NormalizedMCPServer {
    try {
      // Use the existing baseServerConfigSchema as the foundation
      const normalizer = baseServerConfigSchema
        .transform((data) => {
          // 1. Determine the command
          const baseCommand = data.command || "";

          // 2. Process arguments if they exist
          const args = data.args
            ? data.args.map((arg: string | number | boolean) => String(arg))
            : [];

          // 3. Combine command and args
          // If we don't have a command, we can't proceed
          if (!baseCommand) {
            throw new Error("Server must have a 'command' property");
          }

          const finalCommand = args.length > 0 ? `${baseCommand} ${args.join(" ")}` : baseCommand;

          // 4. Determine the name (name > title > command)
          const name = data.name || data.title || (baseCommand ? baseCommand.split(" ")[0] : "");

          // 5. Process environment variables
          const env = data.env
            ? Object.fromEntries(
                Object.entries(data.env)
                  .filter(([_, value]: [string, unknown]) => value !== null && value !== undefined)
                  .map(([key, value]: [string, unknown]) => [key, String(value)]),
              )
            : undefined;

          // 6. Return the normalized server
          return {
            name,
            command: finalCommand,
            arguments: "", // Empty since we've included the args in the command
            useShellInterpolation: true, // New servers use shell interpolation
            env: Object.keys(env || {}).length > 0 ? env : undefined,
          };
        })
        .refine((data) => !!data.name, {
          message: "Server must have a name",
          path: ["name"],
        })
        .refine((data) => !!data.command, {
          message: "Server must have a command",
          path: ["command"],
        });

      // Apply the normalization
      const result = normalizer.safeParse(server);
      if (!result.success) {
        throw new MCPServerError(result.error.message);
      }

      return result.data;
    } catch (error) {
      if (error instanceof Error) {
        throw new MCPServerError(`Invalid server configuration: ${error.message}`);
      }
      throw new MCPServerError("Invalid server configuration");
    }
  }
}
