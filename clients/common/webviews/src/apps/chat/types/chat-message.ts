import { type AutofixIterationStage } from "$vscode/src/autofix/autofix-state";
import type {
  ExternalSource,
  WorkspaceFileChunk,
} from "$vscode/src/webview-providers/webview-messages";
import {
  ChatRequestNodeType,
  ChatResultNodeType,
  type ChatRequestNode,
  type ChatResultNode,
  type Exchange,
  type MemoriesInfo,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import type { JSONContent } from "@tiptap/core";
import type { IChatMentionable } from "./mention-option";

export const signInConversationId = "augment-welcome";

export const enum ExchangeStatus {
  draft = "draft",
  sent = "sent",
  failed = "failed",
  success = "success",
  /** The user cancelled the exchange */
  cancelled = "cancelled",
}

/**
 * An agent exchange is a virtual exchange, consisting of a list of normal exchanges.
 * The delimiter between agent exchanges is an exchange with a present user message.
 * In other words, this is how it would look:
 *
 * - [1] User: "Hello"
 * - [1] Agent: "Hi there"
 * - [1] Agent: [Tool Use]
 * - [1] User: [Tool Result]
 * - [1] Agent: [Tool Use]
 * - [1] User: [Tool Result]
 * - [1] Agent: "Here are your pending changes"
 * - [2] User: "Thanks"
 * - [2] Agent: "You're welcome"
 *
 * The first [1] agent exchange is started by a user message. It is stopped when there is
 * another user message, which then creates the start of another agent exchange.
 */
export enum AgentExchangeStatus {
  running = "running",
  awaitingUserAction = "awaiting-user-action",
  notRunning = "not-running",
}

export enum SeenState {
  seen = "seen",
  unseen = "unseen",
}

export const enum ChatItemType {
  signInWelcome = "sign-in-welcome",
  generateCommitMessage = "generate-commit-message",
  summaryResponse = "summary-response",
  summaryTitle = "summary-title",
  educateFeatures = "educate-features",
  autofixMessage = "autofix-message",
  autofixSteeringMessage = "autofix-steering-message",
  autofixStage = "autofix-stage",
  agentOnboarding = "agent-onboarding",
  agenticTurnDelimiter = "agentic-turn-delimiter",
  agenticRevertDelimiter = "agentic-revert-delimiter",
  agenticCheckpointDelimiter = "agentic-checkpoint-delimiter",
  exchange = "exchange",
}

export type ChatItem = ChatItemCommon & OneOfChatItem;
export interface ChatItemCommon {
  chatItemType?: ChatItemType;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  request_id?: string;
}

export type OneOfChatItem =
  | ExchangeWithStatus
  | SignInWelcomeMessage
  | EducateFeatures
  | GenerateCommitMessage
  | SummaryResponseMessage
  | AutofixMessage
  | AutofixSteeringMessage
  | AutofixStageMessage
  | AgenticTurnDelimiter
  | AgenticRevertDelimiter
  | AgenticCheckpointDelimiter;

export function isChatItemExchangeWithStatus(
  item: ChatItem | null | undefined,
): item is ExchangeWithStatus {
  if (!item) {
    return false;
  }
  return item.chatItemType === undefined || item.chatItemType === ChatItemType.agentOnboarding;
}

export function isChatItemSuccessfulExchange(
  item: ChatItem | null | undefined,
): item is ExchangeWithStatus {
  return isChatItemExchangeWithStatus(item) && item.status === ExchangeStatus.success;
}

export function isChatItemAutofixMessage(item: ChatItem): item is AutofixMessage {
  return item.chatItemType === ChatItemType.autofixMessage;
}

export function isChatItemAutofixSteeringMessage(item: ChatItem): item is AutofixMessage {
  return item.chatItemType === ChatItemType.autofixSteeringMessage;
}

export function isChatItemAutofixStage(item: ChatItem): item is AutofixStageMessage {
  return item.chatItemType === ChatItemType.autofixStage;
}

export function isChatItemSignInWelcome(item: ChatItem): item is SignInWelcomeMessage {
  return item.chatItemType === ChatItemType.signInWelcome;
}

export function isChatItemGenerateCommitMessage(item: ChatItem): item is GenerateCommitMessage {
  return item.chatItemType === ChatItemType.generateCommitMessage;
}

export function isChatItemSummaryResponse(item: ChatItem): item is SummaryResponseMessage {
  return item.chatItemType === ChatItemType.summaryResponse;
}

export function isChatItemEducateFeatures(item: ChatItem): item is EducateFeatures {
  return item.chatItemType === ChatItemType.educateFeatures;
}

export function noChatItemExchangeWithStatus(items: ChatItem[]): boolean {
  return items.every((item) => !isChatItemExchangeWithStatus(item));
}

export function isChatItemAgentOnboarding(item: ChatItem): item is AgentOnboardingMessage {
  return item.chatItemType === ChatItemType.agentOnboarding;
}

export function isChatItemAgenticTurnDelimiter(item: ChatItem): item is AgenticTurnDelimiter {
  return item.chatItemType === ChatItemType.agenticTurnDelimiter;
}

export function isChatItemAgenticCheckpointDelimiter(
  item: ChatItem,
): item is AgenticCheckpointDelimiter {
  return item.chatItemType === ChatItemType.agenticCheckpointDelimiter;
}

export type ExchangeWithStatus = ChatItemCommon &
  IChatItemSeenState &
  IChatItemResponseMessage & {
    chatItemType?: ChatItemType;
    /* eslint-disable @typescript-eslint/naming-convention */
    request_message: string;
    request_id?: string;
    timestamp?: string; // Timestamp in ISO format
    rich_text_json_repr?: JSONContent | JSONContent[];
    workspace_file_chunks?: WorkspaceFileChunk[];
    external_sources?: ExternalSource[];
    mentioned_items?: IChatMentionable[];
    structured_output_nodes?: ChatResultNode[];
    model_id?: string | undefined;
    disableRetrieval?: boolean;
    disableSelectedCodeDetails?: boolean;
    /**
     * Override the exchange's history. If empty array, there is no chat history.
     * If undefined, use the original chat history. */
    chatHistory?: Exchange[];
    structured_request_nodes?: ChatRequestNode[];
    memoriesInfo?: MemoriesInfo;
    /* eslint-enable @typescript-eslint/naming-convention */
  };

export type SignInWelcomeMessage = ChatItemCommon &
  IChatItemSeenState &
  IChatItemResponseMessage & {
    chatItemType: ChatItemType.signInWelcome;
  };

export type GenerateCommitMessage = ExchangeWithStatus & {
  chatItemType: ChatItemType.generateCommitMessage;
};

export type SummaryResponseMessage = ExchangeWithStatus & {
  chatItemType: ChatItemType.summaryResponse;
  questions?: string[];
  questionsLoaded?: boolean;
};

export type AutofixMessage = ChatItemCommon &
  IChatItemResponseMessage & {
    chatItemType: ChatItemType.autofixMessage;
  };

export type AutofixSteeringMessage = ChatItemCommon &
  IChatItemResponseMessage & {
    chatItemType: ChatItemType.autofixSteeringMessage;
  };

export type AutofixStageMessage = ChatItemCommon &
  IChatItemResponseMessage & {
    chatItemType: ChatItemType.autofixStage;
    iterationId: string;
    stage: AutofixIterationStage;
    stageCount?: number;
  };

export type EducateFeatures = ChatItemCommon &
  IChatItemSeenState & {
    chatItemType: ChatItemType.educateFeatures;
  };

export type AgentOnboardingMessage = ExchangeWithStatus & {
  chatItemType: ChatItemType.agentOnboarding;
};

export type AgenticTurnDelimiter = ChatItemCommon & {
  chatItemType: ChatItemType.agenticTurnDelimiter;
};

export type AgenticRevertDelimiter = ChatItemCommon & {
  chatItemType: ChatItemType.agenticRevertDelimiter;
  status: ExchangeStatus;
  revertTarget: number;
};

export type AgenticRevertTargetInfo = {
  uuid?: string; // The UUID of the checkpoint that was reverted to
  filePath?: IQualifiedPathName; // The file path of the checkpoint that was reverted to
};

export type AgenticCheckpointDelimiter = ChatItemCommon & {
  uuid: string;
  chatItemType: ChatItemType.agenticCheckpointDelimiter;

  /**
   * The status of the checkpoint. States have the following semantic meaning:
   * - Draft: the "current" checkpoint, but it is not yet required to track any changes.
   *     This is the state before a user message is sent and there is work to track.
   *     Should not be rendered.
   * - Sent: the "current" checkpoint, and is actively tracking changes. This is the
   *     state after a user message is sent. Should not be rendered, but should actively
   *     affect checkpoint tracking state. All current changes in the current agentic turn
   *     will coalesce into this checkpoint.
   * - Success: the checkpoint is completed and is no longer the "current" checkpoint.
   *      When an agentic turn finishes (either through success or cancellation) and
   *     there are changes tracked in this checkpoint, it will be added to the conversation
   *     history. Should be rendered.
   */
  status: ExchangeStatus;

  // If this checkpoint is the result of a revert, this is the UUID of the checkpoint that
  // was reverted to. If we cannot find this in the list, we will not show metadata for it.
  revertTarget?: AgenticRevertTargetInfo;

  // The timestamp range for this checkpoint
  fromTimestamp: number;
  toTimestamp: number;
};

export function isCheckpointRevert(
  checkpoint: AgenticCheckpointDelimiter,
): checkpoint is AgenticCheckpointDelimiter & { revertTarget: AgenticRevertTargetInfo } {
  return checkpoint.revertTarget !== undefined;
}

export interface IChatItemSeenState {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  request_id?: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  seen_state?: SeenState;
}

export function hasToolUse(exchange: ExchangeWithStatus): boolean {
  return (
    exchange.structured_output_nodes?.some((node) => node.type === ChatResultNodeType.TOOL_USE) ??
    false
  );
}

export function hasToolResult(exchange: ExchangeWithStatus): boolean {
  return (
    exchange.structured_request_nodes?.some(
      (node) => node.type === ChatRequestNodeType.TOOL_RESULT,
    ) ?? false
  );
}

export function isIChatItemSeenState(value: unknown): value is IChatItemSeenState {
  if (!value || typeof value !== "object") {
    return false;
  }
  if ("request_id" in value && typeof value.request_id !== "string") {
    return false;
  }
  if (
    "seen_state" in value &&
    !(value.seen_state === SeenState.seen || value.seen_state === SeenState.unseen)
  ) {
    return false;
  }
  return true;
}

export interface IChatItemResponseMessage {
  status: ExchangeStatus;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  response_text?: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  display_error_message?: string;
  isRetriable?: boolean;
}
