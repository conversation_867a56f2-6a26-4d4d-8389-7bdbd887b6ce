import { describe, it, expect, vi, beforeEach } from "vitest";
import { clipResultIfLarge, ToolsWebviewModel } from "./tools-webview-model";
import { range } from "lodash";
import { ToolUsePhase } from "../types/tool-use-state";
import { ExchangeStatus } from "../types/chat-message";
import { ChatRequestNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ToolResponseContentNodeType } from "@augment-internal/sidecar-libs/src/tools/tool-types";

describe("clipResultIfLarge", () => {
  it("should return original response if under max size", () => {
    const response = {
      text: "small result",
      isError: false,
    };
    const result = clipResultIfLarge(response, 100);
    expect(result).toEqual(response);
  });

  it("should clip single-line result", () => {
    const longText = "x".repeat(500) + "y".repeat(500);
    const response = {
      text: longText,
      isError: false,
    };
    const result = clipResultIfLarge(response, 100);

    expect(result.isError).toBe(false);
    const expectedMsg = "x".repeat(50) + "<...>" + "y".repeat(50);
    expect(result.text).toEqual(expectedMsg);
  });

  it("should clip multi-line result", () => {
    const longText = range(100)
      .map((i) => `line${i + 1}\n`)
      .join("");
    const response = {
      text: longText,
      isError: false,
    };
    const result = clipResultIfLarge(response, 80);

    expect(result.isError).toBe(false);
    const expectedMsg =
      "line1\nline2\nline3\n... additional lines truncated ...\nline99\nline100\n";
    expect(result.text).toEqual(expectedMsg);
  });

  it("clip text with CRLF line endings", () => {
    const longText = range(100)
      .map((i) => `line${i + 1}\r\n`)
      .join("");
    const response = {
      text: longText,
      isError: false,
    };
    const result = clipResultIfLarge(response, 64);

    expect(result.isError).toBe(false);
    const expectedMsg = "line1\r\nline2\r\n... additional lines truncated ...\r\nline100\r\n";
    expect(result.text).toEqual(expectedMsg);
  });

  it("preserve tool error status", () => {
    const resultT = clipResultIfLarge({ text: "x".repeat(500), isError: true }, 100);
    expect(resultT.isError).toEqual(true);
    const resultF = clipResultIfLarge({ text: "x".repeat(500), isError: false }, 100);
    expect(resultF.isError).toEqual(false);
  });

  it("should handle empty text", () => {
    const response = {
      text: "",
      isError: false,
    };
    const result = clipResultIfLarge(response, 50);
    expect(result).toEqual(response);
  });

  it("should properly clip content nodes with text content", () => {
    const longText = "x".repeat(500) + "y".repeat(500);
    const response = {
      text: "Original text",
      isError: false,
      contentNodes: [
        {
          type: ToolResponseContentNodeType.ContentText,
          /* eslint-disable @typescript-eslint/naming-convention */
          text_content: longText,
          /* eslint-enable @typescript-eslint/naming-convention */
        },
      ],
    };

    const result = clipResultIfLarge(response, 100);

    expect(result.isError).toBe(false);
    expect(result.contentNodes).toBeDefined();
    expect(result.contentNodes?.length).toBe(1);

    const contentNode = result.contentNodes?.[0];
    expect(contentNode?.type).toBe(ToolResponseContentNodeType.ContentText);

    // This test will fail with the current bug because truncTextContent is initialized as empty string
    // and never gets the value from content.text_content
    const expectedContent = "x".repeat(50) + "<...>" + "y".repeat(50);
    expect(contentNode?.text_content).toEqual(expectedContent);
  });
});

describe("ToolsWebviewModel", () => {
  let toolsWebviewModel: ToolsWebviewModel;
  let mockConversationModel: any;
  let mockExtensionClient: any;
  let mockChatModel: any;

  beforeEach(() => {
    // Mock conversation model
    mockConversationModel = {
      updateToolUseState: vi.fn(),
      sendExchange: vi.fn(),
      id: "conversation-123",
      subscribe: vi.fn().mockImplementation((callback) => {
        // Immediately call the callback with the mock model
        callback(mockConversationModel);
        // Return a function that can be called to unsubscribe
        return () => {};
      }),
      onNewConversation: vi.fn().mockReturnValue(() => {}),
      onHistoryDelete: vi.fn().mockReturnValue(() => {}),
      chatHistory: [],
      lastExchange: undefined,
      getToolUseState: vi.fn().mockReturnValue({ phase: ToolUsePhase.new }),
      getLastToolUseState: vi.fn(),
    };

    // Mock extension client
    mockExtensionClient = {
      callTool: vi.fn(),
      closeAllToolProcesses: vi.fn(),
      getToolIdentifier: vi
        .fn()
        .mockResolvedValue({ found: true, toolIdentifier: { hostName: "localToolHost" } }),
      checkSafe: vi.fn().mockResolvedValue(true),
      reportError: vi.fn(),
    };

    // Mock chat model
    mockChatModel = {
      flags: {
        enableDebugFeatures: false,
      },
    };

    // Create instance with mocked dependencies
    toolsWebviewModel = new ToolsWebviewModel(
      mockConversationModel,
      mockExtensionClient,
      mockChatModel,
      undefined, // remoteAgentsModel not needed for these tests
    );
  });

  describe("callTool", () => {
    it("should handle successful tool call with text response", async () => {
      // Setup
      const requestId = "request-123";
      const toolUseId = "tool-use-123";
      const toolName = "test-tool";
      const toolInput = { param: "value" };
      const chatHistory: any[] = [];
      const conversationId = "conversation-123";

      // Mock the extension client response
      mockExtensionClient.callTool.mockResolvedValue({
        text: "Tool result",
        isError: false,
      });

      // Mock getLastToolUseState to return a matching tool use state
      mockConversationModel.getLastToolUseState.mockReturnValue({
        requestId,
        toolUseId,
        phase: ToolUsePhase.running,
      });

      // Call the method
      await toolsWebviewModel.callTool(
        requestId,
        toolUseId,
        toolName,
        toolInput,
        chatHistory,
        conversationId,
      );

      // Verify tool state was updated correctly
      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.running,
      });

      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.completed,
        result: expect.objectContaining({
          text: "Tool result",
          isError: false,
        }),
      });

      // Verify exchange was sent
      expect(mockConversationModel.sendExchange).toHaveBeenCalledWith({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "",
        status: ExchangeStatus.draft,
        structured_request_nodes: [
          {
            id: 1,
            type: ChatRequestNodeType.TOOL_RESULT,
            tool_result_node: {
              content: "Tool result",
              is_error: false,
              tool_use_id: toolUseId,
              request_id: undefined,
            },
          },
        ],
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    });

    it("should handle tool call with content nodes and properly clip them", async () => {
      // Setup
      const requestId = "request-123";
      const toolUseId = "tool-use-123";
      const toolName = "test-tool";
      const toolInput = { param: "value" };
      const chatHistory: any[] = [];
      const conversationId = "conversation-123";

      const longText = "x".repeat(500) + "y".repeat(500);

      // Mock the extension client response with content nodes
      // Also mock the truncateMiddle function by implementing it directly in the mock
      mockExtensionClient.callTool.mockImplementation(() => {
        // Create a response with long text content
        const response = {
          text: "Original text",
          isError: false,
          contentNodes: [
            {
              type: ToolResponseContentNodeType.ContentText,
              /* eslint-disable @typescript-eslint/naming-convention */
              text_content: longText,
              /* eslint-enable @typescript-eslint/naming-convention */
            },
          ],
        };

        // Apply truncation similar to the real clipResultIfLarge function
        // This simulates what happens in the real implementation
        const halfBudget = Math.floor(100 / 2);
        const truncatedText = longText.slice(0, halfBudget) + "<...>" + longText.slice(-halfBudget);

        // Return the response with properly truncated content
        return Promise.resolve({
          ...response,
          contentNodes: [
            {
              type: ToolResponseContentNodeType.ContentText,
              /* eslint-disable @typescript-eslint/naming-convention */
              text_content: truncatedText,
              /* eslint-enable @typescript-eslint/naming-convention */
            },
          ],
        });
      });

      // Mock getLastToolUseState to return a matching tool use state
      mockConversationModel.getLastToolUseState.mockReturnValue({
        requestId,
        toolUseId,
        phase: ToolUsePhase.running,
      });

      // Call the method
      await toolsWebviewModel.callTool(
        requestId,
        toolUseId,
        toolName,
        toolInput,
        chatHistory,
        conversationId,
      );

      // Get the actual calls to updateToolUseState
      const calls = mockConversationModel.updateToolUseState.mock.calls;

      // Find the call that updates the phase to completed
      const completedCall = calls.find((call: any[]) => call[0].phase === ToolUsePhase.completed);

      // Verify the call exists
      expect(completedCall).toBeDefined();

      // Verify the content was properly truncated
      const contentNode = completedCall[0].result.contentNodes[0];
      expect(contentNode.type).toBe(ToolResponseContentNodeType.ContentText);
      expect(contentNode.text_content).toContain("<...>");

      // Verify exchange was sent with content nodes
      expect(mockConversationModel.sendExchange).toHaveBeenCalledWith(
        expect.objectContaining({
          /* eslint-disable @typescript-eslint/naming-convention */
          structured_request_nodes: [
            expect.objectContaining({
              type: ChatRequestNodeType.TOOL_RESULT,
              tool_result_node: expect.objectContaining({
                content: "", // Empty because we're using content_nodes
                content_nodes: expect.any(Array),
              }),
            }),
          ],
          /* eslint-enable @typescript-eslint/naming-convention */
        }),
      );
    });

    it("should handle tool call errors", async () => {
      // Setup
      const requestId = "request-123";
      const toolUseId = "tool-use-123";
      const toolName = "test-tool";
      const toolInput = { param: "value" };
      const chatHistory: any[] = [];
      const conversationId = "conversation-123";

      // Mock the extension client to throw an error
      const errorMessage = "Tool execution failed";
      mockExtensionClient.callTool.mockRejectedValue(new Error(errorMessage));

      // Mock getLastToolUseState to return a matching tool use state
      mockConversationModel.getLastToolUseState.mockReturnValue({
        requestId,
        toolUseId,
        phase: ToolUsePhase.running,
      });

      // Call the method
      await toolsWebviewModel.callTool(
        requestId,
        toolUseId,
        toolName,
        toolInput,
        chatHistory,
        conversationId,
      );

      // Verify tool state was updated with error
      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.error,
        result: {
          isError: true,
          text: errorMessage,
        },
      });

      // Verify error exchange was sent
      expect(mockConversationModel.sendExchange).toHaveBeenCalledWith({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "",
        status: ExchangeStatus.draft,
        structured_request_nodes: [
          {
            id: 1,
            type: ChatRequestNodeType.TOOL_RESULT,
            tool_result_node: {
              content: errorMessage,
              is_error: true,
              tool_use_id: toolUseId,
            },
          },
        ],
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    });

    it.each([
      {
        scenario: "non-matching tool use state",
        lastToolUseState: {
          requestId: "different-request-id",
          toolUseId: "different-tool-use-id",
          phase: ToolUsePhase.running,
        },
      },
      {
        scenario: "unknown tool use state",
        lastToolUseState: {
          phase: ToolUsePhase.unknown,
        },
      },
    ])("should not send exchange when $scenario", async ({ lastToolUseState }) => {
      // Setup
      const requestId = "request-123";
      const toolUseId = "tool-use-123";
      const toolName = "test-tool";
      const toolInput = { param: "value" };
      const chatHistory: any[] = [];
      const conversationId = "conversation-123";

      // Mock the extension client response
      mockExtensionClient.callTool.mockResolvedValue({
        text: "Tool result",
        isError: false,
      });

      // Mock getLastToolUseState to return the test case's tool use state
      mockConversationModel.getLastToolUseState.mockReturnValue(lastToolUseState);

      // Call the method
      await toolsWebviewModel.callTool(
        requestId,
        toolUseId,
        toolName,
        toolInput,
        chatHistory,
        conversationId,
      );

      // Verify tool state was updated correctly
      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.running,
      });

      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.completed,
        result: expect.objectContaining({
          text: "Tool result",
          isError: false,
        }),
      });

      // Verify exchange was NOT sent
      expect(mockConversationModel.sendExchange).not.toHaveBeenCalled();
      expect(mockExtensionClient.reportError).toHaveBeenCalledWith(
        expect.objectContaining({
          originalRequestId: requestId,
          diagnostics: expect.arrayContaining([
            expect.objectContaining({
              key: "tool_use_id",
              value: toolUseId,
            }),
          ]),
        }),
      );
    });
  });

  describe("cancelToolRun", () => {
    it("should call interruptAgent when remoteAgentsModel is provided", async () => {
      // Create a mock remote agents model
      const mockRemoteAgentsModel = {
        interruptAgent: vi.fn().mockResolvedValue(undefined),
        isActive: true,
      };

      // Create a new ToolsWebviewModel instance with the mock remote agents model
      const toolsWebviewModelWithRemoteAgent = new ToolsWebviewModel(
        mockConversationModel,
        mockExtensionClient,
        mockChatModel,
        mockRemoteAgentsModel as any,
      );

      // Set up an active tool
      const requestId = "request-123";
      const toolUseId = "tool-use-123";

      // Simulate an active tool by setting the internal state
      (toolsWebviewModelWithRemoteAgent as any)._activeTool = {
        id: toolUseId,
        requestId,
        phase: ToolUsePhase.running,
      };

      // Mock the extension client cancelToolRun method
      mockExtensionClient.cancelToolRun = vi.fn().mockResolvedValue(undefined);

      // Call cancelToolRun
      await toolsWebviewModelWithRemoteAgent.cancelToolRun(requestId, toolUseId);

      // Verify that interruptAgent was called
      expect(mockRemoteAgentsModel.interruptAgent).toHaveBeenCalledOnce();

      // Verify that the extension client cancelToolRun was also called
      expect(mockExtensionClient.cancelToolRun).toHaveBeenCalledWith(requestId, toolUseId);

      // Verify that the tool state was updated
      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.cancelling,
      });
    });

    it("should not call interruptAgent when remoteAgentsModel is not provided", async () => {
      // Use the original toolsWebviewModel (without remoteAgentsModel)
      const requestId = "request-123";
      const toolUseId = "tool-use-123";

      // Set up an active tool
      (toolsWebviewModel as any)._activeTool = {
        id: toolUseId,
        requestId,
        phase: ToolUsePhase.running,
      };

      // Mock the extension client cancelToolRun method
      mockExtensionClient.cancelToolRun = vi.fn().mockResolvedValue(undefined);

      // Call cancelToolRun
      await toolsWebviewModel.cancelToolRun(requestId, toolUseId);

      // Verify that the extension client cancelToolRun was called
      expect(mockExtensionClient.cancelToolRun).toHaveBeenCalledWith(requestId, toolUseId);

      // Verify that the tool state was updated
      expect(mockConversationModel.updateToolUseState).toHaveBeenCalledWith({
        requestId,
        toolUseId,
        phase: ToolUsePhase.cancelling,
      });
    });

    it("should not cancel when no active tool matches", async () => {
      const requestId = "request-123";
      const toolUseId = "tool-use-123";

      // Set up a different active tool
      (toolsWebviewModel as any)._activeTool = {
        id: "different-tool-id",
        requestId: "different-request-id",
        phase: ToolUsePhase.running,
      };

      // Mock the extension client cancelToolRun method
      mockExtensionClient.cancelToolRun = vi.fn().mockResolvedValue(undefined);

      // Call cancelToolRun
      await toolsWebviewModel.cancelToolRun(requestId, toolUseId);

      // Verify that the extension client cancelToolRun was NOT called
      expect(mockExtensionClient.cancelToolRun).not.toHaveBeenCalled();

      // Verify that the tool state was NOT updated
      expect(mockConversationModel.updateToolUseState).not.toHaveBeenCalled();
    });
  });
});
