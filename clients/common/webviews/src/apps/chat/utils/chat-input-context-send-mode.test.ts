/**
 * @file chat-input-context-send-mode.test.ts
 * Tests for send mode functionality in chat input context
 */

import { describe, it, expect, beforeEach, vi } from "vitest";
import { getChatInputContext } from "./chat-input-context";
import type { ChatModel } from "../models/chat-model";
import type { AgentConversationModel } from "../models/agent-conversation-model";
import type { ToolsWebviewModel } from "../models/tools-webview-model";
import type { ICurrentConversationTaskStore } from "../models/task-store";
import { get } from "svelte/store";
import { SendMode } from "../types/send-mode";

// Mock the svelte/store get function
vi.mock("svelte/store", () => ({
  get: vi.fn(),
}));

describe("getChatInputContext - Send Mode Functionality", () => {
  let chatModel: ChatModel;
  let agentConversationModel: AgentConversationModel;
  let toolsWebviewModel: ToolsWebviewModel;
  let mockTaskStore: ICurrentConversationTaskStore;
  let mockConversationModel: any;
  let mockSlashCommandModel: any;

  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks();

    // Create mock conversation model
    mockConversationModel = {
      draftExchange: {
        /* eslint-disable @typescript-eslint/naming-convention */
        rich_text_json_repr: null,
        request_message: "Create a new feature for user authentication",
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      clearDraftExchange: vi.fn(),
      sendDraftExchange: vi.fn(),
      hasDraft: true,
      canSendDraft: true,
      canCancelMessage: false,
      rootTaskUuid: "mock-root-task-uuid",
    };

    // Create mock slash command model
    mockSlashCommandModel = {
      activeCommand: "mockActiveCommand",
      runActiveCommand: vi.fn(),
    };

    // Create mock chat model
    chatModel = {
      currentConversationModel: mockConversationModel,
      flags: {
        enableChatMultimodal: true,
        enableTaskList: true,
      },
      extensionClient: {
        createTask: vi.fn().mockResolvedValue("mock-task-uuid"),
      },
    } as unknown as ChatModel;

    // Create mock agent conversation model
    agentConversationModel = {
      isCurrConversationAgentic: "mockIsCurrConversationAgentic",
      interruptAgent: vi.fn().mockResolvedValue(undefined),
    } as unknown as AgentConversationModel;

    // Create mock tools webview model
    toolsWebviewModel = {
      interruptToolsConversation: vi.fn(),
    } as unknown as ToolsWebviewModel;

    // Create mock task store
    mockTaskStore = {
      createTask: vi.fn().mockResolvedValue("mock-task-uuid"),
      rootTaskUuid: vi.fn(),
      rootTask: vi.fn(),
      isEnhancing: vi.fn(),
      isImportingExporting: vi.fn(),
      canShowTaskList: vi.fn(),
      uuidToTask: vi.fn(),
      updateTask: vi.fn(),
      getHydratedTask: vi.fn(),
      cloneHydratedTask: vi.fn(),
      refreshTasks: vi.fn(),
      updateTaskListStatuses: vi.fn(),
      syncTaskListWithConversation: vi.fn(),
      deleteTask: vi.fn(),
      cleanupEmptyAndCancelledTasks: vi.fn(),
      getParentTask: vi.fn(),
      addNewTaskAfter: vi.fn(),
      saveHydratedTask: vi.fn(),
      runHydratedTask: vi.fn(),
      dispose: vi.fn(),
      handleMessageFromExtension: vi.fn(),
      runAllTasks: vi.fn(),
      exportTask: vi.fn(),
      exportTasksToMarkdown: vi.fn(),
      importTasksFromMarkdown: vi.fn(),
      setEnhancing: vi.fn(),
    } as unknown as ICurrentConversationTaskStore;

    // Setup default mock return values
    (get as any).mockImplementation((store: unknown) => {
      if (store === mockSlashCommandModel.activeCommand) {
        return null; // No active slash command by default
      }
      if (store === agentConversationModel.isCurrConversationAgentic) {
        return true; // In agent mode by default for send mode tests
      }
      return store;
    });
  });

  describe("agent mode with send mode", () => {
    it("should use regular send action when in send mode", () => {
      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.send,
        mockTaskStore,
      );

      expect(context.stateName).toBe("agentMode");
      expect(context.showCancelButton).toBe(false);
      expect(context.isDisabled).toBe(false);

      // Test action - should interrupt agent and send draft
      context.action();
      expect(agentConversationModel.interruptAgent).toHaveBeenCalled();
      // Note: sendDraftExchange is called asynchronously after interruptAgent resolves
    });

    it("should use task creation action when in addTask mode", async () => {
      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.addTask,
        mockTaskStore,
      );

      expect(context.stateName).toBe("agentMode");
      expect(context.showCancelButton).toBe(false);
      expect(context.isDisabled).toBe(false);

      // Test action - should create task instead of sending message
      const result = context.action();
      expect(result).toBe(true);

      // Wait for async operations
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Verify task creation was called
      expect(mockTaskStore.createTask).toHaveBeenCalledWith(
        "Create a new feature for user authentication",
        "Create a new feature for user authentication",
        "mock-root-task-uuid",
      );

      // Verify draft was cleared after successful task creation
      expect(mockConversationModel.clearDraftExchange).toHaveBeenCalled();

      // Verify agent was not interrupted (different from send mode)
      expect(agentConversationModel.interruptAgent).not.toHaveBeenCalled();
    });

    it("should handle task creation with long message content", async () => {
      const longMessage =
        "This is a very long message that exceeds the normal task name length limit and should be truncated properly when creating a task";
      /* eslint-disable @typescript-eslint/naming-convention */
      mockConversationModel.draftExchange.request_message = longMessage;
      /* eslint-enable @typescript-eslint/naming-convention */

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.addTask,
        mockTaskStore,
      );

      context.action();
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Verify task creation was called with full message as both name and description
      // (since it's a single line, the entire message becomes both the task name and description)
      expect(mockTaskStore.createTask).toHaveBeenCalledWith(
        longMessage, // Full message as task name (no truncation in original behavior)
        longMessage, // Full message as task description (for single line messages)
        "mock-root-task-uuid",
      );
    });

    it("should handle task creation with multiline message content", async () => {
      const multilineMessage =
        "First line of the task\nSecond line with more details\nThird line with even more information";
      /* eslint-disable @typescript-eslint/naming-convention */
      mockConversationModel.draftExchange.request_message = multilineMessage;
      /* eslint-enable @typescript-eslint/naming-convention */

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.addTask,
        mockTaskStore,
      );

      context.action();
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Verify task creation was called with first line as name and rest as description
      expect(mockTaskStore.createTask).toHaveBeenCalledWith(
        "First line of the task",
        "Second line with more details Third line with even more information", // Newlines collapsed to spaces
        "mock-root-task-uuid",
      );
    });
  });

  describe("task creation error handling", () => {
    it("should fall back to sending message when task creation fails", async () => {
      // Mock task creation to fail
      mockTaskStore.createTask = vi.fn().mockRejectedValue(new Error("Task creation failed"));

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.addTask,
        mockTaskStore,
      );

      context.action();
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Verify task creation was attempted
      expect(mockTaskStore.createTask).toHaveBeenCalled();

      // Verify fallback to regular send behavior
      expect(agentConversationModel.interruptAgent).toHaveBeenCalled();
    });

    it("should fall back to sending when no root task UUID", async () => {
      // Remove root task UUID
      mockConversationModel.rootTaskUuid = undefined;

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.addTask,
        mockTaskStore,
      );

      context.action();
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Verify task creation was not attempted
      expect(mockTaskStore.createTask).not.toHaveBeenCalled();

      // Verify fallback to regular send behavior
      expect(agentConversationModel.interruptAgent).toHaveBeenCalled();
    });

    it("should fall back to sending when task list is disabled", async () => {
      // Disable task list
      chatModel = {
        ...chatModel,
        flags: {
          ...chatModel.flags,
          enableTaskList: false,
        },
      } as unknown as ChatModel;

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.addTask,
        mockTaskStore,
      );

      context.action();
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Verify task creation was not attempted
      expect(mockTaskStore.createTask).not.toHaveBeenCalled();

      // Verify fallback to regular send behavior
      expect(agentConversationModel.interruptAgent).toHaveBeenCalled();
    });

    it("should handle empty message content gracefully", async () => {
      // Set empty message
      /* eslint-disable @typescript-eslint/naming-convention */
      mockConversationModel.draftExchange.request_message = "";
      /* eslint-enable @typescript-eslint/naming-convention */

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.addTask,
        mockTaskStore,
      );

      const result = context.action();
      expect(result).toBe(true);

      // Verify no task creation or message sending occurred
      expect(mockTaskStore.createTask).not.toHaveBeenCalled();
      expect(agentConversationModel.interruptAgent).not.toHaveBeenCalled();
    });

    it("should handle whitespace-only message content", async () => {
      // Set whitespace-only message
      /* eslint-disable @typescript-eslint/naming-convention */
      mockConversationModel.draftExchange.request_message = "   \n\t  ";
      /* eslint-enable @typescript-eslint/naming-convention */

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.addTask,
        mockTaskStore,
      );

      const result = context.action();
      expect(result).toBe(true);

      // Verify no task creation or message sending occurred
      expect(mockTaskStore.createTask).not.toHaveBeenCalled();
      expect(agentConversationModel.interruptAgent).not.toHaveBeenCalled();
    });
  });

  describe("chat mode behavior", () => {
    it("should always use regular send in chat mode regardless of send mode", () => {
      // Set to chat mode
      (get as any).mockImplementation((store: unknown) => {
        if (store === mockSlashCommandModel.activeCommand) {
          return null; // No active slash command
        }
        if (store === agentConversationModel.isCurrConversationAgentic) {
          return false; // In chat mode
        }
        return store;
      });

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.addTask, // Even with addTask mode, should use regular send in chat mode
      );

      expect(context.stateName).toBe("chatMode");

      // Test action - should always send message, never create task
      context.action();
      expect(mockConversationModel.sendDraftExchange).toHaveBeenCalled();
      expect(chatModel.extensionClient.createTask).not.toHaveBeenCalled();
    });
  });

  describe("unknown send mode handling", () => {
    it("should handle unknown send mode gracefully", async () => {
      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        "unknown" as SendMode,
      );

      expect(context.stateName).toBe("agentMode");

      context.action();
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Should fall back to regular send behavior
      expect(agentConversationModel.interruptAgent).toHaveBeenCalled();
    });
  });

  describe("task name extraction", () => {
    it("should extract task name from short message", async () => {
      /* eslint-disable @typescript-eslint/naming-convention */
      mockConversationModel.draftExchange.request_message = "Fix the login bug";
      /* eslint-enable @typescript-eslint/naming-convention */

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.addTask,
        mockTaskStore,
      );

      context.action();
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(mockTaskStore.createTask).toHaveBeenCalledWith(
        "Fix the login bug",
        "Fix the login bug",
        "mock-root-task-uuid",
      );
    });

    it("should use full message as task name when single line (no truncation)", async () => {
      /* eslint-disable @typescript-eslint/naming-convention */
      mockConversationModel.draftExchange.request_message =
        "This is a very long task name that should be used in full without any truncation";
      /* eslint-enable @typescript-eslint/naming-convention */

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.addTask,
        mockTaskStore,
      );

      context.action();
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Verify task creation was called with full message as task name (no truncation)
      expect(mockTaskStore.createTask).toHaveBeenCalledWith(
        "This is a very long task name that should be used in full without any truncation",
        "This is a very long task name that should be used in full without any truncation", // Same as name for single line
        "mock-root-task-uuid",
      );
    });

    it("should handle task names that are exactly at the limit", async () => {
      /* eslint-disable @typescript-eslint/naming-convention */
      mockConversationModel.draftExchange.request_message =
        "This is exactly fifty characters long for testing";
      /* eslint-enable @typescript-eslint/naming-convention */

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.addTask,
        mockTaskStore,
      );

      context.action();
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(mockTaskStore.createTask).toHaveBeenCalledWith(
        "This is exactly fifty characters long for testing",
        "This is exactly fifty characters long for testing",
        "mock-root-task-uuid",
      );
    });

    it("should use full message as task name even with no spaces (no truncation)", async () => {
      /* eslint-disable @typescript-eslint/naming-convention */
      mockConversationModel.draftExchange.request_message =
        "Supercalifragilisticexpialidocious_task_name_with_no_spaces_that_is_very_long";
      /* eslint-enable @typescript-eslint/naming-convention */

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        SendMode.addTask,
        mockTaskStore,
      );

      context.action();
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Verify task creation was called with full message as task name (no truncation)
      expect(mockTaskStore.createTask).toHaveBeenCalledWith(
        "Supercalifragilisticexpialidocious_task_name_with_no_spaces_that_is_very_long",
        "Supercalifragilisticexpialidocious_task_name_with_no_spaces_that_is_very_long", // Same as name for single line
        "mock-root-task-uuid",
      );
    });
  });
});
