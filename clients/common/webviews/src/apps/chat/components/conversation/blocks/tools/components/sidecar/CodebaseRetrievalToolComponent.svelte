<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import AugmentIcon from "$common-webviews/src/design-system/icons/augment/augment-logo.svelte";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import ShowMore from "$common-webviews/src/common/components/ShowMore.svelte";

  export let toolUseInput: Record<string, unknown> = {};
</script>

<BaseToolComponent showToolOutput={false}>
  <ToolUseHeader
    slot="header"
    class="c-tool-codebase-retrieval__header"
    toolName="Augment Context Engine"
  >
    <span slot="icon" class="c-tool-codebase-retrieval__header__icon"><AugmentIcon /></span>
    <div slot="secondary" class="c-tool-codebase-retrieval__secondary">
      <ShowMore maxHeight={40} showOnHover={true} overflowThreshold={0}>
        {toolUseInput.information_request || toolUseInput.query || ""}
      </ShowMore>
    </div>
  </ToolUseHeader>
</BaseToolComponent>

<style>
  .c-tool-codebase-retrieval__header__icon {
    width: var(--ds-icon-size-2);
    height: var(--ds-icon-size-2);
    /*  Hack to fix icon positioning */
    position: relative;
    top: -1px;
  }

  .c-tool-codebase-retrieval__secondary {
    width: 100%;
    display: block;
  }

  /** Remove padding from show-more because we're in a ToolUse container */
  .c-tool-codebase-retrieval__secondary :global(.c-show-more__button-container) {
    background-color: var(--ds-panel-solid);
    padding: 0;
  }

  /** Remove bottom padding when expanded, otherwise extraneous black space is added */
  .c-tool-codebase-retrieval__secondary :global(.c-show-more--expanded .c-show-more__content) {
    padding-bottom: 0;
  }
</style>
