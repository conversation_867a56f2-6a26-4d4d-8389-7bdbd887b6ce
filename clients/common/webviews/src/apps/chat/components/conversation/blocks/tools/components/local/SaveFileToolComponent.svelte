<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import FilePlusIcon from "$common-webviews/src/design-system/icons/file-plus.svelte";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import {
    stringOrDefault,
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import OpenFileButton from "../OpenFileButton.svelte";
  import SimpleMonaco from "$common-webviews/src/design-system/components/MonacoProvider/SimpleMonaco.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
</script>

<BaseToolComponent showToolOutput={toolUseState.phase === ToolUsePhase.completed}>
  <ToolUseHeader slot="header">
    <FilePlusIcon slot="icon" />
    <Filespan slot="secondary" filepath={stringOrDefault(toolUseInput.path, "")} />
    <OpenFileButton slot="toolAction" path={stringOrDefault(toolUseInput.path, "")} />
    <TextAugment size={1} slot="toolName" class="c-save-file__header__secondary">
      {#if toolUseState.phase === ToolUsePhase.running}
        Creating file...
      {:else if toolUseState.phase === ToolUsePhase.completed}
        Created file
      {/if}
    </TextAugment>
  </ToolUseHeader>

  <div slot="details">
    <SimpleMonaco
      text={stringOrDefault(toolUseInput.file_content, "")}
      pathName={stringOrDefault(toolUseInput.path, "")}
    />
  </div>
</BaseToolComponent>
