<script lang="ts">
  /**
   * Component for rendering task diff markdown into organized task lists
   *
   * Core concept: Parse markdown sections and render each task as a linear list
   *
   * Component lifecycle:
   * [INIT] - Parse markdown and extract task sections
   * [REACTIVE] - Update display when markdown changes
   */
  import type { HydratedTask } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import {
    TaskState,
    TaskUpdatedBy,
  } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import { CurrentConversationTaskStore } from "$common-webviews/src/apps/chat/models/task-store";
  import TaskTree from "$common-webviews/src/apps/chat/components/tasks/task-tree/TaskTree.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { parseTaskDiffFromFullText } from "@augment-internal/sidecar-libs/src/agent/task/task-utils";
  import {
    preprocessTaskTreeVisibility,
    type VisibleHydratedTask,
  } from "$common-webviews/src/apps/chat/components/tasks/utils/task-visibility-utils";

  export let text: string;

  // Parse the full text into sections using existing utilities
  $: sections = parseTaskDiffFromFullText(text);
  $: hasChanges =
    sections.created.length > 0 || sections.updated.length > 0 || sections.deleted.length > 0;

  function createDummyRootTask(tasks: HydratedTask[], sectionName: string): VisibleHydratedTask {
    const preprocessedTasks = tasks.map((task) => preprocessTaskTreeVisibility(task));
    return {
      uuid: `dummy-root-${sectionName}`,
      name: `${sectionName} Tasks`,
      description: `Tasks that were ${sectionName.toLowerCase()}`,
      state: TaskState.NOT_STARTED,
      subTasks: tasks.map((t) => t.uuid),
      subTasksData: preprocessedTasks,
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.AGENT,
      isVisible: true,
    };
  }
</script>

{#if hasChanges}
  <div class="c-task-diff-renderer">
    {#if sections.created.length > 0}
      <div class="c-task-diff-renderer__section">
        <div class="c-task-diff-renderer__section-header">
          <TextAugment size={1} weight="medium" color="success">
            Created Tasks ({sections.created.length})
          </TextAugment>
        </div>
        <div class="c-task-diff-renderer__task-list">
          <TaskTree
            task={createDummyRootTask(sections.created, "Created")}
            taskStore={CurrentConversationTaskStore.createReadOnlyStore(undefined, true)}
            editable={false}
          />
        </div>
      </div>
    {/if}

    {#if sections.updated.length > 0}
      <div class="c-task-diff-renderer__section">
        <div class="c-task-diff-renderer__section-header">
          <TextAugment size={1} weight="medium" color="accent">
            Updated Tasks ({sections.updated.length})
          </TextAugment>
        </div>
        <div class="c-task-diff-renderer__task-list">
          <TaskTree
            task={createDummyRootTask(sections.updated, "Updated")}
            taskStore={CurrentConversationTaskStore.createReadOnlyStore(undefined, true)}
            editable={false}
          />
        </div>
      </div>
    {/if}

    {#if sections.deleted.length > 0}
      <div class="c-task-diff-renderer__section">
        <div class="c-task-diff-renderer__section-header">
          <TextAugment size={1} weight="medium" color="error">
            Deleted Tasks ({sections.deleted.length})
          </TextAugment>
        </div>
        <TaskTree
          task={createDummyRootTask(sections.deleted, "Deleted")}
          taskStore={CurrentConversationTaskStore.createReadOnlyStore(undefined, true)}
          editable={false}
        />
      </div>
    {/if}
  </div>
{:else}
  <div class="c-task-diff-renderer__empty">
    <TextAugment size={1} color="neutral">No task changes to display</TextAugment>
  </div>
{/if}

<style>
  .c-task-diff-renderer {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .c-task-diff-renderer__section {
    border: 1px solid var(--ds-color-neutral-a4);
    border-radius: var(--ds-radius-2);
    background-color: var(--ds-color-neutral-a1);
    overflow: hidden;
  }

  .c-task-diff-renderer__section-header {
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    background-color: var(--ds-color-neutral-a2);
    border-bottom: 1px solid var(--ds-color-neutral-a3);
  }

  .c-task-diff-renderer__empty {
    padding: var(--ds-spacing-1);
    text-align: center;
    background-color: var(--ds-color-neutral-a1);
    border-radius: var(--ds-radius-2);
    border: 1px solid var(--ds-color-neutral-a4);
  }
</style>
