<script lang="ts">
  export let isNotFullHeight: boolean = false;
</script>

<div class="l-chat-wrapper" class:l-chat--not-full-height={isNotFullHeight}>
  <div
    class="l-chat"
    data-testid="main-chat"
    data-vscode-context={JSON.stringify({ preventDefaultContextMenuItems: true })}
  >
    <div class="l-chat__header">
      <slot name="header-area" />
    </div>

    <div class="l-chat__floating">
      <slot name="floating-area" />
    </div>

    <div class="l-chat__msg_area">
      <div class="l-chat__msg_area_body">
        <slot name="message-area" />
      </div>
    </div>
  </div>

  <div class="l-chat__divider_area">
    <slot name="divider-area" />
  </div>

  <div class="l-chat__input_area">
    <slot name="actions-area" />
    <slot name="input-area" />
    <slot name="footer-area" />
  </div>
</div>

<style>
  .l-chat-wrapper {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    overflow: hidden;
    /* Ensure the wrapper maintains proper layout */
    min-height: 0;
    --chat-padding: var(--ds-spacing-3);
  }

  .l-chat {
    padding: var(--chat-padding);
    --chat-inline-padding: var(--ds-spacing-2);
    display: grid;
    width: 100%;
    max-width: 100%;
    flex: 1;

    /* Setting --l-chat-height in the parent can override this */
    height: var(--l-chat-height, 100vh);
    margin: auto;

    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
    grid-template-areas:
      "header-area"
      "floating-area"
      "message-area";
    overflow: hidden;
  }

  .l-chat--not-full-height {
    height: var(--l-chat-height, auto);
    flex: 1;
    padding-bottom: 0;
  }

  .l-chat__header {
    grid-area: header-area;
  }

  .l-chat__floating {
    z-index: var(--z-threads-menu-backdrop);
    grid-area: floating-area;
  }

  .l-chat__msg_area {
    grid-area: message-area;

    display: flex;
    flex-direction: column;

    /* Ensure contents doesn't exceed the available space */
    max-width: 100vw;
    overflow: hidden;
  }

  .l-chat__msg_area_body {
    /* Grow to fill available space */
    flex: 1;
    /* Do not exceed the available space */
    flex-basis: 0;
    overflow: hidden;

    display: contents;
  }

  .l-chat__divider_area {
    /* Position the divider between the main chat and input areas */
    padding: 0 var(--chat-padding);
    padding-bottom: var(--ds-spacing-2);
    /* Ensure it spans the full width */
    width: 100%;
    /* Ensure the divider area doesn't interfere with cursor changes */
    position: relative;
    z-index: 1;
  }

  .l-chat__input_area {
    display: flex;
    flex-direction: column;
    padding: var(--chat-padding);
    padding-top: 0;
    gap: var(--ds-spacing-2);
  }
</style>
