import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/svelte";
import ThreadLengthWarning from "./ThreadLengthWarning.svelte";

describe("ThreadLengthWarning", () => {
  it("renders the warning message", () => {
    const mockOnNewThreadClick = vi.fn();
    const mockOnDismiss = vi.fn();
    render(ThreadLengthWarning, {
      onNewThreadClick: mockOnNewThreadClick,
      onDismiss: mockOnDismiss,
    });

    expect(screen.getByText("Long threads can lead to worse results.")).toBeInTheDocument();
  });

  it("calls onNewThreadClick when the New Thread button is clicked", async () => {
    const mockOnNewThreadClick = vi.fn();
    const mockOnDismiss = vi.fn();
    render(ThreadLengthWarning, {
      onNewThreadClick: mockOnNewThreadClick,
      onDismiss: mockOnDismiss,
    });

    const newThreadButton = screen.getByTestId("new-thread-warning-button");
    await fireEvent.click(newThreadButton);

    expect(mockOnNewThreadClick).toHaveBeenCalledTimes(1);
  });

  it("calls onDismiss when the dismiss button is clicked", async () => {
    const mockOnNewThreadClick = vi.fn();
    const mockOnDismiss = vi.fn();
    render(ThreadLengthWarning, {
      onNewThreadClick: mockOnNewThreadClick,
      onDismiss: mockOnDismiss,
    });

    const dismissButton = screen.getByTestId("dismiss-thread-warning-button");
    await fireEvent.click(dismissButton);

    expect(mockOnDismiss).toHaveBeenCalledTimes(1);
  });
});
