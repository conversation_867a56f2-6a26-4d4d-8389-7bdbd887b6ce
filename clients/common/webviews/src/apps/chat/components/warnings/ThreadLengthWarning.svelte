<script lang="ts">
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import MessagePlus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message-plus.svg?component";
  import XMark from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import ChatInputBanner from "./ChatInputBanner.svelte";

  export let onNewThreadClick: () => void;
  export let onDismiss: () => void;
</script>

<ChatInputBanner text="Long threads can lead to worse results.">
  <div class="l-thread-length-warning__buttons" slot="right-content">
    <TextTooltipAugment
      content="New Thread"
      triggerOn={[TooltipTriggerOn.Hover, TooltipTriggerOn.Click]}
    >
      <IconButtonAugment
        size={1}
        variant="ghost-block"
        color="neutral"
        on:click={onNewThreadClick}
        data-testid="new-thread-warning-button"
      >
        <MessagePlus />
      </IconButtonAugment>
    </TextTooltipAugment>
    <TextTooltipAugment content="Dismiss" triggerOn={[TooltipTriggerOn.Hover]}>
      <IconButtonAugment
        size={1}
        variant="ghost-block"
        color="neutral"
        on:click={onDismiss}
        data-testid="dismiss-thread-warning-button"
      >
        <XMark />
      </IconButtonAugment>
    </TextTooltipAugment>
  </div>
</ChatInputBanner>

<style>
  .l-thread-length-warning__buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .l-thread-length-warning__buttons :global(svg) {
    width: 14px;
    height: 14px;
    fill: var(--icon-color, currentColor);
  }
</style>
