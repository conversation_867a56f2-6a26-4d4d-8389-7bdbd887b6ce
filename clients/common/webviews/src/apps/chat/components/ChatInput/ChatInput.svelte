<script lang="ts">
  import RichTextEditorAugment from "$common-webviews/src/design-system/components/RichTextEditorAugment";
  import DropZone from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/DropZone/DropZone.svelte";
  import Image from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Image";
  import Keybindings from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Keybindings/Keybindings.svelte";
  import type { MentionsUpdatedData } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Mention/types";
  import Placeholder from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Placeholder";
  import type RichTextEditorRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/Root.svelte";
  import type { ContentData } from "$common-webviews/src/design-system/components/RichTextEditorAugment/types";
  import type { JSONContent } from "@tiptap/core";

  import { GitReferenceModel } from "$common-webviews/src/apps/remote-agent-manager/models/git-reference-model";
  import { getMessageRenderOptions } from "$common-webviews/src/apps/remote-agent-manager/models/message-render-options";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import BadgeRoot from "$common-webviews/src/design-system/components/BadgeAugment/BadgeRoot.svelte";
  import { RemoteAgentStatus } from "$vscode/src/remote-agent-manager/types";
  import type { Exchange } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import { AgentSessionEventName } from "@augment-internal/sidecar-libs/src/metrics/types";
  import { getContext, onDestroy, tick } from "svelte";
  import tippy from "tippy.js";
  import { type AgentConversationModel } from "../../models/agent-conversation-model";
  import { DEFAULT_MAX_CHAT_INPUT_CHARS } from "../../models/chat-flags-model";
  import { type ChatModel } from "../../models/chat-model";
  import { ConversationModel } from "../../models/conversation-model";
  import { type SlashCommandModel } from "../../models/slash-command-model";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../models/task-store";
  import { type ToolsWebviewModel } from "../../models/tools-webview-model";
  import { AgentExchangeStatus, ExchangeStatus } from "../../types/chat-message";
  import type { IChatMentionable } from "../../types/mention-option";
  import { ToolUsePhase } from "../../types/tool-use-state";
  import { getChatInputContext } from "../../utils/chat-input-context";
  import ToggleModeButton from "../buttons/ToggleModeButton.svelte";
  import ContextBar from "../context/ContextBar.svelte";
  import InputActionBar from "../InputActionBar";
  import ModelPicker from "../ModelPicker";
  import SlashCommandInput from "../slash-commands/SlashCommandInput.svelte";
  import AtMentions from "./AtMentions.svelte";
  import SlashActions from "./SlashActions.svelte";

  // Get chat model from context
  const chatModel = getContext<ChatModel>("chatModel");
  const toolsWebviewModel = getContext<ToolsWebviewModel>("toolsWebviewModel");
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  const remoteAgentsModel = getContext<RemoteAgentsModel | undefined>(RemoteAgentsModel.key);
  const gitRefModel = getContext<GitReferenceModel>(GitReferenceModel.key);
  const slashCommandModel = getContext<SlashCommandModel>("slashCommandModel");
  const taskStore = getContext<ICurrentConversationTaskStore | undefined>(
    CurrentConversationTaskStore.key,
  );
  const flagsModel = chatModel.flags;

  $: isRemoteAgent = !!$remoteAgentsModel?.isActive;

  $: messageRenderOptions = getMessageRenderOptions($remoteAgentsModel);

  const { currentConversationModel } = chatModel;
  const { activeCommand } = slashCommandModel;

  // Props
  export let editable = true;
  export let placeholder = "Ask or instruct Augment...";
  export let hasToggleModeButton = true;
  export let hasSendButton = true;
  export let richTextEditorRoot: RichTextEditorRoot | undefined = undefined;

  // we need to split the reactive storedContent statement into two lines
  // to make sure it's properly updated
  // eslint-disable-next-line @typescript-eslint/naming-convention
  $: draftExchange = $currentConversationModel.draftExchange || { rich_text_json_repr: undefined };
  $: storedContent = draftExchange.rich_text_json_repr || {
    type: "doc",
    content: [{ type: "paragraph" }],
  };

  function onContentChanged(data: ContentData) {
    $currentConversationModel.saveDraftExchange(data.rawText, data.richTextJsonRepr);
  }
  function onMentionItemsUpdated(data: MentionsUpdatedData<IChatMentionable>) {
    $currentConversationModel.saveDraftMentions(data.current);
  }
  let lastConversationType: string | undefined = undefined;
  $: conversationType = $remoteAgentsModel?.isActive
    ? "remoteAgent"
    : $currentConversationModel.extraData?.isAgentConversation
      ? "localAgent"
      : "chat";
  $: {
    if (
      $chatModel.conversations[$currentConversationModel.id] &&
      ConversationModel.isNew($chatModel.conversations[$currentConversationModel.id]) &&
      lastConversationType !== conversationType
    ) {
      lastConversationType = conversationType;
      if (conversationType !== "remoteAgent") {
        tick().then(() => forceEditorFocus());
      }
    }
  }
  let lastThreadId: string | undefined = undefined;
  $: {
    if (lastThreadId !== $currentConversationModel.id) {
      lastThreadId = $currentConversationModel.id;
      if (conversationType !== "remoteAgent") {
        tick().then(() => forceEditorFocus());
      }
    }
  }

  // Whenever a new conversation is created, we need to focus the editor
  const cleanupNewConvoListener = currentConversationModel.onNewConversation(() =>
    forceEditorFocus(),
  );
  onDestroy(cleanupNewConvoListener);

  // Focus the editor when remote agent transitions from starting to running/idle
  let lastRemoteAgentStatus: RemoteAgentStatus | undefined = undefined;
  $: {
    const currentStatus = $remoteAgentsModel?.currentAgent?.status;
    if (
      lastRemoteAgentStatus === RemoteAgentStatus.agentStarting &&
      (currentStatus === RemoteAgentStatus.agentRunning ||
        currentStatus === RemoteAgentStatus.agentIdle)
    ) {
      tick().then(() => forceEditorFocus());
    }
    lastRemoteAgentStatus = currentStatus;
  }

  // When agentic, hide / commands
  $: ({ isCurrConversationAgentic, agentExchangeStatus: ideAgentExchangeStatus } =
    agentConversationModel);

  $: currentSendMode = $chatModel.sendModeModel.mode;
  $: _chatInputContext = getChatInputContext(
    $chatModel,
    agentConversationModel,
    toolsWebviewModel,
    slashCommandModel,
    $currentSendMode,
    taskStore,
    $remoteAgentsModel,
    gitRefModel,
    richTextEditorRoot,
  );
  $: showCancelButton = _chatInputContext.showCancelButton;
  $: messageLength = $currentConversationModel.draftExchange?.request_message?.length || 0;
  $: isDisabled = _chatInputContext.isDisabled;
  $: disabledReason = _chatInputContext.disabledReason;

  $: action = _chatInputContext.action;
  $: cancelAction = _chatInputContext.cancelAction;

  /** Key for the RichTextEditorAugment.Root to force it to be recreated */
  $: richTextEditorKey = `${$isCurrConversationAgentic}-${$remoteAgentsModel?.isActive}-${placeholder}`;

  $: shortcuts = {
    /* eslint-disable @typescript-eslint/naming-convention */
    Enter: action,
    /* eslint-disable @typescript-eslint/naming-convention */
    Escape: unfocusEditor,
  };

  // Hooks provided by children that we bind to
  let atMentionsComponent: AtMentions | undefined = undefined;
  const requestEditorFocus = () => richTextEditorRoot?.requestFocus();
  const forceEditorFocus = () => richTextEditorRoot?.forceFocus?.();
  const unfocusEditor = () => {
    richTextEditorRoot?.blur();
    return true;
  };

  $: conversationModel = $chatModel.currentConversationModel;
  let lastMessageCancelled = false;
  $: {
    const lastExchange = $conversationModel?.lastExchange?.status;
    const lastExchangeCancelled = lastExchange === ExchangeStatus.cancelled;
    const lastToolPhase = $conversationModel?.getLastToolUseState().phase;
    const lastToolCancelled = lastToolPhase === ToolUsePhase.cancelled;
    lastMessageCancelled = lastExchangeCancelled || lastToolCancelled;
  }
  $: showStopAgent =
    !$remoteAgentsModel?.isActive &&
    $isCurrConversationAgentic &&
    $ideAgentExchangeStatus !== AgentExchangeStatus.notRunning;
  // only show resend if textbox is empty
  $: showResend =
    !$remoteAgentsModel?.isActive &&
    lastMessageCancelled &&
    $currentConversationModel.draftExchange?.request_message === "";
  // Change placeholder text if resending

  $: lastRequestMessage = $conversationModel?.lastExchange?.request_message;
  $: placeholderText = showResend && lastRequestMessage ? lastRequestMessage : placeholder;

  // Function to rewrite the prompt using the current conversation model
  async function rewritePrompt() {
    // Handle enhance prompt button click

    try {
      // Get the current prompt text
      /* eslint-disable @typescript-eslint/naming-convention */
      const currentPrompt = $currentConversationModel.draftExchange?.request_message || "";
      /* eslint-enable @typescript-eslint/naming-convention */

      // Check if there's a prompt to enhance

      // If there's no text, don't do anything
      if (!currentPrompt || currentPrompt.trim() === "") {
        // Exit if there's no prompt to enhance
        return;
      }

      // Create an instruction to enhance the user's prompt
      // const instruction =
      //   "Generate an enhanced version of this prompt. Reply only with the enhanced prompt, no explanations or other commentary.";

      const instruction =
        "Here is an instruction that I'd like to give you, but it needs to be improved. " +
        "Rewrite and enhance this instruction to make it clearer, more specific, " +
        "less ambiguous, and correct any mistakes. " +
        "Do not use any tools: reply immediately with your answer, even if you're not sure. " +
        "Consider the context of our conversation history when enhancing the prompt. " +
        "Reply with the following format:\n\n" +
        "### BEGIN RESPONSE ###\n" +
        "Here is an enhanced version of the original instruction that is more specific and clear:\n" +
        "<augment-enhanced-prompt>enhanced prompt goes here</augment-enhanced-prompt>\n\n" +
        "### END RESPONSE ###\n\n" +
        "Here is my original instruction:";

      // Generate an enhanced version of this prompt. Reply only with the enhanced prompt, no explanations or other commentary.";

      // Store the original content in case we need to restore it
      const originalContent = $currentConversationModel.draftExchange?.rich_text_json_repr;

      try {
        let chatHistory: Exchange[] | undefined = undefined;
        if ($remoteAgentsModel?.isActive) {
          // The remote agents model active conversation is different fom the current conversation model, so we need to inject the remote
          // agent conversation to sendSilentExchange if the user is in a remote agent conversation
          // Convert from ExchangeWithStatus to Exchange
          chatHistory = $remoteAgentsModel.getCurrentChatHistory().map((exchange) => ({
            /* eslint-disable @typescript-eslint/naming-convention */
            request_id: exchange.request_id || "",
            request_message: exchange.request_message,
            response_text: exchange.response_text || "",
            /* eslint-enable @typescript-eslint/naming-convention */
          }));
        }

        // Create a temporary exchange to send to the model
        let { responseText } = await $currentConversationModel.sendSilentExchange({
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: `${instruction}\n\n${currentPrompt}`,
          /* eslint-enable @typescript-eslint/naming-convention */
          disableRetrieval: false,
          chatHistory,
        });

        // Report the metric
        $currentConversationModel.extensionClient.reportAgentSessionEvent({
          eventName: AgentSessionEventName.enhancedPrompt,
          conversationId: $currentConversationModel.id,
        });

        // Process the enhanced prompt from the LLM

        // Extract the enhanced prompt from between the tags
        let enhancedPrompt = "";
        const tagPattern = /<augment-enhanced-prompt>([\s\S]*?)<\/augment-enhanced-prompt>/;

        const match = responseText.match(tagPattern);

        if (match && match[1]) {
          // If we found the tags, use the content between them
          enhancedPrompt = match[1].trim();
        } else {
          // Fallback: use the original prompt if tags aren't found
          enhancedPrompt = currentPrompt;
          console.warn("Enhanced prompt tags not found in response, using original prompt");

          // Show a popup notification that enhancement failed
          const errorTooltip = document.createElement("div");
          errorTooltip.className = "enhance-prompt-error-tooltip";
          errorTooltip.textContent = "Failed to enhance prompt, sorry about that";
          document.body.appendChild(errorTooltip);

          // Create a tooltip instance
          const tooltip = tippy(errorTooltip, {
            content: errorTooltip,
            trigger: "manual",
            placement: "top",
            theme: "error",
            duration: [300, 2000], // Show quickly, hide after 2 seconds
            onHidden: () => {
              errorTooltip.remove(); // Clean up the DOM
            },
          });

          // Position near the rewrite button and show
          const rewriteButton = document.querySelector(".c-rewrite-prompt-button");
          if (rewriteButton) {
            tooltip.setProps({
              getReferenceClientRect: () => rewriteButton.getBoundingClientRect(),
            });
            tooltip.show();

            // Auto-hide after 3 seconds
            setTimeout(() => {
              tooltip.hide();
            }, 3000);
          } else {
            errorTooltip.remove(); // Clean up if button not found
          }
        }

        // Create enhanced content for the editor

        if (enhancedPrompt && enhancedPrompt.trim() !== "") {
          // Get the original content to preserve all non-text nodes
          const originalContent = $currentConversationModel.draftExchange?.rich_text_json_repr;

          // Create a new enhanced content that preserves all non-paragraph nodes
          let enhancedContent: JSONContent;

          if (originalContent && typeof originalContent === "object") {
            // Create a copy of the original content to avoid mutating it
            enhancedContent = { ...originalContent };

            if (enhancedContent.content && Array.isArray(enhancedContent.content)) {
              // Create a new content array
              const newContent: JSONContent[] = [];

              // Flag to track if we've already processed the first paragraph
              let firstParagraphReplaced = false;

              // Process each top-level node
              for (const node of enhancedContent.content) {
                if (node.type === "paragraph" && !firstParagraphReplaced) {
                  // Replace the first paragraph with the enhanced prompt
                  // Split the enhanced prompt into lines
                  const lines = enhancedPrompt.split("\n");

                  // Create a new paragraph with the enhanced prompt text
                  const newParagraph: JSONContent = {
                    type: "paragraph",
                    content: [],
                  };

                  // Add each line with hardBreak nodes between them
                  lines.forEach((line, index) => {
                    // Add the text node for this line
                    if (line.length > 0) {
                      newParagraph.content?.push({
                        type: "text",
                        text: line,
                      });
                    }

                    // Add a hardBreak after each line except the last one
                    if (index < lines.length - 1) {
                      newParagraph.content?.push({
                        type: "hardBreak",
                      });
                    }
                  });

                  // Add the new paragraph to the content
                  newContent.push(newParagraph);

                  // Mark that we've replaced the first paragraph
                  firstParagraphReplaced = true;
                } else if (node.type !== "paragraph") {
                  // Preserve all non-paragraph nodes
                  newContent.push(node);
                }
                // Skip all other paragraph nodes (they will be deleted)
              }

              // Update the content with our new array
              enhancedContent.content = newContent;
            }
          } else {
            // If there's no original content or it's not an object, create simple content
            enhancedContent = {
              type: "doc",
              content: [
                {
                  type: "paragraph",
                  content: [{ type: "text", text: enhancedPrompt }],
                },
              ],
            };
          }

          // Set the content in a single operation
          $currentConversationModel.saveDraftExchange(enhancedPrompt, enhancedContent);
          // Position cursor at beginning after content update

          // We already set the content above, no need to do it again

          // Focus the editor and position cursor at beginning
          setTimeout(() => {
            // First focus the editor
            forceEditorFocus();

            // Use the Selection API to position cursor at beginning
            try {
              // Find the editor element
              const editorElement = document.querySelector(".ProseMirror");
              if (editorElement) {
                // Find the first paragraph and text node
                const firstParagraph = editorElement.querySelector("p");
                if (firstParagraph) {
                  // Create a selection at the beginning
                  const range = document.createRange();
                  const selection = window.getSelection();

                  // Set selection at the beginning of the paragraph
                  range.setStart(firstParagraph, 0);
                  range.collapse(true);

                  // Apply the selection
                  if (selection) {
                    selection.removeAllRanges();
                    selection.addRange(range);
                  }
                }
              }
            } catch (e) {
              // Error occurred while positioning cursor
            }
          }, 200); // TODO(guy) make this more robust, don't rely on magic timeout value
        } else {
          // If we didn't get a valid response, restore the original content
          $currentConversationModel.saveDraftExchange(
            /* eslint-disable @typescript-eslint/naming-convention */
            originalContent ? $currentConversationModel.draftExchange?.request_message || "" : "",
            /* eslint-enable @typescript-eslint/naming-convention */
            originalContent || { type: "doc", content: [] },
          );
          // Original content restored due to invalid response
        }
      } catch (error) {
        // Error occurred while calling LLM
        // Restore the original content on error
        $currentConversationModel.saveDraftExchange(
          /* eslint-disable @typescript-eslint/naming-convention */
          originalContent ? $currentConversationModel.draftExchange?.request_message || "" : "",
          /* eslint-enable @typescript-eslint/naming-convention */
          originalContent || { type: "doc", content: [] },
        );
      }

      // Focus the editor
      forceEditorFocus();
    } catch (error) {
      // Error occurred in rewritePrompt function
    }
  }
</script>

<svelte:window
  on:focus={() => {
    if (isRemoteAgent) return;
    requestEditorFocus();
  }}
  on:blur={$chatModel.saveImmediate}
  on:beforeunload={$chatModel.saveImmediate}
/>

<div class="c-chat-input" data-testid="design-system-chat-input">
  <!--
  We need to recreate the editor when the conversation type changes.

  More details: https://app.staging.augmentcode.com/share/mDoyO1Sk1kk
  -->
  {#key richTextEditorKey}
    <RichTextEditorAugment.Root {editable} bind:this={richTextEditorRoot}>
      <slot name="banner" slot="banner" />
      <!-- Header -->
      <svelte:fragment slot="header">
        {#if !messageRenderOptions.doHideContextBar}
          <ContextBar {chatModel} />
        {/if}
      </svelte:fragment>

      <!-- Main content and plugins -->
      <Keybindings {shortcuts} />
      {#if !messageRenderOptions.doHideAtMentions}
        <AtMentions bind:this={atMentionsComponent} {requestEditorFocus} {onMentionItemsUpdated} />
      {/if}
      {#if !$isCurrConversationAgentic && !messageRenderOptions.doHideSlashActions}
        <SlashActions />
      {/if}
      <Placeholder placeholder={placeholderText} />
      <RichTextEditorAugment.Content content={storedContent} {onContentChanged} />
      {#if $chatModel.flags.enableChatMultimodal && !messageRenderOptions.doHideMultimodalActions}
        <Image
          saveImage={$chatModel.saveImage}
          deleteImage={$chatModel.deleteImage}
          renderImage={$chatModel.renderImage}
        />
        <DropZone />
      {/if}

      <!-- Used for ActionsV1. Proceed at your own peril. -->
      {#if $activeCommand && !messageRenderOptions.doHideSlashActions}
        <SlashCommandInput />
      {/if}

      <!-- Footer -->
      <InputActionBar.Root slot="footer">
        <svelte:fragment slot="leftAlign">
          {#if $flagsModel.enableAgentMode && hasToggleModeButton}
            <ToggleModeButton {chatModel} {agentConversationModel} />
          {/if}
          {#if $chatModel.flags.enableChatMultimodal && !messageRenderOptions.doHideMultimodalActions}
            <InputActionBar.AddFileButton accept=".png,.jpg,.jpeg" />
          {/if}
          {#if !$isCurrConversationAgentic && !messageRenderOptions.doHideSlashActions}
            <InputActionBar.ActionsMenu onCloseDropdown={requestEditorFocus} />
          {/if}
          {#if !messageRenderOptions.doHideAtMentions}
            <InputActionBar.ContextMenu
              onCloseDropdown={requestEditorFocus}
              onInsertMentionable={atMentionsComponent?.insertMentionNode}
            />
          {/if}

          <!-- Character counter -->
          {#if messageLength > DEFAULT_MAX_CHAT_INPUT_CHARS}
            <span class="character-counter">
              {messageLength}/{DEFAULT_MAX_CHAT_INPUT_CHARS}
            </span>
          {/if}
          {#if remoteAgentsModel?.isActive}
            <BadgeRoot color="premium" size={1} variant="soft">Beta</BadgeRoot>
          {/if}

          <!-- Model Picker -->
          {#if $isCurrConversationAgentic && $flagsModel.enableModelRegistry}
            <ModelPicker />
          {/if}
        </svelte:fragment>
        <svelte:fragment slot="rightAlign">
          {#if isRemoteAgent}
            <InputActionBar.NotifyButton agentId={$remoteAgentsModel?.currentAgentId} />
          {/if}
          {#if showCancelButton}
            <InputActionBar.CancelButton {cancelAction} />
          {:else if showStopAgent}
            <InputActionBar.CancelButton cancelAction={agentConversationModel.interruptAgent} />
          {:else if showResend}
            <InputActionBar.ResendButton resendAction={$conversationModel?.resendLastExchange} />
          {:else}
            <InputActionBar.RewritePromptButton {isDisabled} onRewrite={rewritePrompt} />
            {#if hasSendButton}
              <InputActionBar.SendButton {isDisabled} {disabledReason} primaryAction={action} />
            {/if}
          {/if}
        </svelte:fragment>
      </InputActionBar.Root>
    </RichTextEditorAugment.Root>
  {/key}
</div>

<style>
  .c-chat-input {
    width: 100%;
    height: 100%;

    max-width: 100%;
    max-height: 100%;

    gap: var(--ds-spacing-2);
    display: flex;
    flex-direction: column;
    /* to prevent the toggle mode tooltip from being overlapped */
    position: relative;
    /* Higher z-index to ensure dropdowns appear above ThreadsList */
    z-index: var(--z-chat-input);
  }

  .character-counter {
    font-size: var(--ds-font-size-1);
    margin-left: var(--ds-spacing-2);
    color: var(--ds-color-error-a11);
    font-weight: var(--ds-font-weight-bold);
  }

  :global(.enhance-prompt-error-tooltip) {
    background-color: var(--ds-color-error-a3);
    color: var(--ds-color-error-a11);
    padding: var(--ds-spacing-2);
    border-radius: var(--ds-radius-2);
    font-size: var(--ds-font-size-1);
    box-shadow: var(--ds-shadow-2);
    max-width: 250px;
    text-align: center;
  }

  :global(.tippy-box[data-theme~="error"]) {
    background-color: var(--ds-color-error-a3);
    color: var(--ds-color-error-a11);
  }

  :global(.tippy-box[data-theme~="error"] .tippy-arrow) {
    color: var(--ds-color-error-a3);
  }
</style>
