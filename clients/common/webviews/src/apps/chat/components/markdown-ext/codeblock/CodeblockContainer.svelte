<script lang="ts">
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";
  import { DEFAULT_MONACO_OPTIONS } from "$common-webviews/src/common/components/markdown/codeblock/monaco";
  import SimpleMonaco from "$common-webviews/src/design-system/components/MonacoProvider/SimpleMonaco.svelte";
  import {
    getFileDirectory,
    getFilename,
    normalizeFilePath,
  } from "$common-webviews/src/common/utils/file-paths";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import Expand from "$common-webviews/src/design-system/icons/augment/expand.svelte";
  import DeferredLoadingAugment from "$common-webviews/src/design-system/components/DeferredLoadingAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { type Tokens } from "marked";
  import type { ICodeblockActionButton, ICodeblockMetadata } from "../utils";
  import { ExchangeStatus } from "../../../types/chat-message";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import CodeblockSuccessfulButton from "$common-webviews/src/apps/chat/components/markdown-ext/codeblock/CodeblockSuccessfulButton.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import DotsHorizontal from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import { themeStore } from "$common-webviews/src/common/hosts/user-themes/theme-store";
  import IconFilePath from "$common-webviews/src/common/components/icon-file-path/IconFilePath.svelte";
  import { STICKY_HEADER_TOP_PX } from "$common-webviews/src/design-system/components/CollapsibleAugment/constants";

  export let token: Tokens.Code;
  export let codeblockMetadata: ICodeblockMetadata | undefined;
  export let collapsed: boolean = true;
  export let element: HTMLDivElement | undefined = undefined;
  let isHeaderStuck: boolean;

  export let lineHeight = DEFAULT_MONACO_OPTIONS.lineHeight ?? 18;
  /** Number of codeblock lines to show when truncated */
  export let truncateLines = 10;

  export let requestStatus: ExchangeStatus | undefined;
  export let primaryButton: ICodeblockActionButton;
  export let goToFileButton: ICodeblockActionButton;
  export let buttons: ICodeblockActionButton[];
  export let truncate: boolean = false;
  export let opened: boolean = false;
  export let codeContainerElement: HTMLDivElement | undefined = undefined;

  $: themeCategory = $themeStore?.category;
  $: normalizedPath = normalizeFilePath(codeblockMetadata?.relPath ?? "");
  $: language = codeblockMetadata?.language ?? token.lang;

  $: numLinesOfCode = (token.text.match(/\r?\n/g) || []).length + 1;
  $: deferredEstimatedHeight = Math.min(numLinesOfCode, truncateLines) * lineHeight;
  $: isMessageLoading = requestStatus === ExchangeStatus.sent;

  let toggle: (() => void) | undefined = undefined;
</script>

<div
  bind:this={element}
  class:is-sticky={isHeaderStuck}
  class={`c-codeblock c-codeblock__${themeCategory}`}
  style={`--codeblock-line-height: ${lineHeight}px; --codeblock-truncate-lines: ${truncateLines};`}
>
  <CollapsibleAugment
    stickyHeaderTop={STICKY_HEADER_TOP_PX}
    stickyHeader
    bind:isHeaderStuck
    bind:collapsed
    bind:toggle
    expandable
    class="c-codeblock__container"
  >
    <div slot="header" class="c-codeblock__header-content">
      <CollapseButtonAugment class="c-codeblock__collapse-btn" />
      {#if codeblockMetadata?.relPath}
        <div class="c-codeblock__relpath">
          <TextTooltipAugment content={normalizedPath} triggerOn={[TooltipTriggerOn.Hover]}>
            <ButtonAugment
              variant="ghost-block"
              color="neutral"
              size={1}
              on:click={goToFileButton.onClick}
              class="c-codeblock__filename"
            >
              <IconFilePath
                value={[]}
                filepath={getFilename(normalizedPath)}
                onCodeAction={goToFileButton.onClick}
                codeActions={[
                  {
                    action: "active",
                    label: "Focus in Editor",
                    icon: "open-in-new-window",
                  },
                ]}
              />
            </ButtonAugment>
          </TextTooltipAugment>
          {#if getFileDirectory(normalizedPath)}
            <span class="c-codeblock__directory">
              {getFileDirectory(normalizedPath)}
            </span>
          {/if}
        </div>
      {/if}
      <div class="c-codeblock__action-bar-right">
        {#if isMessageLoading}
          <div class="c-codeblock__loading">
            <SpinnerAugment size={1} />
          </div>
        {:else}
          <CodeblockSuccessfulButton {primaryButton} />
        {/if}
        <DropdownMenu.Root nested={false}>
          <DropdownMenu.Trigger>
            <IconButtonAugment variant="ghost-block" color="neutral" size={1}>
              <DotsHorizontal />
            </IconButtonAugment>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content side="bottom" align="end">
            {#each buttons as button}
              <DropdownMenu.Item onSelect={button.onClick} disabled={button.disabled}>
                {button.buttonText}
              </DropdownMenu.Item>
            {/each}
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      </div>
    </div>

    <div
      class="c-codeblock__container-inner"
      class:c-codeblock__truncated={truncate}
      bind:this={codeContainerElement}
    >
      <slot>
        <DeferredLoadingAugment minHeight="{deferredEstimatedHeight}px">
          <SimpleMonaco
            options={DEFAULT_MONACO_OPTIONS}
            text={token.text}
            lang={language || token.lang}
          />
        </DeferredLoadingAugment>
      </slot>
      {#if truncate}
        <div class="c-codeblock__truncated-surface">
          <ButtonAugment
            variant="soft"
            color="neutral"
            size={1}
            on:click={() => {
              truncate = false;
              opened = true;
            }}
          >
            <Expand slot="iconLeft" />
            Expand
          </ButtonAugment>
        </div>
      {/if}
    </div>
  </CollapsibleAugment>
</div>

<style>
  .c-codeblock {
    margin: var(--ds-spacing-2) 0;
  }

  .c-codeblock :global(.c-collapsible__header-inner) {
    padding: var(--ds-spacing-1);
  }

  .c-codeblock :global(.c-collapsible__header) {
    /**
     * When the window is small, we need the left side to shrink faster in
     * order for the directory to not overflow into the buttons on the right
     * side.
     */
    flex-shrink: 10;
    /**
     * But we still want the left side to take all the available space
     */
    width: 100%;
  }

  .c-codeblock__header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .c-codeblock__collapse-btn {
    margin-right: var(--ds-spacing-1);
  }

  .c-codeblock__light .c-codeblock__truncated-surface {
    background: var(--light-gradient);
  }
  .c-codeblock__relpath {
    display: flex;
    flex-direction: row;
    align-items: center;
    min-width: 0;
    width: 100%;
    flex: 1;
  }

  .c-codeblock__relpath :global(.l-tooltip-trigger) {
    max-width: 100%;
  }
  .c-codeblock__relpath:has(.c-codeblock__directory) :global(.l-tooltip-trigger) {
    max-width: 65%;
  }

  .c-codeblock__relpath :global(.c-codeblock__filename) {
    width: 100%;
    color: var(--ds-color-neutral-12);
    max-width: 100%;
    overflow: hidden;
  }

  .c-codeblock__relpath :global(.c-codeblock__filename .c-button--content) {
    display: block;
    overflow: hidden;
    text-wrap: nowrap;
    text-overflow: ellipsis;
    width: 100%;
  }

  .c-codeblock__relpath :global(.c-icon-file-path) {
    width: 100%;
    overflow: hidden;
  }

  .c-codeblock__relpath :global(.c-icon-file-path__path) {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    direction: rtl;
  }

  .c-codeblock__directory {
    color: var(--ds-color-neutral-10);
    overflow: hidden;
    direction: rtl;
    text-wrap: nowrap;
    text-overflow: ellipsis;
  }

  .c-codeblock__action-bar-right {
    padding-right: var(--ds-spacing-1);
    display: flex;
    /* Words with spaces get automatically wrapped */
    text-wrap: nowrap;
    /** Right align the buttons */
    justify-content: end;
  }

  .c-codeblock__loading {
    display: flex;
    align-items: center;
    margin-right: var(--ds-spacing-1);
  }

  /* Styles for the CollapsibleAugment container */
  .c-codeblock :global(.c-codeblock__container) {
    --base-btn-disabled-color: var(--augment-text-color);
    gap: 0;
    padding: 0;
    border: 0;
    overflow: visible;
  }

  /* Styles for the inner container that holds the code */
  .c-codeblock__container-inner {
    position: relative;
  }

  .c-codeblock__truncated {
    max-height: calc(var(--codeblock-line-height) * var(--codeblock-truncate-lines));
    overflow: hidden;
  }

  .c-codeblock__truncated-surface {
    /**
     * Gradients to fade out the bottom of the codeblock to represent that
     * there is more content hidden.
     */
    --dark-gradient: linear-gradient(180deg, rgba(17, 17, 19, 0.15) 0%, rgba(17, 17, 19, 1) 100%);
    --light-gradient: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 1) 100%
    );

    position: absolute;
    bottom: 0;
    width: 100%;
    display: flex;
    align-items: center;
    padding: var(--ds-spacing-2);
    background: var(--dark-gradient);
  }

  .c-codeblock__truncated-surface :global(.c-base-btn) {
    backdrop-filter: blur(4px);
  }

  .c-codeblock__truncated-surface :global(.c-base-btn) {
    width: 100%;
    height: 100%;
  }

  /** Overwrite the default Monaco styles to align with designs */
  .c-codeblock__container-inner :global(.c-codeblock__monaco) {
    border: none;
    border-radius: unset;

    & .monaco-editor {
      outline: none;
    }

    &:has(.monaco-editor.focused) {
      border: none;
    }
  }

  .c-codeblock__container-inner :global(.monaco-editor-background),
  .c-codeblock__container-inner :global(.monaco-editor),
  .c-codeblock__container-inner :global(.monaco-editor .margin) {
    /* Override monaco's background color with the block container's color */
    background-color: var(--ds-panel-solid);
  }
</style>
