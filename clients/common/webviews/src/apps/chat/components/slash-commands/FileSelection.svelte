<script lang="ts">
  /* adapted from common/webviews/src/apps/chat/components/context/SelectionItem.svelte */
  import { type FileDetails } from "$vscode/src/webview-providers/webview-messages";

  export let fileSelection: FileDetails;

  $: filepath = fileSelection.pathName || "";
  let startLine: number | undefined, endLine: number | undefined;
  $: {
    if (fileSelection.fullRange) {
      startLine = fileSelection.fullRange.startLineNumber;
      endLine = fileSelection.fullRange.endLineNumber;
    } else if (fileSelection.range) {
      startLine = fileSelection.range.start;
      endLine = fileSelection.range.stop;
    }
  }
  // Remove all empty parts from the path
  $: parts = filepath.split("/").filter((part) => part.length > 0);
  $: filename = parts[parts.length - 1];
</script>

<span class="c-selection-item">
  {filename}
  {#if startLine !== undefined && endLine !== undefined}
    {#if startLine === endLine}
      L{startLine + 1}
    {:else}
      L{startLine + 1}-{endLine + 1}
    {/if}
  {/if}
</span>

<style>
  .c-selection-item {
    background-color: var(--ds-panel-solid);
    border: 1px solid var(--augment-border-color);
    color: var(--ds-color-info-a9);
    padding: 0 var(--ds-spacing-1);
  }
</style>
