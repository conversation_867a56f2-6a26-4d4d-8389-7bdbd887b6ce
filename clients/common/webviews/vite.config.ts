import { defineConfig, coverageConfigDefaults, Plugin } from "vitest/config";
import { svelte } from "@sveltejs/vite-plugin-svelte";
import { svelteTesting } from "@testing-library/svelte/vite";
import svg from "@poppanator/sveltekit-svg";

import replace from "@rollup/plugin-replace";
import { resolve } from "path";
import { randomBytes } from "crypto";
import terser from "@rollup/plugin-terser";
import json5 from "vite-plugin-json5";
import { inlineMonacoBootstrap } from "./scripts/vite-plugins/inline-monaco-bootstrap";

const isDev = process.argv.includes("--watch") || process.env.AUGMENT_JS_ENV === "development";

const plugins = [
  /**
   * Some libraries provide testing utilities for backend and non-prod environments
   * using process.env.NODE_ENV. This is a common pattern in the JS community, but
   * we want to replace it with a string literal when bundling because most environments
   * will not have access to these variables at runtime.
   *
   * See this for details:
   * - https://atomiks.github.io/tippyjs/v6/faq/#i-m-getting-uncaught-referenceerror-process-is-not-defined
   */
  replace({
    // eslint-disable-next-line @typescript-eslint/naming-convention
    "process.env.NODE_ENV": JSON.stringify(process?.env?.NODE_ENV || "production"),
    preventAssignment: true,
  }),
];

if (!isDev) {
  plugins.push(
    terser({
      format: {
        comments: false,
      },
    }),
  );
}

// https://vitejs.dev/config/
export default defineConfig({
  // Needed so that paths are relative, allowing VSCodes base href to work
  base: "./",
  mode: isDev ? "development" : "production",
  // svelteTesting tries to dynamically resolve vitest in its cleanup --
  // this doesn't work well with bazel, so we disable it here and manually
  // add the vitest cleanup in vitest-setup.ts
  plugins: [
    svelte() as Plugin<any>[],
    json5(),
    svelteTesting({ autoCleanup: false }) as Plugin<any>,
    svg({
      // bazel requires absolute paths for this to work
      includePaths: [`${__dirname}/src/design-system/icons/fontawesome/svgs`],
      svgoOptions: {
        multipass: true,
        plugins: [
          {
            name: "preset-default",
            // by default svgo removes the viewBox which prevents svg icons from scaling
            // not a good idea! https://github.com/svg/svgo/pull/1461
            params: { overrides: { removeViewBox: false } },
          },
          { name: "removeAttrs", params: { attrs: "(fill|stroke)" } },
          {
            /**
             * This adds a attribute which we select to automatically style icons
             */
            name: "addAttributesToSVGElement",
            params: {
              attributes: [{ "data-ds-icon": "fa" }],
            },
          },
        ],
      },
    }),
    inlineMonacoBootstrap(),
  ],
  resolve: {
    // These should match the aliases in //clients/tsconfig.json
    alias: {
      /* eslint-disable @typescript-eslint/naming-convention */
      "$common-webviews": resolve(__dirname),
      $vscode: resolve(__dirname, "..", "..", "vscode"),
      //storybook was complaining about this, I think it was confused where to look.
      diff: resolve(__dirname, "node_modules", "diff"),
      "@augment-internal/sidecar-libs": resolve(__dirname, "..", "..", "sidecar", "libs"),
      $clients: resolve(__dirname, "..", ".."),
      /* eslint-enable @typescript-eslint/naming-convention */
    },
  },
  html: {
    cspNonce: `nonce-${randomBytes(16).toString("base64")}`,
  },
  build: {
    // you can specify minifiy:'terser' here but for some reason it was not working
    // for clients/vscode/webviews, so we will keep the same pattern here as well, and
    // we just continue to use the terser plugin. (even with terser installed)
    minify: isDev ? false : undefined,
    rollupOptions: {
      // This is needed to ensure rollup can build the webview when run by bazel
      preserveSymlinks: true,
      input: {
        /* eslint-disable @typescript-eslint/naming-convention */
        "diff-view": "diff-view.html",
        autofix: "autofix.html",
        "main-panel": "main-panel.html",
        "next-edit-suggestions": "next-edit-suggestions.html",
        history: "history.html",
        "remote-agent-diff": "remote-agent-diff.html",
        "remote-agent-home": "remote-agent-home.html",
        preference: "preference.html",
        settings: "settings.html",
        rules: "rules.html",
        memories: "memories.html",
        index: "index.html",
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      plugins,
    },
  },
  server: {
    cors: {
      origin: "*",
    },
  },
  test: {
    // Used for setting up the global environment for tests (expects, for example)
    globals: true,
    environment: "jsdom",
    // This file runs before each test
    setupFiles: "./vitest-setup.ts",
    coverage: {
      reporter: ["lcov", "text", "html"],
      exclude: ["**/mocks/**", ...coverageConfigDefaults.exclude],
    },
  },
  optimizeDeps: {
    force: true, // Force re-optimization to include fromJson
    include: ["@bufbuild/protobuf"],
  },
});
